import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { logger } from '../utils/logger.js';
import { AuditLogger, AuditAction, AuditSeverity } from './auditLogger.js';
import { CacheService } from '../config/redis.js';

export enum UserRole {
  SUPER_ADMIN = 'SUPER_ADMIN',
  AGENCY_ADMIN = 'AGENCY_ADMIN',
  CLIENT_ADMIN = 'CLIENT_ADMIN',
  CONTENT_CREATOR = 'CONTENT_CREATOR',
  DESIGNER = 'DESIGNER',
  EDITOR = 'EDITOR',
  STRATEGIST = 'STRATEGIST',
  MANAGER = 'MANAGER',
  CLIENT_USER = 'CLIENT_USER'
}

export enum UserStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  SUSPENDED = 'SUSPENDED',
  PENDING_VERIFICATION = 'PENDING_VERIFICATION'
}

export interface UserPermissions {
  // Project permissions
  canCreateProjects: boolean;
  canEditProjects: boolean;
  canDeleteProjects: boolean;
  canViewAllProjects: boolean;

  // Content permissions
  canCreateContent: boolean;
  canEditContent: boolean;
  canDeleteContent: boolean;
  canPublishContent: boolean;
  canScheduleContent: boolean;

  // User management permissions
  canInviteUsers: boolean;
  canEditUsers: boolean;
  canDeleteUsers: boolean;
  canChangeUserRoles: boolean;

  // Analytics permissions
  canViewAnalytics: boolean;
  canExportAnalytics: boolean;
  canViewAdvancedAnalytics: boolean;

  // Organization permissions
  canEditOrganization: boolean;
  canManageBilling: boolean;
  canManageIntegrations: boolean;

  // System permissions
  canAccessAPI: boolean;
  canManageWebhooks: boolean;
  canViewAuditLogs: boolean;
}

export interface CreateUserRequest {
  email: string;
  firstName: string;
  lastName: string;
  role: UserRole;
  organizationId: string;
  password?: string;
  sendInvitation?: boolean;
  customPermissions?: Partial<UserPermissions>;
}

export interface UpdateUserRequest {
  firstName?: string;
  lastName?: string;
  role?: UserRole;
  status?: UserStatus;
  customPermissions?: Partial<UserPermissions>;
}

export class UserManagementService {
  private prisma: PrismaClient;
  private auditLogger: AuditLogger;

  constructor(prisma: PrismaClient, auditLogger: AuditLogger) {
    this.prisma = prisma;
    this.auditLogger = auditLogger;
  }

  /**
   * Create a new user
   */
  async createUser(
    request: CreateUserRequest,
    createdBy: string
  ): Promise<{ user: any; invitationToken?: string }> {
    try {
      // Check if user already exists
      const existingUser = await this.prisma.user.findUnique({
        where: { email: request.email }
      });

      if (existingUser) {
        throw new Error('User with this email already exists');
      }

      // Generate password if not provided
      let hashedPassword: string | undefined;
      let invitationToken: string | undefined;

      if (request.password) {
        hashedPassword = await bcrypt.hash(request.password, 12);
      } else if (request.sendInvitation) {
        // Generate invitation token
        invitationToken = jwt.sign(
          { email: request.email, organizationId: request.organizationId },
          process.env.JWT_SECRET!,
          { expiresIn: '7d' }
        );
      } else {
        // Generate temporary password
        const tempPassword = this.generateTemporaryPassword();
        hashedPassword = await bcrypt.hash(tempPassword, 12);
      }

      // Get role permissions
      const permissions = this.getRolePermissions(request.role);
      
      // Apply custom permissions if provided
      const finalPermissions = {
        ...permissions,
        ...request.customPermissions
      };

      // Create user
      const user = await this.prisma.user.create({
        data: {
          email: request.email,
          firstName: request.firstName,
          lastName: request.lastName,
          password: hashedPassword,
          role: request.role,
          organizationId: request.organizationId,
          status: request.sendInvitation ? UserStatus.PENDING_VERIFICATION : UserStatus.ACTIVE,
          permissions: finalPermissions,
          invitationToken,
          invitationExpiresAt: invitationToken ? new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) : null
        },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          role: true,
          status: true,
          createdAt: true
        }
      });

      // Log audit event
      await this.auditLogger.logUserManagement(
        AuditAction.USER_CREATED,
        createdBy,
        user.id,
        request.organizationId,
        {
          userEmail: request.email,
          userRole: request.role,
          sendInvitation: request.sendInvitation
        }
      );

      // Send invitation email if requested
      if (request.sendInvitation && invitationToken) {
        await this.sendInvitationEmail(request.email, invitationToken);
      }

      logger.info(`User created: ${user.email} by ${createdBy}`);

      return { user, invitationToken };
    } catch (error) {
      logger.error('Error creating user:', error);
      throw error;
    }
  }

  /**
   * Update user
   */
  async updateUser(
    userId: string,
    updates: UpdateUserRequest,
    updatedBy: string
  ): Promise<any> {
    try {
      const user = await this.prisma.user.findUnique({
        where: { id: userId },
        select: { id: true, email: true, role: true, organizationId: true }
      });

      if (!user) {
        throw new Error('User not found');
      }

      const updateData: any = {};

      if (updates.firstName) updateData.firstName = updates.firstName;
      if (updates.lastName) updateData.lastName = updates.lastName;
      if (updates.status) updateData.status = updates.status;

      // Handle role change
      if (updates.role && updates.role !== user.role) {
        updateData.role = updates.role;
        
        // Update permissions based on new role
        const newPermissions = this.getRolePermissions(updates.role);
        updateData.permissions = {
          ...newPermissions,
          ...updates.customPermissions
        };

        // Log role change
        await this.auditLogger.logUserManagement(
          AuditAction.USER_ROLE_CHANGED,
          updatedBy,
          userId,
          user.organizationId,
          {
            oldRole: user.role,
            newRole: updates.role
          }
        );
      } else if (updates.customPermissions) {
        // Update only custom permissions
        const currentUser = await this.prisma.user.findUnique({
          where: { id: userId },
          select: { permissions: true }
        });

        updateData.permissions = {
          ...(currentUser?.permissions as any),
          ...updates.customPermissions
        };
      }

      const updatedUser = await this.prisma.user.update({
        where: { id: userId },
        data: updateData,
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          role: true,
          status: true,
          permissions: true,
          updatedAt: true
        }
      });

      // Log audit event
      await this.auditLogger.logUserManagement(
        AuditAction.USER_UPDATED,
        updatedBy,
        userId,
        user.organizationId,
        { updates }
      );

      // Clear user cache
      await this.clearUserCache(userId);

      logger.info(`User updated: ${user.email} by ${updatedBy}`);

      return updatedUser;
    } catch (error) {
      logger.error('Error updating user:', error);
      throw error;
    }
  }

  /**
   * Suspend user
   */
  async suspendUser(userId: string, reason: string, suspendedBy: string): Promise<void> {
    try {
      const user = await this.prisma.user.findUnique({
        where: { id: userId },
        select: { email: true, organizationId: true }
      });

      if (!user) {
        throw new Error('User not found');
      }

      await this.prisma.user.update({
        where: { id: userId },
        data: {
          status: UserStatus.SUSPENDED,
          suspendedAt: new Date(),
          suspensionReason: reason
        }
      });

      // Invalidate all user sessions
      await this.invalidateUserSessions(userId);

      // Log audit event
      await this.auditLogger.logUserManagement(
        AuditAction.USER_SUSPENDED,
        suspendedBy,
        userId,
        user.organizationId,
        { reason }
      );

      logger.info(`User suspended: ${user.email} by ${suspendedBy}, reason: ${reason}`);
    } catch (error) {
      logger.error('Error suspending user:', error);
      throw error;
    }
  }

  /**
   * Activate user
   */
  async activateUser(userId: string, activatedBy: string): Promise<void> {
    try {
      const user = await this.prisma.user.findUnique({
        where: { id: userId },
        select: { email: true, organizationId: true }
      });

      if (!user) {
        throw new Error('User not found');
      }

      await this.prisma.user.update({
        where: { id: userId },
        data: {
          status: UserStatus.ACTIVE,
          suspendedAt: null,
          suspensionReason: null
        }
      });

      // Log audit event
      await this.auditLogger.logUserManagement(
        AuditAction.USER_ACTIVATED,
        activatedBy,
        userId,
        user.organizationId
      );

      logger.info(`User activated: ${user.email} by ${activatedBy}`);
    } catch (error) {
      logger.error('Error activating user:', error);
      throw error;
    }
  }

  /**
   * Delete user
   */
  async deleteUser(userId: string, deletedBy: string): Promise<void> {
    try {
      const user = await this.prisma.user.findUnique({
        where: { id: userId },
        select: { email: true, organizationId: true }
      });

      if (!user) {
        throw new Error('User not found');
      }

      // Soft delete - mark as deleted but keep data for audit purposes
      await this.prisma.user.update({
        where: { id: userId },
        data: {
          status: UserStatus.INACTIVE,
          deletedAt: new Date(),
          email: `deleted_${Date.now()}_${user.email}` // Prevent email conflicts
        }
      });

      // Invalidate all user sessions
      await this.invalidateUserSessions(userId);

      // Log audit event
      await this.auditLogger.logUserManagement(
        AuditAction.USER_DELETED,
        deletedBy,
        userId,
        user.organizationId
      );

      logger.info(`User deleted: ${user.email} by ${deletedBy}`);
    } catch (error) {
      logger.error('Error deleting user:', error);
      throw error;
    }
  }

  /**
   * Get role permissions
   */
  private getRolePermissions(role: UserRole): UserPermissions {
    const permissions: Record<UserRole, UserPermissions> = {
      [UserRole.SUPER_ADMIN]: {
        canCreateProjects: true,
        canEditProjects: true,
        canDeleteProjects: true,
        canViewAllProjects: true,
        canCreateContent: true,
        canEditContent: true,
        canDeleteContent: true,
        canPublishContent: true,
        canScheduleContent: true,
        canInviteUsers: true,
        canEditUsers: true,
        canDeleteUsers: true,
        canChangeUserRoles: true,
        canViewAnalytics: true,
        canExportAnalytics: true,
        canViewAdvancedAnalytics: true,
        canEditOrganization: true,
        canManageBilling: true,
        canManageIntegrations: true,
        canAccessAPI: true,
        canManageWebhooks: true,
        canViewAuditLogs: true
      },
      [UserRole.AGENCY_ADMIN]: {
        canCreateProjects: true,
        canEditProjects: true,
        canDeleteProjects: true,
        canViewAllProjects: true,
        canCreateContent: true,
        canEditContent: true,
        canDeleteContent: true,
        canPublishContent: true,
        canScheduleContent: true,
        canInviteUsers: true,
        canEditUsers: true,
        canDeleteUsers: true,
        canChangeUserRoles: true,
        canViewAnalytics: true,
        canExportAnalytics: true,
        canViewAdvancedAnalytics: true,
        canEditOrganization: true,
        canManageBilling: true,
        canManageIntegrations: true,
        canAccessAPI: true,
        canManageWebhooks: true,
        canViewAuditLogs: true
      },
      [UserRole.CLIENT_ADMIN]: {
        canCreateProjects: true,
        canEditProjects: true,
        canDeleteProjects: false,
        canViewAllProjects: true,
        canCreateContent: true,
        canEditContent: true,
        canDeleteContent: false,
        canPublishContent: true,
        canScheduleContent: true,
        canInviteUsers: true,
        canEditUsers: true,
        canDeleteUsers: false,
        canChangeUserRoles: false,
        canViewAnalytics: true,
        canExportAnalytics: true,
        canViewAdvancedAnalytics: false,
        canEditOrganization: false,
        canManageBilling: false,
        canManageIntegrations: false,
        canAccessAPI: false,
        canManageWebhooks: false,
        canViewAuditLogs: false
      },
      // Add other roles...
      [UserRole.CONTENT_CREATOR]: {
        canCreateProjects: false,
        canEditProjects: false,
        canDeleteProjects: false,
        canViewAllProjects: false,
        canCreateContent: true,
        canEditContent: true,
        canDeleteContent: false,
        canPublishContent: false,
        canScheduleContent: true,
        canInviteUsers: false,
        canEditUsers: false,
        canDeleteUsers: false,
        canChangeUserRoles: false,
        canViewAnalytics: false,
        canExportAnalytics: false,
        canViewAdvancedAnalytics: false,
        canEditOrganization: false,
        canManageBilling: false,
        canManageIntegrations: false,
        canAccessAPI: false,
        canManageWebhooks: false,
        canViewAuditLogs: false
      },
      // ... implement other roles
      [UserRole.DESIGNER]: {} as UserPermissions,
      [UserRole.EDITOR]: {} as UserPermissions,
      [UserRole.STRATEGIST]: {} as UserPermissions,
      [UserRole.MANAGER]: {} as UserPermissions,
      [UserRole.CLIENT_USER]: {} as UserPermissions
    };

    return permissions[role];
  }

  /**
   * Generate temporary password
   */
  private generateTemporaryPassword(): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
    let password = '';
    for (let i = 0; i < 12; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return password;
  }

  /**
   * Send invitation email
   */
  private async sendInvitationEmail(email: string, token: string): Promise<void> {
    // Implementation would depend on your email service
    logger.info(`Invitation email sent to: ${email}`);
  }

  /**
   * Clear user cache
   */
  private async clearUserCache(userId: string): Promise<void> {
    await CacheService.del(`user:${userId}`);
    await CacheService.del(`user:permissions:${userId}`);
  }

  /**
   * Invalidate user sessions
   */
  private async invalidateUserSessions(userId: string): Promise<void> {
    // This would invalidate all JWT tokens for the user
    // Implementation depends on your session management strategy
    await CacheService.del(`sessions:${userId}:*`);
  }
}
