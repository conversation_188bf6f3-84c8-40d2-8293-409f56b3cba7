import { apiClient } from './client'
import { Project, CreateProjectData, UpdateProjectData } from '../types/project'
import { ApiResponse } from '../types/auth'

export const projectsAPI = {
  // Get all projects
  getProjects: () => {
    return apiClient.get<ApiResponse<{ projects: Project[] }>>('/projects')
  },

  // Get a specific project
  getProject: (id: string) => {
    return apiClient.get<ApiResponse<{ project: Project }>>(`/projects/${id}`)
  },

  // Create a new project
  createProject: (data: CreateProjectData) => {
    return apiClient.post<ApiResponse<{ project: Project }>>('/projects', data)
  },

  // Update a project
  updateProject: (id: string, data: UpdateProjectData) => {
    return apiClient.put<ApiResponse<{ project: Project }>>(`/projects/${id}`, data)
  },

  // Delete a project
  deleteProject: (id: string) => {
    return apiClient.delete<ApiResponse<{ message: string }>>(`/projects/${id}`)
  },
}
