import OpenAI from 'openai';
import { PrismaClient } from '@prisma/client';
import { logger } from '../utils/logger.js';
import { CacheService } from '../config/redis.js';

export interface ContentGenerationRequest {
  type: 'post' | 'caption' | 'hashtags' | 'story' | 'article' | 'ad_copy';
  platform: 'instagram' | 'facebook' | 'twitter' | 'linkedin' | 'tiktok' | 'youtube';
  topic: string;
  tone: 'professional' | 'casual' | 'humorous' | 'inspirational' | 'educational' | 'promotional';
  length: 'short' | 'medium' | 'long';
  targetAudience?: string;
  keywords?: string[];
  brandVoice?: string;
  includeEmojis?: boolean;
  includeHashtags?: boolean;
  callToAction?: string;
  context?: string;
}

export interface GeneratedContent {
  content: string;
  hashtags?: string[];
  suggestions?: string[];
  confidence: number;
  metadata: {
    wordCount: number;
    characterCount: number;
    estimatedEngagement: number;
    seoScore: number;
  };
}

export interface ContentOptimizationRequest {
  content: string;
  platform: string;
  goals: ('engagement' | 'reach' | 'conversions' | 'brand_awareness')[];
  currentPerformance?: {
    likes: number;
    shares: number;
    comments: number;
    clicks: number;
  };
}

export class AIContentGenerator {
  private openai: OpenAI;
  private prisma: PrismaClient;

  constructor(prisma: PrismaClient) {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY
    });
    this.prisma = prisma;
  }

  /**
   * Generate content using AI
   */
  async generateContent(
    request: ContentGenerationRequest,
    userId: string,
    organizationId: string
  ): Promise<GeneratedContent> {
    try {
      // Check rate limits and usage
      await this.checkUsageLimits(organizationId);

      // Generate cache key
      const cacheKey = this.generateCacheKey(request);
      
      // Check cache first
      const cached = await CacheService.get(cacheKey);
      if (cached) {
        logger.debug('Returning cached AI content');
        return JSON.parse(cached);
      }

      // Build prompt based on request
      const prompt = this.buildPrompt(request);

      // Generate content using OpenAI
      const completion = await this.openai.chat.completions.create({
        model: 'gpt-4-turbo-preview',
        messages: [
          {
            role: 'system',
            content: this.getSystemPrompt(request.platform, request.type)
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: this.getMaxTokens(request.length),
        temperature: this.getTemperature(request.tone),
        presence_penalty: 0.1,
        frequency_penalty: 0.1
      });

      const generatedText = completion.choices[0]?.message?.content;
      if (!generatedText) {
        throw new Error('No content generated');
      }

      // Parse and structure the response
      const structuredContent = this.parseGeneratedContent(generatedText, request);

      // Analyze and score the content
      const analyzedContent = await this.analyzeContent(structuredContent, request);

      // Track usage
      await this.trackUsage(userId, organizationId, request.type);

      // Cache the result
      await CacheService.set(cacheKey, JSON.stringify(analyzedContent), 3600); // 1 hour

      logger.info(`AI content generated for user ${userId}, type: ${request.type}`);

      return analyzedContent;
    } catch (error) {
      logger.error('Error generating AI content:', error);
      throw new Error('Failed to generate content');
    }
  }

  /**
   * Optimize existing content
   */
  async optimizeContent(
    request: ContentOptimizationRequest,
    userId: string,
    organizationId: string
  ): Promise<{
    optimizedContent: string;
    improvements: string[];
    score: number;
    suggestions: string[];
  }> {
    try {
      await this.checkUsageLimits(organizationId);

      const prompt = this.buildOptimizationPrompt(request);

      const completion = await this.openai.chat.completions.create({
        model: 'gpt-4-turbo-preview',
        messages: [
          {
            role: 'system',
            content: 'You are a social media optimization expert. Analyze and improve content for better performance.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 1000,
        temperature: 0.3
      });

      const response = completion.choices[0]?.message?.content;
      if (!response) {
        throw new Error('No optimization generated');
      }

      const optimization = this.parseOptimizationResponse(response);

      await this.trackUsage(userId, organizationId, 'optimization');

      return optimization;
    } catch (error) {
      logger.error('Error optimizing content:', error);
      throw new Error('Failed to optimize content');
    }
  }

  /**
   * Generate hashtags for content
   */
  async generateHashtags(
    content: string,
    platform: string,
    count: number = 10
  ): Promise<string[]> {
    try {
      const prompt = `Generate ${count} relevant hashtags for this ${platform} content: "${content}". 
      Return only the hashtags, one per line, without the # symbol.`;

      const completion = await this.openai.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: 'You are a hashtag expert. Generate relevant, trending hashtags for social media content.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 200,
        temperature: 0.5
      });

      const response = completion.choices[0]?.message?.content;
      if (!response) {
        return [];
      }

      return response
        .split('\n')
        .map(tag => tag.trim().replace(/^#/, ''))
        .filter(tag => tag.length > 0)
        .slice(0, count);
    } catch (error) {
      logger.error('Error generating hashtags:', error);
      return [];
    }
  }

  /**
   * Analyze content sentiment and engagement potential
   */
  async analyzeContentSentiment(content: string): Promise<{
    sentiment: 'positive' | 'negative' | 'neutral';
    confidence: number;
    emotions: string[];
    engagementPotential: number;
  }> {
    try {
      const prompt = `Analyze the sentiment and engagement potential of this content: "${content}"
      
      Provide analysis in this JSON format:
      {
        "sentiment": "positive|negative|neutral",
        "confidence": 0.0-1.0,
        "emotions": ["emotion1", "emotion2"],
        "engagementPotential": 0.0-1.0
      }`;

      const completion = await this.openai.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: 'You are a sentiment analysis expert. Analyze content for sentiment and engagement potential.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 300,
        temperature: 0.1
      });

      const response = completion.choices[0]?.message?.content;
      if (!response) {
        throw new Error('No analysis generated');
      }

      return JSON.parse(response);
    } catch (error) {
      logger.error('Error analyzing sentiment:', error);
      return {
        sentiment: 'neutral',
        confidence: 0.5,
        emotions: [],
        engagementPotential: 0.5
      };
    }
  }

  /**
   * Build prompt based on request parameters
   */
  private buildPrompt(request: ContentGenerationRequest): string {
    let prompt = `Create a ${request.type} for ${request.platform} about "${request.topic}".

Requirements:
- Tone: ${request.tone}
- Length: ${request.length}
- Platform: ${request.platform}`;

    if (request.targetAudience) {
      prompt += `\n- Target audience: ${request.targetAudience}`;
    }

    if (request.keywords && request.keywords.length > 0) {
      prompt += `\n- Include these keywords: ${request.keywords.join(', ')}`;
    }

    if (request.brandVoice) {
      prompt += `\n- Brand voice: ${request.brandVoice}`;
    }

    if (request.includeEmojis) {
      prompt += `\n- Include relevant emojis`;
    }

    if (request.includeHashtags) {
      prompt += `\n- Include relevant hashtags`;
    }

    if (request.callToAction) {
      prompt += `\n- Call to action: ${request.callToAction}`;
    }

    if (request.context) {
      prompt += `\n- Additional context: ${request.context}`;
    }

    prompt += `\n\nGenerate engaging, platform-optimized content that follows best practices for ${request.platform}.`;

    return prompt;
  }

  /**
   * Get system prompt for specific platform and content type
   */
  private getSystemPrompt(platform: string, type: string): string {
    const platformGuidelines = {
      instagram: 'Instagram content should be visually appealing, use relevant hashtags, and encourage engagement. Keep captions concise but engaging.',
      facebook: 'Facebook content should encourage conversation, use storytelling, and include clear calls to action.',
      twitter: 'Twitter content must be concise (280 characters), use trending hashtags, and be timely and relevant.',
      linkedin: 'LinkedIn content should be professional, provide value, and establish thought leadership.',
      tiktok: 'TikTok content should be trendy, entertaining, and use popular sounds/effects when applicable.',
      youtube: 'YouTube content should have compelling titles, detailed descriptions, and clear value propositions.'
    };

    return `You are an expert social media content creator specializing in ${platform}. 
    ${platformGuidelines[platform as keyof typeof platformGuidelines] || ''}
    
    Create high-quality, engaging ${type} content that follows platform best practices and maximizes engagement.
    Always consider the platform's algorithm preferences and user behavior patterns.`;
  }

  /**
   * Parse generated content into structured format
   */
  private parseGeneratedContent(content: string, request: ContentGenerationRequest): GeneratedContent {
    // Extract hashtags if present
    const hashtagRegex = /#[\w]+/g;
    const hashtags = content.match(hashtagRegex) || [];

    // Remove hashtags from main content for analysis
    const mainContent = content.replace(hashtagRegex, '').trim();

    return {
      content: mainContent,
      hashtags: hashtags.map(tag => tag.replace('#', '')),
      suggestions: [],
      confidence: 0.8, // Will be calculated in analyzeContent
      metadata: {
        wordCount: mainContent.split(' ').length,
        characterCount: mainContent.length,
        estimatedEngagement: 0, // Will be calculated
        seoScore: 0 // Will be calculated
      }
    };
  }

  /**
   * Analyze content quality and engagement potential
   */
  private async analyzeContent(
    content: GeneratedContent,
    request: ContentGenerationRequest
  ): Promise<GeneratedContent> {
    // Calculate engagement potential based on various factors
    let engagementScore = 0.5;

    // Length optimization for platform
    const optimalLength = this.getOptimalLength(request.platform, request.type);
    const lengthScore = this.calculateLengthScore(content.metadata.characterCount, optimalLength);
    engagementScore += lengthScore * 0.2;

    // Emoji usage (if requested)
    if (request.includeEmojis) {
      const emojiCount = (content.content.match(/[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]/gu) || []).length;
      engagementScore += Math.min(emojiCount * 0.05, 0.15);
    }

    // Hashtag optimization
    if (content.hashtags && content.hashtags.length > 0) {
      const hashtagScore = Math.min(content.hashtags.length * 0.02, 0.1);
      engagementScore += hashtagScore;
    }

    // Update metadata
    content.metadata.estimatedEngagement = Math.min(engagementScore, 1.0);
    content.confidence = engagementScore;

    return content;
  }

  /**
   * Helper methods
   */
  private generateCacheKey(request: ContentGenerationRequest): string {
    const key = `ai_content:${request.platform}:${request.type}:${request.topic}:${request.tone}:${request.length}`;
    return key.replace(/[^a-zA-Z0-9:]/g, '_');
  }

  private getMaxTokens(length: string): number {
    switch (length) {
      case 'short': return 150;
      case 'medium': return 300;
      case 'long': return 500;
      default: return 300;
    }
  }

  private getTemperature(tone: string): number {
    switch (tone) {
      case 'professional': return 0.3;
      case 'casual': return 0.7;
      case 'humorous': return 0.8;
      case 'inspirational': return 0.6;
      case 'educational': return 0.4;
      case 'promotional': return 0.5;
      default: return 0.6;
    }
  }

  private getOptimalLength(platform: string, type: string): { min: number; max: number } {
    const lengths = {
      instagram: { post: { min: 125, max: 300 }, story: { min: 50, max: 100 } },
      facebook: { post: { min: 100, max: 250 }, ad_copy: { min: 90, max: 125 } },
      twitter: { post: { min: 71, max: 100 } },
      linkedin: { post: { min: 150, max: 300 }, article: { min: 1000, max: 3000 } },
      tiktok: { caption: { min: 100, max: 150 } },
      youtube: { description: { min: 200, max: 1000 } }
    };

    return lengths[platform as keyof typeof lengths]?.[type as keyof any] || { min: 100, max: 300 };
  }

  private calculateLengthScore(actual: number, optimal: { min: number; max: number }): number {
    if (actual >= optimal.min && actual <= optimal.max) {
      return 1.0;
    }
    
    const distance = actual < optimal.min 
      ? optimal.min - actual 
      : actual - optimal.max;
    
    return Math.max(0, 1 - (distance / optimal.max));
  }

  private async checkUsageLimits(organizationId: string): Promise<void> {
    // Implementation would check organization's AI usage limits
    // For now, we'll just log
    logger.debug(`Checking AI usage limits for organization: ${organizationId}`);
  }

  private async trackUsage(userId: string, organizationId: string, type: string): Promise<void> {
    try {
      await this.prisma.aiUsage.create({
        data: {
          userId,
          organizationId,
          type,
          timestamp: new Date(),
          tokens: 100 // Estimate, would be actual from OpenAI response
        }
      });
    } catch (error) {
      logger.error('Error tracking AI usage:', error);
    }
  }

  private buildOptimizationPrompt(request: ContentOptimizationRequest): string {
    return `Optimize this ${request.platform} content for ${request.goals.join(', ')}:

"${request.content}"

Provide:
1. Optimized version of the content
2. List of specific improvements made
3. Overall score (0-100)
4. Additional suggestions

Format as JSON:
{
  "optimizedContent": "...",
  "improvements": ["improvement1", "improvement2"],
  "score": 85,
  "suggestions": ["suggestion1", "suggestion2"]
}`;
  }

  private parseOptimizationResponse(response: string): any {
    try {
      return JSON.parse(response);
    } catch (error) {
      // Fallback parsing if JSON is malformed
      return {
        optimizedContent: response,
        improvements: [],
        score: 70,
        suggestions: []
      };
    }
  }
}
