import React from 'react';
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Progress } from '../ui/progress';
import { TrendingUp, TrendingDown, Users, Eye, Heart } from 'lucide-react';

interface PlatformMetrics {
  platform: string;
  posts: number;
  engagement: number;
  reach: number;
  impressions: number;
  engagementRate: number;
  followerCount: number;
  followerGrowth: number;
  bestPostingTimes: { hour: number; engagementRate: number }[];
}

interface PlatformComparisonProps {
  data: PlatformMetrics[];
}

export const PlatformComparison: React.FC<PlatformComparisonProps> = ({ data }) => {
  const getPlatformIcon = (platform: string) => {
    const icons: Record<string, string> = {
      INSTAGRAM: '📷',
      FACEBOOK: '👥',
      TWITTER: '🐦',
      LINKEDIN: '💼',
      TIKTOK: '🎵',
      YOUTUBE: '📺',
      PINTEREST: '📌'
    };
    return icons[platform] || '📱';
  };

  const getPlatformColor = (platform: string) => {
    const colors: Record<string, string> = {
      INSTAGRAM: '#E4405F',
      FACEBOOK: '#1877F2',
      TWITTER: '#1DA1F2',
      LINKEDIN: '#0A66C2',
      TIKTOK: '#000000',
      YOUTUBE: '#FF0000',
      PINTEREST: '#BD081C'
    };
    return colors[platform] || '#6B7280';
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toString();
  };

  const getBestTime = (times: { hour: number; engagementRate: number }[]) => {
    if (times.length === 0) return 'No data';
    const best = times.reduce((prev, current) => 
      prev.engagementRate > current.engagementRate ? prev : current
    );
    const period = best.hour >= 12 ? 'PM' : 'AM';
    const displayHour = best.hour === 0 ? 12 : best.hour > 12 ? best.hour - 12 : best.hour;
    return `${displayHour}:00 ${period}`;
  };

  const maxReach = Math.max(...data.map(p => p.reach));
  const maxEngagement = Math.max(...data.map(p => p.engagement));

  if (data.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Platform Comparison</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <div className="text-4xl mb-4">📊</div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No platform data available</h3>
            <p className="text-gray-600">Connect social media accounts to see platform comparison.</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900">Platform Comparison</h2>
        <Badge variant="outline">{data.length} platforms</Badge>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {data.map((platform) => (
          <Card key={platform.platform} className="relative overflow-hidden">
            <div 
              className="absolute top-0 left-0 w-full h-1"
              style={{ backgroundColor: getPlatformColor(platform.platform) }}
            />
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div
                    className="w-10 h-10 rounded-full flex items-center justify-center text-white text-lg"
                    style={{ backgroundColor: getPlatformColor(platform.platform) }}
                  >
                    {getPlatformIcon(platform.platform)}
                  </div>
                  <div>
                    <CardTitle className="text-lg">{platform.platform}</CardTitle>
                    <p className="text-sm text-gray-600">{platform.posts} posts</p>
                  </div>
                </div>
                <Badge 
                  variant={platform.engagementRate > 0.03 ? "default" : "secondary"}
                  className="text-xs"
                >
                  {(platform.engagementRate * 100).toFixed(1)}%
                </Badge>
              </div>
            </CardHeader>
            
            <CardContent className="space-y-4">
              {/* Key Metrics */}
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-3 bg-blue-50 rounded-lg">
                  <div className="flex items-center justify-center mb-1">
                    <Eye className="w-4 h-4 text-blue-600 mr-1" />
                    <span className="text-sm font-medium text-blue-700">Reach</span>
                  </div>
                  <p className="text-lg font-bold text-blue-900">
                    {formatNumber(platform.reach)}
                  </p>
                </div>
                <div className="text-center p-3 bg-red-50 rounded-lg">
                  <div className="flex items-center justify-center mb-1">
                    <Heart className="w-4 h-4 text-red-600 mr-1" />
                    <span className="text-sm font-medium text-red-700">Engagement</span>
                  </div>
                  <p className="text-lg font-bold text-red-900">
                    {formatNumber(platform.engagement)}
                  </p>
                </div>
              </div>

              {/* Performance Bars */}
              <div className="space-y-3">
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span className="text-gray-600">Reach Performance</span>
                    <span className="font-medium">
                      {((platform.reach / maxReach) * 100).toFixed(0)}%
                    </span>
                  </div>
                  <Progress 
                    value={(platform.reach / maxReach) * 100} 
                    className="h-2"
                  />
                </div>
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span className="text-gray-600">Engagement Performance</span>
                    <span className="font-medium">
                      {((platform.engagement / maxEngagement) * 100).toFixed(0)}%
                    </span>
                  </div>
                  <Progress 
                    value={(platform.engagement / maxEngagement) * 100} 
                    className="h-2"
                  />
                </div>
              </div>

              {/* Followers */}
              <div className="border-t pt-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Users className="w-4 h-4 text-gray-600" />
                    <span className="text-sm font-medium text-gray-700">Followers</span>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-semibold">{formatNumber(platform.followerCount)}</p>
                    <div className={`flex items-center text-xs ${
                      platform.followerGrowth >= 0 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {platform.followerGrowth >= 0 ? (
                        <TrendingUp className="w-3 h-3 mr-1" />
                      ) : (
                        <TrendingDown className="w-3 h-3 mr-1" />
                      )}
                      {Math.abs(platform.followerGrowth).toFixed(1)}%
                    </div>
                  </div>
                </div>
              </div>

              {/* Best Posting Time */}
              <div className="border-t pt-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Best Time</span>
                  <span className="text-sm font-medium">
                    {getBestTime(platform.bestPostingTimes)}
                  </span>
                </div>
              </div>

              {/* Impressions */}
              <div className="text-center text-xs text-gray-500">
                {formatNumber(platform.impressions)} impressions
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Summary Comparison */}
      <Card>
        <CardHeader>
          <CardTitle>Platform Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-2">Platform</th>
                  <th className="text-right py-2">Posts</th>
                  <th className="text-right py-2">Reach</th>
                  <th className="text-right py-2">Engagement</th>
                  <th className="text-right py-2">Rate</th>
                  <th className="text-right py-2">Followers</th>
                </tr>
              </thead>
              <tbody>
                {data.map((platform) => (
                  <tr key={platform.platform} className="border-b">
                    <td className="py-2">
                      <div className="flex items-center space-x-2">
                        <span>{getPlatformIcon(platform.platform)}</span>
                        <span className="font-medium">{platform.platform}</span>
                      </div>
                    </td>
                    <td className="text-right py-2">{platform.posts}</td>
                    <td className="text-right py-2">{formatNumber(platform.reach)}</td>
                    <td className="text-right py-2">{formatNumber(platform.engagement)}</td>
                    <td className="text-right py-2">{(platform.engagementRate * 100).toFixed(1)}%</td>
                    <td className="text-right py-2">{formatNumber(platform.followerCount)}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default PlatformComparison;
