import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { 
  CreditCard, 
  Plus, 
  Trash2, 
  Star,
  Shield,
  AlertCircle
} from 'lucide-react';

// Mock payment methods data
const MOCK_PAYMENT_METHODS = [
  {
    id: 'pm_1',
    type: 'card',
    last4: '4242',
    brand: 'visa',
    expiryMonth: 12,
    expiryYear: 2025,
    isDefault: true
  },
  {
    id: 'pm_2',
    type: 'card',
    last4: '0005',
    brand: 'mastercard',
    expiryMonth: 8,
    expiryYear: 2024,
    isDefault: false
  }
];

export const PaymentMethods: React.FC = () => {
  const [paymentMethods, setPaymentMethods] = useState(MOCK_PAYMENT_METHODS);
  const [isAdding, setIsAdding] = useState(false);

  const getBrandIcon = (brand: string) => {
    const icons: Record<string, string> = {
      visa: '💳',
      mastercard: '💳',
      amex: '💳',
      discover: '💳'
    };
    return icons[brand] || '💳';
  };

  const getBrandColor = (brand: string) => {
    const colors: Record<string, string> = {
      visa: 'bg-blue-500',
      mastercard: 'bg-red-500',
      amex: 'bg-green-500',
      discover: 'bg-orange-500'
    };
    return colors[brand] || 'bg-gray-500';
  };

  const handleSetDefault = (methodId: string) => {
    setPaymentMethods(methods =>
      methods.map(method => ({
        ...method,
        isDefault: method.id === methodId
      }))
    );
  };

  const handleRemove = (methodId: string) => {
    setPaymentMethods(methods =>
      methods.filter(method => method.id !== methodId)
    );
  };

  const handleAddPaymentMethod = () => {
    setIsAdding(true);
    // In a real app, this would open Stripe Elements or similar
    setTimeout(() => {
      const newMethod = {
        id: `pm_${Date.now()}`,
        type: 'card' as const,
        last4: '1234',
        brand: 'visa',
        expiryMonth: 6,
        expiryYear: 2026,
        isDefault: paymentMethods.length === 0
      };
      setPaymentMethods(methods => [...methods, newMethod]);
      setIsAdding(false);
    }, 2000);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Payment Methods</h2>
          <p className="text-gray-600 mt-1">
            Manage your payment methods and billing information
          </p>
        </div>
        <Button onClick={handleAddPaymentMethod} disabled={isAdding}>
          {isAdding ? (
            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
          ) : (
            <Plus className="w-4 h-4 mr-2" />
          )}
          Add Payment Method
        </Button>
      </div>

      {/* Security Notice */}
      <Card className="border-blue-200 bg-blue-50">
        <CardContent className="p-4">
          <div className="flex items-center space-x-3">
            <Shield className="w-5 h-5 text-blue-600" />
            <div>
              <h4 className="font-medium text-blue-800">Secure Payment Processing</h4>
              <p className="text-sm text-blue-700">
                All payment information is encrypted and processed securely through Stripe. 
                We never store your full card details.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Payment Methods List */}
      <div className="space-y-4">
        {paymentMethods.map((method) => (
          <Card key={method.id} className={`${method.isDefault ? 'ring-2 ring-blue-500' : ''}`}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className={`w-12 h-8 ${getBrandColor(method.brand)} rounded flex items-center justify-center text-white text-lg`}>
                    {getBrandIcon(method.brand)}
                  </div>
                  <div>
                    <div className="flex items-center space-x-2">
                      <h4 className="font-medium text-gray-900 capitalize">
                        {method.brand} •••• {method.last4}
                      </h4>
                      {method.isDefault && (
                        <Badge variant="default" className="text-xs">
                          <Star className="w-3 h-3 mr-1" />
                          Default
                        </Badge>
                      )}
                    </div>
                    <p className="text-sm text-gray-600">
                      Expires {method.expiryMonth.toString().padStart(2, '0')}/{method.expiryYear}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  {!method.isDefault && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleSetDefault(method.id)}
                    >
                      Set as Default
                    </Button>
                  )}
                  <Button
                    variant="outline"
                    size="sm"
                    className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    onClick={() => handleRemove(method.id)}
                    disabled={method.isDefault && paymentMethods.length === 1}
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Empty State */}
      {paymentMethods.length === 0 && !isAdding && (
        <Card>
          <CardContent className="text-center py-12">
            <div className="space-y-4">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto">
                <CreditCard className="w-8 h-8 text-gray-400" />
              </div>
              <div>
                <h3 className="text-lg font-medium text-gray-900">No payment methods</h3>
                <p className="text-gray-600 mt-1">
                  Add a payment method to manage your subscription
                </p>
              </div>
              <Button onClick={handleAddPaymentMethod}>
                <Plus className="w-4 h-4 mr-2" />
                Add Your First Payment Method
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Billing Information */}
      <Card>
        <CardHeader>
          <CardTitle>Billing Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Billing Email
              </label>
              <p className="text-sm text-gray-900"><EMAIL></p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Tax ID
              </label>
              <p className="text-sm text-gray-900">Not provided</p>
            </div>
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Billing Address
              </label>
              <div className="text-sm text-gray-900 space-y-1">
                <p>123 Business Street</p>
                <p>San Francisco, CA 94105</p>
                <p>United States</p>
              </div>
            </div>
          </div>
          <div className="mt-6">
            <Button variant="outline">
              Update Billing Information
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Payment Security */}
      <Card>
        <CardHeader>
          <CardTitle>Payment Security</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-start space-x-3">
              <Shield className="w-5 h-5 text-green-500 mt-0.5" />
              <div>
                <h4 className="font-medium text-gray-900">PCI DSS Compliant</h4>
                <p className="text-sm text-gray-600">
                  Our payment processing meets the highest security standards
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <Shield className="w-5 h-5 text-green-500 mt-0.5" />
              <div>
                <h4 className="font-medium text-gray-900">256-bit SSL Encryption</h4>
                <p className="text-sm text-gray-600">
                  All data is encrypted in transit and at rest
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <Shield className="w-5 h-5 text-green-500 mt-0.5" />
              <div>
                <h4 className="font-medium text-gray-900">Fraud Protection</h4>
                <p className="text-sm text-gray-600">
                  Advanced fraud detection protects your account
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Important Notice */}
      <Card className="border-yellow-200 bg-yellow-50">
        <CardContent className="p-4">
          <div className="flex items-start space-x-3">
            <AlertCircle className="w-5 h-5 text-yellow-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-yellow-800">Important</h4>
              <p className="text-sm text-yellow-700">
                If your default payment method fails, we'll attempt to charge your other 
                payment methods. Update your payment information to avoid service interruption.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default PaymentMethods;
