import { PrismaClient } from '@prisma/client';
import { logger } from '../utils/logger.js';
import { CacheService } from '../config/redis.js';

export interface ContentRecommendation {
  type: 'content_idea' | 'posting_time' | 'hashtag' | 'platform' | 'audience_segment';
  title: string;
  description: string;
  confidence: number;
  reasoning: string;
  data: any;
  priority: 'low' | 'medium' | 'high';
  category: string;
}

export interface UserBehaviorData {
  userId: string;
  organizationId: string;
  contentInteractions: {
    likes: number;
    shares: number;
    comments: number;
    clicks: number;
    contentType: string;
    platform: string;
    timestamp: Date;
  }[];
  postingPatterns: {
    preferredTimes: string[];
    preferredDays: string[];
    frequency: number;
  };
  contentPreferences: {
    topics: string[];
    tones: string[];
    formats: string[];
  };
}

export interface PerformanceMetrics {
  contentId: string;
  platform: string;
  engagementRate: number;
  reach: number;
  impressions: number;
  clicks: number;
  conversions: number;
  sentiment: number;
  timestamp: Date;
}

export class RecommendationEngine {
  private prisma: PrismaClient;
  private modelWeights: Map<string, number>;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
    this.modelWeights = new Map([
      ['engagement_rate', 0.3],
      ['reach', 0.2],
      ['timing', 0.15],
      ['content_type', 0.15],
      ['hashtag_performance', 0.1],
      ['audience_match', 0.1]
    ]);
  }

  /**
   * Generate personalized recommendations for a user
   */
  async generateRecommendations(
    userId: string,
    organizationId: string,
    limit: number = 10
  ): Promise<ContentRecommendation[]> {
    try {
      // Check cache first
      const cacheKey = `recommendations:${userId}:${organizationId}`;
      const cached = await CacheService.get(cacheKey);
      if (cached) {
        return JSON.parse(cached);
      }

      // Gather user behavior data
      const behaviorData = await this.getUserBehaviorData(userId, organizationId);
      
      // Get historical performance data
      const performanceData = await this.getPerformanceData(organizationId);

      // Generate different types of recommendations
      const recommendations: ContentRecommendation[] = [];

      // Content idea recommendations
      const contentIdeas = await this.generateContentIdeas(behaviorData, performanceData);
      recommendations.push(...contentIdeas);

      // Optimal posting time recommendations
      const timingRecs = await this.generateTimingRecommendations(behaviorData, performanceData);
      recommendations.push(...timingRecs);

      // Hashtag recommendations
      const hashtagRecs = await this.generateHashtagRecommendations(behaviorData, performanceData);
      recommendations.push(...hashtagRecs);

      // Platform optimization recommendations
      const platformRecs = await this.generatePlatformRecommendations(behaviorData, performanceData);
      recommendations.push(...platformRecs);

      // Audience targeting recommendations
      const audienceRecs = await this.generateAudienceRecommendations(behaviorData, performanceData);
      recommendations.push(...audienceRecs);

      // Sort by confidence and priority
      const sortedRecommendations = recommendations
        .sort((a, b) => {
          const priorityWeight = { high: 3, medium: 2, low: 1 };
          const priorityDiff = priorityWeight[b.priority] - priorityWeight[a.priority];
          if (priorityDiff !== 0) return priorityDiff;
          return b.confidence - a.confidence;
        })
        .slice(0, limit);

      // Cache recommendations for 1 hour
      await CacheService.set(cacheKey, JSON.stringify(sortedRecommendations), 3600);

      logger.info(`Generated ${sortedRecommendations.length} recommendations for user ${userId}`);

      return sortedRecommendations;
    } catch (error) {
      logger.error('Error generating recommendations:', error);
      return [];
    }
  }

  /**
   * Analyze content performance and update ML models
   */
  async analyzeContentPerformance(contentId: string): Promise<void> {
    try {
      const content = await this.prisma.contentItem.findUnique({
        where: { id: contentId },
        include: {
          analytics: true,
          project: {
            include: { organization: true }
          }
        }
      });

      if (!content) return;

      // Calculate performance metrics
      const metrics = this.calculatePerformanceMetrics(content.analytics);

      // Update user behavior patterns
      await this.updateUserBehaviorPatterns(content.createdBy, metrics);

      // Update content type performance data
      await this.updateContentTypePerformance(content.type, content.platforms, metrics);

      // Update hashtag performance
      if (content.hashtags) {
        await this.updateHashtagPerformance(content.hashtags as string[], metrics);
      }

      // Update timing performance
      await this.updateTimingPerformance(content.scheduledAt || content.createdAt, metrics);

      logger.debug(`Analyzed performance for content ${contentId}`);
    } catch (error) {
      logger.error('Error analyzing content performance:', error);
    }
  }

  /**
   * Get trending topics and content ideas
   */
  async getTrendingTopics(organizationId: string): Promise<{
    topics: string[];
    hashtags: string[];
    contentTypes: string[];
  }> {
    try {
      const cacheKey = `trending:${organizationId}`;
      const cached = await CacheService.get(cacheKey);
      if (cached) {
        return JSON.parse(cached);
      }

      // Analyze recent high-performing content
      const recentContent = await this.prisma.contentItem.findMany({
        where: {
          project: { organizationId },
          createdAt: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
          }
        },
        include: { analytics: true },
        orderBy: { createdAt: 'desc' },
        take: 100
      });

      // Extract trending topics using NLP-like analysis
      const topicFrequency = new Map<string, number>();
      const hashtagFrequency = new Map<string, number>();
      const typeFrequency = new Map<string, number>();

      recentContent.forEach(content => {
        // Analyze content for topics (simplified keyword extraction)
        const keywords = this.extractKeywords(content.content as any);
        keywords.forEach(keyword => {
          topicFrequency.set(keyword, (topicFrequency.get(keyword) || 0) + 1);
        });

        // Count hashtags
        if (content.hashtags) {
          (content.hashtags as string[]).forEach(hashtag => {
            hashtagFrequency.set(hashtag, (hashtagFrequency.get(hashtag) || 0) + 1);
          });
        }

        // Count content types
        typeFrequency.set(content.type, (typeFrequency.get(content.type) || 0) + 1);
      });

      const trending = {
        topics: Array.from(topicFrequency.entries())
          .sort((a, b) => b[1] - a[1])
          .slice(0, 10)
          .map(([topic]) => topic),
        hashtags: Array.from(hashtagFrequency.entries())
          .sort((a, b) => b[1] - a[1])
          .slice(0, 15)
          .map(([hashtag]) => hashtag),
        contentTypes: Array.from(typeFrequency.entries())
          .sort((a, b) => b[1] - a[1])
          .slice(0, 5)
          .map(([type]) => type)
      };

      // Cache for 6 hours
      await CacheService.set(cacheKey, JSON.stringify(trending), 21600);

      return trending;
    } catch (error) {
      logger.error('Error getting trending topics:', error);
      return { topics: [], hashtags: [], contentTypes: [] };
    }
  }

  /**
   * Predict optimal posting times for user
   */
  async predictOptimalPostingTimes(
    userId: string,
    organizationId: string,
    platform: string
  ): Promise<{
    times: string[];
    days: string[];
    confidence: number;
  }> {
    try {
      // Get historical posting data and performance
      const historicalData = await this.prisma.contentItem.findMany({
        where: {
          createdBy: userId,
          platforms: { has: platform },
          scheduledAt: { not: null }
        },
        include: { analytics: true },
        orderBy: { scheduledAt: 'desc' },
        take: 100
      });

      if (historicalData.length < 10) {
        // Not enough data, return general best practices
        return this.getDefaultOptimalTimes(platform);
      }

      // Analyze performance by time and day
      const timePerformance = new Map<string, number[]>();
      const dayPerformance = new Map<string, number[]>();

      historicalData.forEach(content => {
        if (!content.scheduledAt) return;

        const hour = content.scheduledAt.getHours();
        const day = content.scheduledAt.toLocaleDateString('en-US', { weekday: 'long' });
        
        const engagementRate = this.calculateEngagementRate(content.analytics);

        // Group by hour
        const hourKey = `${hour}:00`;
        if (!timePerformance.has(hourKey)) {
          timePerformance.set(hourKey, []);
        }
        timePerformance.get(hourKey)!.push(engagementRate);

        // Group by day
        if (!dayPerformance.has(day)) {
          dayPerformance.set(day, []);
        }
        dayPerformance.get(day)!.push(engagementRate);
      });

      // Calculate average performance for each time/day
      const avgTimePerformance = new Map<string, number>();
      timePerformance.forEach((rates, time) => {
        const avg = rates.reduce((sum, rate) => sum + rate, 0) / rates.length;
        avgTimePerformance.set(time, avg);
      });

      const avgDayPerformance = new Map<string, number>();
      dayPerformance.forEach((rates, day) => {
        const avg = rates.reduce((sum, rate) => sum + rate, 0) / rates.length;
        avgDayPerformance.set(day, avg);
      });

      // Get top performing times and days
      const topTimes = Array.from(avgTimePerformance.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, 3)
        .map(([time]) => time);

      const topDays = Array.from(avgDayPerformance.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, 3)
        .map(([day]) => day);

      const confidence = Math.min(historicalData.length / 50, 1); // Higher confidence with more data

      return {
        times: topTimes,
        days: topDays,
        confidence
      };
    } catch (error) {
      logger.error('Error predicting optimal posting times:', error);
      return this.getDefaultOptimalTimes(platform);
    }
  }

  /**
   * Generate content ideas based on performance data
   */
  private async generateContentIdeas(
    behaviorData: UserBehaviorData,
    performanceData: PerformanceMetrics[]
  ): Promise<ContentRecommendation[]> {
    const ideas: ContentRecommendation[] = [];

    // Analyze top-performing content types
    const typePerformance = new Map<string, number>();
    performanceData.forEach(metric => {
      const key = `${metric.platform}_content`;
      typePerformance.set(key, (typePerformance.get(key) || 0) + metric.engagementRate);
    });

    // Generate recommendations based on top performers
    const topTypes = Array.from(typePerformance.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 3);

    topTypes.forEach(([type, performance], index) => {
      ideas.push({
        type: 'content_idea',
        title: `Create more ${type.replace('_', ' ')} content`,
        description: `This content type has shown ${(performance * 100).toFixed(1)}% higher engagement`,
        confidence: Math.max(0.6, performance / 100),
        reasoning: `Based on analysis of your recent content performance`,
        data: { contentType: type, performance },
        priority: index === 0 ? 'high' : 'medium',
        category: 'content_strategy'
      });
    });

    return ideas;
  }

  /**
   * Generate timing recommendations
   */
  private async generateTimingRecommendations(
    behaviorData: UserBehaviorData,
    performanceData: PerformanceMetrics[]
  ): Promise<ContentRecommendation[]> {
    const recommendations: ContentRecommendation[] = [];

    // Analyze posting patterns vs performance
    if (behaviorData.postingPatterns.preferredTimes.length > 0) {
      recommendations.push({
        type: 'posting_time',
        title: 'Optimize your posting schedule',
        description: `Post during ${behaviorData.postingPatterns.preferredTimes.join(', ')} for better engagement`,
        confidence: 0.75,
        reasoning: 'Based on your historical posting performance',
        data: { 
          times: behaviorData.postingPatterns.preferredTimes,
          days: behaviorData.postingPatterns.preferredDays
        },
        priority: 'high',
        category: 'timing'
      });
    }

    return recommendations;
  }

  /**
   * Helper methods
   */
  private async getUserBehaviorData(userId: string, organizationId: string): Promise<UserBehaviorData> {
    // This would analyze user's historical data
    // For now, return mock data structure
    return {
      userId,
      organizationId,
      contentInteractions: [],
      postingPatterns: {
        preferredTimes: ['09:00', '15:00', '19:00'],
        preferredDays: ['Monday', 'Wednesday', 'Friday'],
        frequency: 5
      },
      contentPreferences: {
        topics: ['technology', 'business', 'lifestyle'],
        tones: ['professional', 'casual'],
        formats: ['image', 'video', 'carousel']
      }
    };
  }

  private async getPerformanceData(organizationId: string): Promise<PerformanceMetrics[]> {
    // This would fetch and analyze performance data
    // For now, return empty array
    return [];
  }

  private calculatePerformanceMetrics(analytics: any[]): PerformanceMetrics {
    // Calculate aggregated metrics from analytics data
    return {
      contentId: '',
      platform: '',
      engagementRate: 0,
      reach: 0,
      impressions: 0,
      clicks: 0,
      conversions: 0,
      sentiment: 0,
      timestamp: new Date()
    };
  }

  private calculateEngagementRate(analytics: any[]): number {
    if (!analytics || analytics.length === 0) return 0;
    
    const totalEngagement = analytics.reduce((sum, metric) => {
      return sum + (metric.likes || 0) + (metric.comments || 0) + (metric.shares || 0);
    }, 0);
    
    const totalReach = analytics.reduce((sum, metric) => sum + (metric.reach || 0), 0);
    
    return totalReach > 0 ? totalEngagement / totalReach : 0;
  }

  private extractKeywords(content: any): string[] {
    // Simplified keyword extraction
    if (!content || typeof content !== 'string') return [];
    
    const words = content.toLowerCase()
      .replace(/[^\w\s]/g, '')
      .split(/\s+/)
      .filter(word => word.length > 3);
    
    // Remove common stop words
    const stopWords = ['this', 'that', 'with', 'have', 'will', 'from', 'they', 'know', 'want', 'been', 'good', 'much', 'some', 'time'];
    
    return words.filter(word => !stopWords.includes(word));
  }

  private getDefaultOptimalTimes(platform: string): {
    times: string[];
    days: string[];
    confidence: number;
  } {
    const defaults = {
      instagram: {
        times: ['11:00', '14:00', '17:00'],
        days: ['Tuesday', 'Wednesday', 'Friday'],
        confidence: 0.5
      },
      facebook: {
        times: ['09:00', '13:00', '15:00'],
        days: ['Tuesday', 'Wednesday', 'Thursday'],
        confidence: 0.5
      },
      twitter: {
        times: ['08:00', '12:00', '17:00'],
        days: ['Tuesday', 'Wednesday', 'Thursday'],
        confidence: 0.5
      },
      linkedin: {
        times: ['08:00', '12:00', '17:00'],
        days: ['Tuesday', 'Wednesday', 'Thursday'],
        confidence: 0.5
      }
    };

    return defaults[platform as keyof typeof defaults] || defaults.instagram;
  }

  private async generateHashtagRecommendations(
    behaviorData: UserBehaviorData,
    performanceData: PerformanceMetrics[]
  ): Promise<ContentRecommendation[]> {
    // Implementation for hashtag recommendations
    return [];
  }

  private async generatePlatformRecommendations(
    behaviorData: UserBehaviorData,
    performanceData: PerformanceMetrics[]
  ): Promise<ContentRecommendation[]> {
    // Implementation for platform recommendations
    return [];
  }

  private async generateAudienceRecommendations(
    behaviorData: UserBehaviorData,
    performanceData: PerformanceMetrics[]
  ): Promise<ContentRecommendation[]> {
    // Implementation for audience recommendations
    return [];
  }

  private async updateUserBehaviorPatterns(userId: string, metrics: PerformanceMetrics): Promise<void> {
    // Update user behavior patterns based on performance
  }

  private async updateContentTypePerformance(type: string, platforms: string[], metrics: PerformanceMetrics): Promise<void> {
    // Update content type performance data
  }

  private async updateHashtagPerformance(hashtags: string[], metrics: PerformanceMetrics): Promise<void> {
    // Update hashtag performance data
  }

  private async updateTimingPerformance(timestamp: Date, metrics: PerformanceMetrics): Promise<void> {
    // Update timing performance data
  }
}
