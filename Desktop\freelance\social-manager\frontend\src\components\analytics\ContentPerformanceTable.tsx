import React from 'react';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { 
  Eye, 
  Heart, 
  Share2, 
  MessageCircle,
  TrendingUp,
  ExternalLink,
  MoreHorizontal
} from 'lucide-react';

interface ContentMetrics {
  contentId: string;
  title: string;
  platform: string;
  publishedAt: string;
  engagement: number;
  reach: number;
  impressions: number;
  engagementRate: number;
  clicks: number;
  shares: number;
  comments: number;
  likes: number;
  contentType: string;
  performance: 'excellent' | 'good' | 'average' | 'poor';
}

interface ContentPerformanceTableProps {
  data: ContentMetrics[];
  showActions?: boolean;
}

export const ContentPerformanceTable: React.FC<ContentPerformanceTableProps> = ({
  data,
  showActions = true
}) => {
  const getPerformanceBadge = (performance: ContentMetrics['performance']) => {
    const variants = {
      excellent: 'default',
      good: 'secondary',
      average: 'outline',
      poor: 'destructive'
    } as const;

    const colors = {
      excellent: '🚀',
      good: '👍',
      average: '📊',
      poor: '📉'
    };

    return (
      <Badge variant={variants[performance]} className="capitalize">
        {colors[performance]} {performance}
      </Badge>
    );
  };

  const getPlatformIcon = (platform: string) => {
    const icons: Record<string, string> = {
      INSTAGRAM: '📷',
      FACEBOOK: '👥',
      TWITTER: '🐦',
      LINKEDIN: '💼',
      TIKTOK: '🎵',
      YOUTUBE: '📺',
      PINTEREST: '📌'
    };
    return icons[platform] || '📱';
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toString();
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  if (data.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Content Performance</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <div className="text-4xl mb-4">📊</div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No content data available</h3>
            <p className="text-gray-600">Publish some content to see performance metrics here.</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center">
            <TrendingUp className="w-5 h-5 mr-2" />
            Content Performance
          </div>
          <Badge variant="outline">{data.length} posts</Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b">
                <th className="text-left py-3 px-2 font-medium text-gray-700">Content</th>
                <th className="text-left py-3 px-2 font-medium text-gray-700">Platform</th>
                <th className="text-left py-3 px-2 font-medium text-gray-700">Published</th>
                <th className="text-right py-3 px-2 font-medium text-gray-700">Reach</th>
                <th className="text-right py-3 px-2 font-medium text-gray-700">Engagement</th>
                <th className="text-right py-3 px-2 font-medium text-gray-700">Rate</th>
                <th className="text-center py-3 px-2 font-medium text-gray-700">Performance</th>
                {showActions && (
                  <th className="text-center py-3 px-2 font-medium text-gray-700">Actions</th>
                )}
              </tr>
            </thead>
            <tbody>
              {data.map((content) => (
                <tr key={content.contentId} className="border-b hover:bg-gray-50">
                  <td className="py-4 px-2">
                    <div>
                      <p className="font-medium text-gray-900 truncate max-w-48">
                        {content.title}
                      </p>
                      <p className="text-sm text-gray-500 capitalize">
                        {content.contentType}
                      </p>
                    </div>
                  </td>
                  <td className="py-4 px-2">
                    <div className="flex items-center space-x-2">
                      <span className="text-lg">{getPlatformIcon(content.platform)}</span>
                      <span className="text-sm font-medium">{content.platform}</span>
                    </div>
                  </td>
                  <td className="py-4 px-2">
                    <span className="text-sm text-gray-600">
                      {formatDate(content.publishedAt)}
                    </span>
                  </td>
                  <td className="py-4 px-2 text-right">
                    <div className="flex items-center justify-end space-x-1">
                      <Eye className="w-4 h-4 text-gray-400" />
                      <span className="text-sm font-medium">
                        {formatNumber(content.reach)}
                      </span>
                    </div>
                  </td>
                  <td className="py-4 px-2 text-right">
                    <div className="space-y-1">
                      <div className="flex items-center justify-end space-x-1">
                        <Heart className="w-4 h-4 text-red-400" />
                        <span className="text-sm">{formatNumber(content.likes)}</span>
                      </div>
                      <div className="flex items-center justify-end space-x-4 text-xs text-gray-500">
                        <span className="flex items-center">
                          <MessageCircle className="w-3 h-3 mr-1" />
                          {formatNumber(content.comments)}
                        </span>
                        <span className="flex items-center">
                          <Share2 className="w-3 h-3 mr-1" />
                          {formatNumber(content.shares)}
                        </span>
                      </div>
                    </div>
                  </td>
                  <td className="py-4 px-2 text-right">
                    <span className="text-sm font-medium">
                      {(content.engagementRate * 100).toFixed(1)}%
                    </span>
                  </td>
                  <td className="py-4 px-2 text-center">
                    {getPerformanceBadge(content.performance)}
                  </td>
                  {showActions && (
                    <td className="py-4 px-2 text-center">
                      <div className="flex items-center justify-center space-x-1">
                        <Button variant="ghost" size="sm">
                          <ExternalLink className="w-4 h-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="w-4 h-4" />
                        </Button>
                      </div>
                    </td>
                  )}
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Summary Stats */}
        <div className="mt-6 grid grid-cols-2 md:grid-cols-4 gap-4 pt-4 border-t">
          <div className="text-center">
            <p className="text-2xl font-bold text-gray-900">
              {formatNumber(data.reduce((sum, item) => sum + item.reach, 0))}
            </p>
            <p className="text-sm text-gray-600">Total Reach</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-gray-900">
              {formatNumber(data.reduce((sum, item) => sum + item.engagement, 0))}
            </p>
            <p className="text-sm text-gray-600">Total Engagement</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-gray-900">
              {(data.reduce((sum, item) => sum + item.engagementRate, 0) / data.length * 100).toFixed(1)}%
            </p>
            <p className="text-sm text-gray-600">Avg. Engagement Rate</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-gray-900">
              {data.filter(item => item.performance === 'excellent' || item.performance === 'good').length}
            </p>
            <p className="text-sm text-gray-600">High Performers</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ContentPerformanceTable;
