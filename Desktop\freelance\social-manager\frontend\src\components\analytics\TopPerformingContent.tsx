import { 
  TrophyIcon,
  EyeIcon,
  HeartIcon,
} from '@heroicons/react/24/outline'

interface TopPerformingContentData {
  id: string
  title: string
  platform: string
  engagements: number
  reach: number
}

interface TopPerformingContentProps {
  data: TopPerformingContentData[]
}

const TopPerformingContent: React.FC<TopPerformingContentProps> = ({ data }) => {
  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case 'INSTAGRAM': return '📷'
      case 'FACEBOOK': return '👥'
      case 'TWITTER': return '🐦'
      case 'LINKEDIN': return '💼'
      case 'TIKTOK': return '🎵'
      case 'YOUTUBE': return '📺'
      case 'PINTEREST': return '📌'
      default: return '📱'
    }
  }

  const getPlatformColor = (platform: string) => {
    switch (platform) {
      case 'INSTAGRAM': return 'text-pink-600'
      case 'FACEBOOK': return 'text-blue-600'
      case 'TWITTER': return 'text-sky-600'
      case 'LINKEDIN': return 'text-blue-700'
      case 'TIKTOK': return 'text-gray-900'
      case 'YOUTUBE': return 'text-red-600'
      case 'PINTEREST': return 'text-red-700'
      default: return 'text-gray-600'
    }
  }

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M'
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K'
    }
    return num.toString()
  }

  return (
    <div className="card">
      <div className="card-header">
        <div className="flex items-center space-x-2">
          <TrophyIcon className="h-5 w-5 text-yellow-600" />
          <h3 className="card-title">Top Performing Content</h3>
        </div>
        <p className="card-description">Highest engagement content pieces</p>
      </div>
      <div className="card-content">
        {data.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            No performance data available
          </div>
        ) : (
          <div className="space-y-4">
            {data.map((content, index) => (
              <div key={content.id} className="flex items-center space-x-4 p-3 bg-gray-50 rounded-lg">
                {/* Rank */}
                <div className="flex-shrink-0">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
                    index === 0 ? 'bg-yellow-100 text-yellow-800' :
                    index === 1 ? 'bg-gray-100 text-gray-800' :
                    'bg-orange-100 text-orange-800'
                  }`}>
                    {index + 1}
                  </div>
                </div>

                {/* Content Info */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2 mb-1">
                    <span className="text-lg">{getPlatformIcon(content.platform)}</span>
                    <span className={`text-sm font-medium ${getPlatformColor(content.platform)}`}>
                      {content.platform}
                    </span>
                  </div>
                  <h4 className="text-sm font-medium text-gray-900 truncate">
                    {content.title}
                  </h4>
                </div>

                {/* Metrics */}
                <div className="flex-shrink-0 text-right">
                  <div className="flex items-center space-x-1 text-purple-600 mb-1">
                    <HeartIcon className="h-4 w-4" />
                    <span className="text-sm font-medium">
                      {formatNumber(content.engagements)}
                    </span>
                  </div>
                  <div className="flex items-center space-x-1 text-blue-600">
                    <EyeIcon className="h-4 w-4" />
                    <span className="text-sm font-medium">
                      {formatNumber(content.reach)}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

export default TopPerformingContent
