const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api';
let authToken = '';
let testUserId = '';
let testProjectId = '';
let testContentId = '';

// Test results tracking
const testResults = {
  passed: 0,
  failed: 0,
  tests: []
};

function logTest(name, passed, details = '') {
  const status = passed ? '✅ PASS' : '❌ FAIL';
  console.log(`${status}: ${name}`);
  if (details) console.log(`   ${details}`);
  
  testResults.tests.push({ name, passed, details });
  if (passed) testResults.passed++;
  else testResults.failed++;
}

async function testAuth() {
  console.log('\n🔐 TESTING AUTHENTICATION...');
  
  try {
    // Test login
    const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'password123'
    });
    
    if (loginResponse.data.success && loginResponse.data.data.token) {
      authToken = loginResponse.data.data.token;
      testUserId = loginResponse.data.data.user.id;
      logTest('Login with valid credentials', true, `Token: ${authToken.substring(0, 20)}...`);
    } else {
      logTest('Login with valid credentials', false, 'No token received');
      return false;
    }
    
    // Test protected route
    const meResponse = await axios.get(`${BASE_URL}/auth/me`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    
    if (meResponse.data.success && meResponse.data.data.user) {
      logTest('Access protected route with token', true, `User: ${meResponse.data.data.user.firstName}`);
    } else {
      logTest('Access protected route with token', false);
    }
    
    // Test invalid login
    try {
      await axios.post(`${BASE_URL}/auth/login`, {
        email: '<EMAIL>',
        password: 'wrongpassword'
      });
      logTest('Reject invalid credentials', false, 'Should have failed but succeeded');
    } catch (error) {
      if (error.response && error.response.status === 401) {
        logTest('Reject invalid credentials', true, 'Correctly rejected');
      } else {
        logTest('Reject invalid credentials', false, `Unexpected error: ${error.message}`);
      }
    }
    
    return true;
    
  } catch (error) {
    logTest('Authentication setup', false, error.message);
    return false;
  }
}

async function testProjects() {
  console.log('\n📁 TESTING PROJECTS API...');
  
  try {
    // Create project
    const createResponse = await axios.post(`${BASE_URL}/projects`, {
      name: 'E2E Test Project',
      description: 'A project created during E2E testing',
      type: 'CAMPAIGN',
      budget: 5000,
      deadline: '2024-12-31'
    }, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    
    if (createResponse.data.success && createResponse.data.data.project) {
      testProjectId = createResponse.data.data.project.id;
      logTest('Create new project', true, `Project ID: ${testProjectId}`);
    } else {
      logTest('Create new project', false);
      return false;
    }
    
    // Get projects list
    const listResponse = await axios.get(`${BASE_URL}/projects`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    
    if (listResponse.data.success && Array.isArray(listResponse.data.data.projects)) {
      const projectCount = listResponse.data.data.projects.length;
      logTest('Get projects list', true, `Found ${projectCount} projects`);
    } else {
      logTest('Get projects list', false);
    }
    
    // Get specific project
    const getResponse = await axios.get(`${BASE_URL}/projects/${testProjectId}`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    
    if (getResponse.data.success && getResponse.data.data.project) {
      logTest('Get project by ID', true, `Name: ${getResponse.data.data.project.name}`);
    } else {
      logTest('Get project by ID', false);
    }
    
    // Update project
    const updateResponse = await axios.put(`${BASE_URL}/projects/${testProjectId}`, {
      name: 'Updated E2E Test Project',
      status: 'IN_PROGRESS'
    }, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    
    if (updateResponse.data.success) {
      logTest('Update project', true, 'Status changed to IN_PROGRESS');
    } else {
      logTest('Update project', false);
    }
    
    return true;
    
  } catch (error) {
    logTest('Projects API', false, error.message);
    return false;
  }
}

async function testContent() {
  console.log('\n📝 TESTING CONTENT API...');
  
  try {
    // Create content
    const createResponse = await axios.post(`${BASE_URL}/content`, {
      title: 'E2E Test Content',
      content: 'This is test content created during E2E testing',
      type: 'POST',
      platform: 'INSTAGRAM',
      projectId: testProjectId,
      scheduledFor: '2024-12-25T10:00:00Z'
    }, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    
    if (createResponse.data.success && createResponse.data.data.content) {
      testContentId = createResponse.data.data.content.id;
      logTest('Create new content', true, `Content ID: ${testContentId}`);
    } else {
      logTest('Create new content', false);
      return false;
    }
    
    // Get content list
    const listResponse = await axios.get(`${BASE_URL}/content`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    
    if (listResponse.data.success && Array.isArray(listResponse.data.data.content)) {
      const contentCount = listResponse.data.data.content.length;
      logTest('Get content list', true, `Found ${contentCount} content items`);
    } else {
      logTest('Get content list', false);
    }
    
    // Update content
    const updateResponse = await axios.put(`${BASE_URL}/content/${testContentId}`, {
      title: 'Updated E2E Test Content',
      status: 'APPROVED'
    }, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    
    if (updateResponse.data.success) {
      logTest('Update content', true, 'Status changed to APPROVED');
    } else {
      logTest('Update content', false);
    }
    
    return true;
    
  } catch (error) {
    logTest('Content API', false, error.message);
    return false;
  }
}

async function testAnalytics() {
  console.log('\n📊 TESTING ANALYTICS API...');
  
  try {
    // Get analytics overview
    const overviewResponse = await axios.get(`${BASE_URL}/analytics/overview`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    
    if (overviewResponse.data.success) {
      logTest('Get analytics overview', true, 'Analytics data retrieved');
    } else {
      logTest('Get analytics overview', false);
    }
    
    // Get performance metrics
    const metricsResponse = await axios.get(`${BASE_URL}/analytics/performance`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    
    if (metricsResponse.data.success) {
      logTest('Get performance metrics', true, 'Performance data retrieved');
    } else {
      logTest('Get performance metrics', false);
    }
    
    return true;
    
  } catch (error) {
    logTest('Analytics API', false, error.message);
    return false;
  }
}

async function testIntegrations() {
  console.log('\n🔗 TESTING INTEGRATIONS API...');
  
  try {
    // Get integrations list
    const listResponse = await axios.get(`${BASE_URL}/integrations`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    
    if (listResponse.data.success) {
      logTest('Get integrations list', true, 'Integrations data retrieved');
    } else {
      logTest('Get integrations list', false);
    }
    
    return true;
    
  } catch (error) {
    logTest('Integrations API', false, error.message);
    return false;
  }
}

async function cleanup() {
  console.log('\n🧹 CLEANING UP TEST DATA...');
  
  try {
    // Delete test content
    if (testContentId) {
      await axios.delete(`${BASE_URL}/content/${testContentId}`, {
        headers: { Authorization: `Bearer ${authToken}` }
      });
      logTest('Delete test content', true);
    }
    
    // Delete test project
    if (testProjectId) {
      await axios.delete(`${BASE_URL}/projects/${testProjectId}`, {
        headers: { Authorization: `Bearer ${authToken}` }
      });
      logTest('Delete test project', true);
    }
    
  } catch (error) {
    logTest('Cleanup', false, error.message);
  }
}

async function runAllTests() {
  console.log('🚀 STARTING COMPREHENSIVE E2E API TESTING...\n');
  console.log('Testing against:', BASE_URL);
  
  const authSuccess = await testAuth();
  if (!authSuccess) {
    console.log('\n❌ Authentication failed. Cannot continue with other tests.');
    return;
  }
  
  await testProjects();
  await testContent();
  await testAnalytics();
  await testIntegrations();
  await cleanup();
  
  // Print summary
  console.log('\n📊 TEST SUMMARY:');
  console.log('================');
  console.log(`✅ Passed: ${testResults.passed}`);
  console.log(`❌ Failed: ${testResults.failed}`);
  console.log(`📈 Success Rate: ${((testResults.passed / (testResults.passed + testResults.failed)) * 100).toFixed(1)}%`);
  
  if (testResults.failed > 0) {
    console.log('\n❌ FAILED TESTS:');
    testResults.tests.filter(t => !t.passed).forEach(test => {
      console.log(`   - ${test.name}: ${test.details}`);
    });
  }
  
  console.log('\n🎉 E2E API Testing Complete!');
}

// Run the tests
runAllTests().catch(console.error);
