import { configureStore } from '@reduxjs/toolkit'
import authSlice from './slices/authSlice'
import uiSlice from './slices/uiSlice'
import projectsSlice from './slices/projectsSlice'
import contentSlice from './slices/contentSlice'

export const store = configureStore({
  reducer: {
    auth: authSlice,
    ui: uiSlice,
    projects: projectsSlice,
    content: contentSlice,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST'],
      },
    }),
})

export type RootState = ReturnType<typeof store.getState>
export type AppDispatch = typeof store.dispatch
