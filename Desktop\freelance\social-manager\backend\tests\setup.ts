const { PrismaClient } = require('@prisma/client');
const { createClient } = require('redis');

// Test database setup
const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.TEST_DATABASE_URL || 'file:./test.db'
    }
  }
});

// Test Redis setup
const redis = createClient({
  url: process.env.TEST_REDIS_URL || 'redis://localhost:6379/1'
});

// Global test setup
beforeAll(async () => {
  // Connect to test database
  await prisma.$connect();
  
  // Connect to test Redis
  await redis.connect();
  
  // Run migrations
  // await prisma.$executeRaw`PRAGMA foreign_keys = OFF`;
  // await prisma.$executeRaw`DELETE FROM users`;
  // await prisma.$executeRaw`DELETE FROM organizations`;
  // await prisma.$executeRaw`PRAGMA foreign_keys = ON`;
});

// Clean up after each test
afterEach(async () => {
  // Clear Redis cache
  await redis.flushDb();
  
  // Clean up database (in reverse order of dependencies)
  const tablenames = await prisma.$queryRaw<Array<{ name: string }>>`
    SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%' AND name NOT LIKE '_prisma_%';
  `;
  
  for (const { name } of tablenames) {
    await prisma.$executeRawUnsafe(`DELETE FROM "${name}";`);
  }
});

// Global test teardown
afterAll(async () => {
  await redis.disconnect();
  await prisma.$disconnect();
});

// Export test utilities
export { prisma, redis };

// Mock environment variables for testing
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test-jwt-secret';
process.env.JWT_REFRESH_SECRET = 'test-refresh-secret';
process.env.REDIS_URL = 'redis://localhost:6379/1';

// Increase timeout for integration tests
jest.setTimeout(30000);
