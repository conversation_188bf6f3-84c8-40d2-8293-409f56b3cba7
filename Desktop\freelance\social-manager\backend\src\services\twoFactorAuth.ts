import speakeasy from 'speakeasy';
import QRCode from 'qrcode';
import { PrismaClient } from '@prisma/client';
import { logger } from '../utils/logger.js';
import { CacheService } from '../config/redis.js';

interface TwoFactorSetup {
  secret: string;
  qrCodeUrl: string;
  backupCodes: string[];
}

interface TwoFactorVerification {
  isValid: boolean;
  usedBackupCode?: boolean;
}

export class TwoFactorAuthService {
  private prisma: PrismaClient;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }

  /**
   * Generate 2FA secret and QR code for user setup
   */
  async generateTwoFactorSetup(userId: string, userEmail: string): Promise<TwoFactorSetup> {
    try {
      // Generate secret
      const secret = speakeasy.generateSecret({
        name: `SocialHub Pro (${userEmail})`,
        issuer: 'SocialHub Pro',
        length: 32
      });

      // Generate QR code
      const qrCodeUrl = await QRCode.toDataURL(secret.otpauth_url!);

      // Generate backup codes
      const backupCodes = this.generateBackupCodes();

      // Store secret temporarily (not activated until verified)
      await this.storeTempSecret(userId, secret.base32, backupCodes);

      logger.info(`2FA setup generated for user: ${userId}`);

      return {
        secret: secret.base32,
        qrCodeUrl,
        backupCodes
      };
    } catch (error) {
      logger.error('Error generating 2FA setup:', error);
      throw new Error('Failed to generate 2FA setup');
    }
  }

  /**
   * Verify and activate 2FA for user
   */
  async verifyAndActivateTwoFactor(
    userId: string, 
    token: string, 
    tempSecret: string
  ): Promise<boolean> {
    try {
      // Verify the token
      const isValid = speakeasy.totp.verify({
        secret: tempSecret,
        encoding: 'base32',
        token,
        window: 2 // Allow 2 time steps (60 seconds) tolerance
      });

      if (!isValid) {
        logger.warn(`Invalid 2FA token for user: ${userId}`);
        return false;
      }

      // Get backup codes from temp storage
      const backupCodes = await this.getTempBackupCodes(userId);

      // Activate 2FA for user
      await this.prisma.user.update({
        where: { id: userId },
        data: {
          twoFactorSecret: tempSecret,
          twoFactorEnabled: true,
          twoFactorBackupCodes: backupCodes,
          updatedAt: new Date()
        }
      });

      // Clean up temporary data
      await this.cleanupTempData(userId);

      logger.info(`2FA activated for user: ${userId}`);
      return true;
    } catch (error) {
      logger.error('Error verifying and activating 2FA:', error);
      throw new Error('Failed to activate 2FA');
    }
  }

  /**
   * Verify 2FA token during login
   */
  async verifyTwoFactorToken(userId: string, token: string): Promise<TwoFactorVerification> {
    try {
      const user = await this.prisma.user.findUnique({
        where: { id: userId },
        select: {
          twoFactorSecret: true,
          twoFactorEnabled: true,
          twoFactorBackupCodes: true
        }
      });

      if (!user || !user.twoFactorEnabled || !user.twoFactorSecret) {
        return { isValid: false };
      }

      // First try to verify with TOTP
      const isValidTotp = speakeasy.totp.verify({
        secret: user.twoFactorSecret,
        encoding: 'base32',
        token,
        window: 2
      });

      if (isValidTotp) {
        // Check if this token was recently used to prevent replay attacks
        const recentlyUsed = await this.isTokenRecentlyUsed(userId, token);
        if (recentlyUsed) {
          logger.warn(`Replay attack detected for user: ${userId}`);
          return { isValid: false };
        }

        // Mark token as used
        await this.markTokenAsUsed(userId, token);
        return { isValid: true };
      }

      // If TOTP fails, check backup codes
      const backupCodes = user.twoFactorBackupCodes as string[] || [];
      const backupCodeIndex = backupCodes.indexOf(token);

      if (backupCodeIndex !== -1) {
        // Remove used backup code
        const updatedBackupCodes = backupCodes.filter((_, index) => index !== backupCodeIndex);
        
        await this.prisma.user.update({
          where: { id: userId },
          data: {
            twoFactorBackupCodes: updatedBackupCodes,
            updatedAt: new Date()
          }
        });

        logger.info(`Backup code used for user: ${userId}, remaining: ${updatedBackupCodes.length}`);
        return { isValid: true, usedBackupCode: true };
      }

      logger.warn(`Invalid 2FA token for user: ${userId}`);
      return { isValid: false };
    } catch (error) {
      logger.error('Error verifying 2FA token:', error);
      throw new Error('Failed to verify 2FA token');
    }
  }

  /**
   * Disable 2FA for user
   */
  async disableTwoFactor(userId: string, password: string): Promise<boolean> {
    try {
      // Verify password before disabling 2FA
      const user = await this.prisma.user.findUnique({
        where: { id: userId },
        select: { password: true }
      });

      if (!user) {
        return false;
      }

      // Note: You should verify the password here using bcrypt
      // const isValidPassword = await bcrypt.compare(password, user.password);
      // if (!isValidPassword) return false;

      await this.prisma.user.update({
        where: { id: userId },
        data: {
          twoFactorSecret: null,
          twoFactorEnabled: false,
          twoFactorBackupCodes: null,
          updatedAt: new Date()
        }
      });

      // Clean up any cached tokens
      await this.cleanupUserTokens(userId);

      logger.info(`2FA disabled for user: ${userId}`);
      return true;
    } catch (error) {
      logger.error('Error disabling 2FA:', error);
      throw new Error('Failed to disable 2FA');
    }
  }

  /**
   * Generate new backup codes
   */
  async generateNewBackupCodes(userId: string): Promise<string[]> {
    try {
      const backupCodes = this.generateBackupCodes();

      await this.prisma.user.update({
        where: { id: userId },
        data: {
          twoFactorBackupCodes: backupCodes,
          updatedAt: new Date()
        }
      });

      logger.info(`New backup codes generated for user: ${userId}`);
      return backupCodes;
    } catch (error) {
      logger.error('Error generating new backup codes:', error);
      throw new Error('Failed to generate new backup codes');
    }
  }

  /**
   * Check if user has 2FA enabled
   */
  async isTwoFactorEnabled(userId: string): Promise<boolean> {
    try {
      const user = await this.prisma.user.findUnique({
        where: { id: userId },
        select: { twoFactorEnabled: true }
      });

      return user?.twoFactorEnabled || false;
    } catch (error) {
      logger.error('Error checking 2FA status:', error);
      return false;
    }
  }

  /**
   * Generate backup codes
   */
  private generateBackupCodes(count: number = 10): string[] {
    const codes: string[] = [];
    for (let i = 0; i < count; i++) {
      // Generate 8-character alphanumeric code
      const code = Math.random().toString(36).substring(2, 10).toUpperCase();
      codes.push(code);
    }
    return codes;
  }

  /**
   * Store temporary secret during setup
   */
  private async storeTempSecret(userId: string, secret: string, backupCodes: string[]): Promise<void> {
    const key = `2fa_temp:${userId}`;
    const data = JSON.stringify({ secret, backupCodes });
    await CacheService.set(key, data, 600); // 10 minutes expiry
  }

  /**
   * Get temporary backup codes
   */
  private async getTempBackupCodes(userId: string): Promise<string[]> {
    const key = `2fa_temp:${userId}`;
    const data = await CacheService.get(key);
    if (data) {
      const parsed = JSON.parse(data);
      return parsed.backupCodes;
    }
    return [];
  }

  /**
   * Clean up temporary data
   */
  private async cleanupTempData(userId: string): Promise<void> {
    const key = `2fa_temp:${userId}`;
    await CacheService.del(key);
  }

  /**
   * Check if token was recently used
   */
  private async isTokenRecentlyUsed(userId: string, token: string): Promise<boolean> {
    const key = `2fa_used:${userId}:${token}`;
    return await CacheService.exists(key);
  }

  /**
   * Mark token as used
   */
  private async markTokenAsUsed(userId: string, token: string): Promise<void> {
    const key = `2fa_used:${userId}:${token}`;
    await CacheService.set(key, 'used', 90); // 90 seconds (3 time windows)
  }

  /**
   * Clean up user tokens
   */
  private async cleanupUserTokens(userId: string): Promise<void> {
    // This would require a more sophisticated Redis pattern matching
    // For now, we'll just log it
    logger.info(`Cleaned up 2FA tokens for user: ${userId}`);
  }
}
