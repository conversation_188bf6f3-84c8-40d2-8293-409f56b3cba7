import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Brain,
  TrendingUp,
  Clock,
  Target,
  Hash,
  Users,
  Lightbulb,
  Star,
  ArrowRight,
  RefreshCw,
  Filter,
  CheckCircle,
  AlertTriangle,
  Info
} from 'lucide-react';

interface ContentRecommendation {
  type: 'content_idea' | 'posting_time' | 'hashtag' | 'platform' | 'audience_segment';
  title: string;
  description: string;
  confidence: number;
  reasoning: string;
  data: any;
  priority: 'low' | 'medium' | 'high';
  category: string;
}

interface TrendPrediction {
  topic: string;
  platform: string;
  trendScore: number;
  peakTime: Date;
  duration: number;
  confidence: number;
  relatedTopics: string[];
  suggestedContent: string[];
}

const AIRecommendationsDashboard: React.FC = () => {
  const [recommendations, setRecommendations] = useState<ContentRecommendation[]>([]);
  const [trends, setTrends] = useState<TrendPrediction[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | 'high' | 'medium' | 'low'>('all');
  const [activeCategory, setActiveCategory] = useState<string>('all');

  useEffect(() => {
    fetchRecommendations();
    fetchTrends();
  }, []);

  const fetchRecommendations = async () => {
    try {
      const response = await fetch('/api/ai/recommendations');
      if (response.ok) {
        const data = await response.json();
        setRecommendations(data);
      }
    } catch (error) {
      console.error('Error fetching recommendations:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchTrends = async () => {
    try {
      const response = await fetch('/api/ai/trends');
      if (response.ok) {
        const data = await response.json();
        setTrends(data);
      }
    } catch (error) {
      console.error('Error fetching trends:', error);
    }
  };

  const handleRefresh = () => {
    setLoading(true);
    fetchRecommendations();
    fetchTrends();
  };

  const filteredRecommendations = recommendations.filter(rec => {
    if (filter !== 'all' && rec.priority !== filter) return false;
    if (activeCategory !== 'all' && rec.category !== activeCategory) return false;
    return true;
  });

  const getRecommendationIcon = (type: string) => {
    switch (type) {
      case 'content_idea': return Lightbulb;
      case 'posting_time': return Clock;
      case 'hashtag': return Hash;
      case 'platform': return Target;
      case 'audience_segment': return Users;
      default: return Star;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'text-red-600 bg-red-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'low': return 'text-green-600 bg-green-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'high': return AlertTriangle;
      case 'medium': return Info;
      case 'low': return CheckCircle;
      default: return Info;
    }
  };

  const categories = ['all', ...Array.from(new Set(recommendations.map(r => r.category)))];

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Brain className="w-8 h-8 text-purple-500" />
          <div>
            <h1 className="text-3xl font-bold text-gray-900">AI Recommendations</h1>
            <p className="text-gray-600">Intelligent insights to boost your social media performance</p>
          </div>
        </div>
        
        <button
          onClick={handleRefresh}
          disabled={loading}
          className="flex items-center space-x-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors disabled:opacity-50"
        >
          <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
          <span>Refresh</span>
        </button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-xl shadow-sm border p-6">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Lightbulb className="w-6 h-6 text-purple-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Total Recommendations</p>
              <p className="text-2xl font-bold text-gray-900">{recommendations.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border p-6">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-red-100 rounded-lg">
              <AlertTriangle className="w-6 h-6 text-red-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">High Priority</p>
              <p className="text-2xl font-bold text-gray-900">
                {recommendations.filter(r => r.priority === 'high').length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border p-6">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <TrendingUp className="w-6 h-6 text-blue-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Trending Topics</p>
              <p className="text-2xl font-bold text-gray-900">{trends.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border p-6">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-green-100 rounded-lg">
              <Target className="w-6 h-6 text-green-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Avg. Confidence</p>
              <p className="text-2xl font-bold text-gray-900">
                {recommendations.length > 0 
                  ? Math.round(recommendations.reduce((sum, r) => sum + r.confidence, 0) / recommendations.length * 100)
                  : 0}%
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-xl shadow-sm border p-6">
        <div className="flex flex-wrap items-center gap-4">
          <div className="flex items-center space-x-2">
            <Filter className="w-4 h-4 text-gray-500" />
            <span className="text-sm font-medium text-gray-700">Filters:</span>
          </div>

          {/* Priority Filter */}
          <div className="flex space-x-1">
            {['all', 'high', 'medium', 'low'].map(priority => (
              <button
                key={priority}
                onClick={() => setFilter(priority as any)}
                className={`px-3 py-1 text-sm rounded-md transition-colors ${
                  filter === priority
                    ? 'bg-purple-100 text-purple-700'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                {priority.charAt(0).toUpperCase() + priority.slice(1)} Priority
              </button>
            ))}
          </div>

          {/* Category Filter */}
          <div className="flex space-x-1">
            {categories.map(category => (
              <button
                key={category}
                onClick={() => setActiveCategory(category)}
                className={`px-3 py-1 text-sm rounded-md transition-colors ${
                  activeCategory === category
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                {category.charAt(0).toUpperCase() + category.slice(1)}
              </button>
            ))}
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Recommendations */}
        <div className="lg:col-span-2 space-y-4">
          <h2 className="text-xl font-semibold text-gray-900">Recommendations</h2>
          
          {loading ? (
            <div className="space-y-4">
              {[1, 2, 3].map(i => (
                <div key={i} className="bg-white rounded-xl shadow-sm border p-6 animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2 mb-4"></div>
                  <div className="h-3 bg-gray-200 rounded w-full"></div>
                </div>
              ))}
            </div>
          ) : (
            <AnimatePresence>
              <div className="space-y-4">
                {filteredRecommendations.map((recommendation, index) => {
                  const Icon = getRecommendationIcon(recommendation.type);
                  const PriorityIcon = getPriorityIcon(recommendation.priority);
                  
                  return (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      transition={{ delay: index * 0.1 }}
                      className="bg-white rounded-xl shadow-sm border p-6 hover:shadow-md transition-shadow"
                    >
                      <div className="flex items-start space-x-4">
                        <div className="p-2 bg-purple-100 rounded-lg">
                          <Icon className="w-5 h-5 text-purple-600" />
                        </div>
                        
                        <div className="flex-1 space-y-2">
                          <div className="flex items-center justify-between">
                            <h3 className="text-lg font-semibold text-gray-900">
                              {recommendation.title}
                            </h3>
                            <div className={`flex items-center space-x-1 px-2 py-1 rounded-md text-xs font-medium ${getPriorityColor(recommendation.priority)}`}>
                              <PriorityIcon className="w-3 h-3" />
                              <span>{recommendation.priority.toUpperCase()}</span>
                            </div>
                          </div>
                          
                          <p className="text-gray-600">{recommendation.description}</p>
                          
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-4">
                              <div className="flex items-center space-x-1">
                                <Star className="w-4 h-4 text-yellow-500" />
                                <span className="text-sm text-gray-600">
                                  {Math.round(recommendation.confidence * 100)}% confidence
                                </span>
                              </div>
                              <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                                {recommendation.category}
                              </span>
                            </div>
                            
                            <button className="flex items-center space-x-1 text-purple-600 hover:text-purple-700 text-sm font-medium">
                              <span>Apply</span>
                              <ArrowRight className="w-4 h-4" />
                            </button>
                          </div>
                          
                          <details className="text-sm text-gray-600">
                            <summary className="cursor-pointer hover:text-gray-900">
                              Why this recommendation?
                            </summary>
                            <p className="mt-2 pl-4 border-l-2 border-gray-200">
                              {recommendation.reasoning}
                            </p>
                          </details>
                        </div>
                      </div>
                    </motion.div>
                  );
                })}
              </div>
            </AnimatePresence>
          )}

          {!loading && filteredRecommendations.length === 0 && (
            <div className="bg-gray-50 rounded-xl border-2 border-dashed border-gray-300 p-12 text-center">
              <Brain className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Recommendations</h3>
              <p className="text-gray-600">
                No recommendations match your current filters. Try adjusting the filters or refresh to get new insights.
              </p>
            </div>
          )}
        </div>

        {/* Trending Topics */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold text-gray-900">Trending Topics</h2>
          
          <div className="space-y-4">
            {trends.slice(0, 5).map((trend, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className="bg-white rounded-xl shadow-sm border p-4"
              >
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-semibold text-gray-900">{trend.topic}</h3>
                  <div className="flex items-center space-x-1">
                    <TrendingUp className="w-4 h-4 text-green-500" />
                    <span className="text-sm font-medium text-green-600">
                      {Math.round(trend.trendScore)}
                    </span>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm text-gray-600">
                    <span>Platform:</span>
                    <span className="font-medium">{trend.platform}</span>
                  </div>
                  
                  <div className="flex items-center justify-between text-sm text-gray-600">
                    <span>Peak Time:</span>
                    <span className="font-medium">
                      {new Date(trend.peakTime).toLocaleDateString()}
                    </span>
                  </div>
                  
                  <div className="flex items-center justify-between text-sm text-gray-600">
                    <span>Duration:</span>
                    <span className="font-medium">{trend.duration} days</span>
                  </div>
                  
                  <div className="flex items-center justify-between text-sm text-gray-600">
                    <span>Confidence:</span>
                    <span className="font-medium">{Math.round(trend.confidence * 100)}%</span>
                  </div>
                </div>

                {trend.relatedTopics.length > 0 && (
                  <div className="mt-3 pt-3 border-t">
                    <p className="text-xs text-gray-500 mb-2">Related Topics:</p>
                    <div className="flex flex-wrap gap-1">
                      {trend.relatedTopics.slice(0, 3).map((topic, i) => (
                        <span
                          key={i}
                          className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded"
                        >
                          {topic}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                <button className="w-full mt-3 py-2 text-sm text-purple-600 hover:text-purple-700 font-medium border border-purple-200 rounded-lg hover:bg-purple-50 transition-colors">
                  Create Content
                </button>
              </motion.div>
            ))}
          </div>

          {trends.length === 0 && !loading && (
            <div className="bg-gray-50 rounded-xl border-2 border-dashed border-gray-300 p-8 text-center">
              <TrendingUp className="w-8 h-8 text-gray-400 mx-auto mb-2" />
              <p className="text-gray-600 text-sm">No trending topics found</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AIRecommendationsDashboard;
