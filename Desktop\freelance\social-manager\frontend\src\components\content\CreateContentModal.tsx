import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { useCreateContentItem } from '../../hooks/useContent'
import { Project } from '../../types/project'
import { Platform, ContentType } from '../../types/content'
import { XMarkIcon } from '@heroicons/react/24/outline'
import LoadingSpinner from '../ui/LoadingSpinner'

const createContentSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  description: z.string().optional(),
  type: z.enum(['POST', 'STORY', 'REEL', 'VIDEO', 'ARTICLE', 'CAROUSEL']),
  platforms: z.array(z.enum(['INSTAGRAM', 'FACEBOOK', 'TWITTER', 'LINKEDIN', 'TIKTOK', 'YOUTUBE', 'PINTEREST'])).min(1, 'Select at least one platform'),
  projectId: z.string().min(1, 'Project is required'),
  contentText: z.string().min(1, 'Content text is required'),
  hashtags: z.string().optional(),
  scheduledAt: z.string().optional(),
})

type CreateContentFormData = z.infer<typeof createContentSchema>

interface CreateContentModalProps {
  projects: Project[]
  onClose: () => void
  onSuccess: () => void
}

const CreateContentModal: React.FC<CreateContentModalProps> = ({
  projects,
  onClose,
  onSuccess,
}) => {
  const [selectedPlatforms, setSelectedPlatforms] = useState<Platform[]>([])
  const createContentItem = useCreateContentItem()

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
  } = useForm<CreateContentFormData>({
    resolver: zodResolver(createContentSchema),
  })

  const contentType = watch('type')

  const platformOptions: { value: Platform; label: string; icon: string }[] = [
    { value: 'INSTAGRAM', label: 'Instagram', icon: '📷' },
    { value: 'FACEBOOK', label: 'Facebook', icon: '👥' },
    { value: 'TWITTER', label: 'Twitter', icon: '🐦' },
    { value: 'LINKEDIN', label: 'LinkedIn', icon: '💼' },
    { value: 'TIKTOK', label: 'TikTok', icon: '🎵' },
    { value: 'YOUTUBE', label: 'YouTube', icon: '📺' },
    { value: 'PINTEREST', label: 'Pinterest', icon: '📌' },
  ]

  const contentTypeOptions: { value: ContentType; label: string; icon: string }[] = [
    { value: 'POST', label: 'Post', icon: '📝' },
    { value: 'STORY', label: 'Story', icon: '📖' },
    { value: 'REEL', label: 'Reel', icon: '🎬' },
    { value: 'VIDEO', label: 'Video', icon: '🎥' },
    { value: 'ARTICLE', label: 'Article', icon: '📄' },
    { value: 'CAROUSEL', label: 'Carousel', icon: '🎠' },
  ]

  const handlePlatformToggle = (platform: Platform) => {
    const newPlatforms = selectedPlatforms.includes(platform)
      ? selectedPlatforms.filter(p => p !== platform)
      : [...selectedPlatforms, platform]
    
    setSelectedPlatforms(newPlatforms)
    setValue('platforms', newPlatforms)
  }

  const onSubmit = async (data: CreateContentFormData) => {
    try {
      const contentData = {
        text: data.contentText,
        hashtags: data.hashtags ? data.hashtags.split(' ').filter(tag => tag.startsWith('#')) : [],
        type: data.type,
      }

      await createContentItem.mutateAsync({
        title: data.title,
        description: data.description,
        type: data.type,
        platforms: data.platforms,
        contentData,
        projectId: data.projectId,
        scheduledAt: data.scheduledAt || undefined,
      })

      onSuccess()
    } catch (error) {
      // Error handling is done in the hook
    }
  }

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={onClose} />

        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full">
          <form onSubmit={handleSubmit(onSubmit)}>
            <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-medium text-gray-900">Create Content</h3>
                <button
                  type="button"
                  onClick={onClose}
                  className="text-gray-400 hover:text-gray-500"
                >
                  <XMarkIcon className="h-6 w-6" />
                </button>
              </div>

              <div className="space-y-6">
                {/* Title */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Title *
                  </label>
                  <input
                    {...register('title')}
                    type="text"
                    className="input"
                    placeholder="Enter content title"
                  />
                  {errors.title && (
                    <p className="mt-1 text-sm text-red-600">{errors.title.message}</p>
                  )}
                </div>

                {/* Project */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Project *
                  </label>
                  <select {...register('projectId')} className="input">
                    <option value="">Select a project</option>
                    {projects.map((project) => (
                      <option key={project.id} value={project.id}>
                        {project.name}
                      </option>
                    ))}
                  </select>
                  {errors.projectId && (
                    <p className="mt-1 text-sm text-red-600">{errors.projectId.message}</p>
                  )}
                </div>

                {/* Content Type */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Content Type *
                  </label>
                  <div className="grid grid-cols-3 gap-3">
                    {contentTypeOptions.map((option) => (
                      <label
                        key={option.value}
                        className={`relative flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50 ${
                          contentType === option.value ? 'border-primary-500 bg-primary-50' : 'border-gray-300'
                        }`}
                      >
                        <input
                          {...register('type')}
                          type="radio"
                          value={option.value}
                          className="sr-only"
                        />
                        <div className="flex items-center space-x-2">
                          <span className="text-lg">{option.icon}</span>
                          <span className="text-sm font-medium">{option.label}</span>
                        </div>
                      </label>
                    ))}
                  </div>
                  {errors.type && (
                    <p className="mt-1 text-sm text-red-600">{errors.type.message}</p>
                  )}
                </div>

                {/* Platforms */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Platforms *
                  </label>
                  <div className="grid grid-cols-2 gap-3">
                    {platformOptions.map((option) => (
                      <label
                        key={option.value}
                        className={`relative flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50 ${
                          selectedPlatforms.includes(option.value) ? 'border-primary-500 bg-primary-50' : 'border-gray-300'
                        }`}
                      >
                        <input
                          type="checkbox"
                          checked={selectedPlatforms.includes(option.value)}
                          onChange={() => handlePlatformToggle(option.value)}
                          className="sr-only"
                        />
                        <div className="flex items-center space-x-2">
                          <span className="text-lg">{option.icon}</span>
                          <span className="text-sm font-medium">{option.label}</span>
                        </div>
                      </label>
                    ))}
                  </div>
                  {errors.platforms && (
                    <p className="mt-1 text-sm text-red-600">{errors.platforms.message}</p>
                  )}
                </div>

                {/* Content Text */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Content Text *
                  </label>
                  <textarea
                    {...register('contentText')}
                    rows={4}
                    className="input"
                    placeholder="Write your content here..."
                  />
                  {errors.contentText && (
                    <p className="mt-1 text-sm text-red-600">{errors.contentText.message}</p>
                  )}
                </div>

                {/* Hashtags */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Hashtags
                  </label>
                  <input
                    {...register('hashtags')}
                    type="text"
                    className="input"
                    placeholder="#hashtag1 #hashtag2 #hashtag3"
                  />
                  <p className="mt-1 text-sm text-gray-500">
                    Separate hashtags with spaces
                  </p>
                </div>

                {/* Description */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Description
                  </label>
                  <textarea
                    {...register('description')}
                    rows={2}
                    className="input"
                    placeholder="Optional description for internal use"
                  />
                </div>

                {/* Scheduled Date */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Schedule for later (optional)
                  </label>
                  <input
                    {...register('scheduledAt')}
                    type="datetime-local"
                    className="input"
                  />
                </div>
              </div>
            </div>

            <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
              <button
                type="submit"
                disabled={createContentItem.isPending}
                className="btn-primary w-full sm:w-auto sm:ml-3"
              >
                {createContentItem.isPending ? (
                  <div className="flex items-center justify-center">
                    <LoadingSpinner size="sm" className="mr-2" />
                    Creating...
                  </div>
                ) : (
                  'Create Content'
                )}
              </button>
              <button
                type="button"
                onClick={onClose}
                className="btn-outline w-full sm:w-auto mt-3 sm:mt-0"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}

export default CreateContentModal
