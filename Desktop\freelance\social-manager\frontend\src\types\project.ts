import { ContentItem } from './content'

export interface Project {
  id: string
  name: string
  description?: string
  type: ProjectType
  status: ProjectStatus
  budget?: number
  deadline?: string
  startDate?: string
  endDate?: string
  metadata?: any
  createdAt: string
  updatedAt: string
  organizationId: string
  createdById: string
  createdBy: {
    id: string
    firstName: string
    lastName: string
    avatar?: string
  }
  members: ProjectMember[]
  contentItems?: ContentItem[]
  milestones?: Milestone[]
}

export interface ProjectMember {
  id: string
  role: ProjectRole
  joinedAt: string
  user: {
    id: string
    firstName: string
    lastName: string
    email: string
    avatar?: string
  }
}

export interface Milestone {
  id: string
  title: string
  description?: string
  dueDate: string
  status: MilestoneStatus
  createdAt: string
  updatedAt: string
}

export type ProjectType = 'CAMPAIGN' | 'ONGOING' | 'ONE_TIME';

export type ProjectStatus = 'PLANNING' | 'IN_PROGRESS' | 'REVIEW' | 'COMPLETED' | 'CANCELLED';

export type ProjectRole = 'OWNER' | 'MANAGER' | 'MEMBER' | 'VIEWER';

export type MilestoneStatus = 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'OVERDUE';

export interface CreateProjectData {
  name: string
  description?: string
  type: ProjectType
  budget?: number
  deadline?: string
  startDate?: string
  endDate?: string
}

export interface UpdateProjectData extends Partial<CreateProjectData> {
  status?: ProjectStatus
}
