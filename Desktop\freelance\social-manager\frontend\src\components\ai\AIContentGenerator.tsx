import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  <PERSON><PERSON>les, 
  Wand2, 
  Target, 
  Clock, 
  Hash, 
  MessageSquare,
  RefreshCw,
  Copy,
  Check,
  Lightbulb,
  TrendingUp
} from 'lucide-react';

interface ContentGenerationRequest {
  type: 'post' | 'caption' | 'hashtags' | 'story' | 'article' | 'ad_copy';
  platform: 'instagram' | 'facebook' | 'twitter' | 'linkedin' | 'tiktok' | 'youtube';
  topic: string;
  tone: 'professional' | 'casual' | 'humorous' | 'inspirational' | 'educational' | 'promotional';
  length: 'short' | 'medium' | 'long';
  targetAudience?: string;
  keywords?: string[];
  brandVoice?: string;
  includeEmojis?: boolean;
  includeHashtags?: boolean;
  callToAction?: string;
  context?: string;
}

interface GeneratedContent {
  content: string;
  hashtags?: string[];
  suggestions?: string[];
  confidence: number;
  metadata: {
    wordCount: number;
    characterCount: number;
    estimatedEngagement: number;
    seoScore: number;
  };
}

const AIContentGenerator: React.FC = () => {
  const [request, setRequest] = useState<ContentGenerationRequest>({
    type: 'post',
    platform: 'instagram',
    topic: '',
    tone: 'professional',
    length: 'medium',
    includeEmojis: true,
    includeHashtags: true
  });

  const [generatedContent, setGeneratedContent] = useState<GeneratedContent | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [copied, setCopied] = useState(false);
  const [activeTab, setActiveTab] = useState<'generate' | 'optimize' | 'analyze'>('generate');

  const handleGenerate = async () => {
    if (!request.topic.trim()) return;

    setIsGenerating(true);
    try {
      const response = await fetch('/api/ai/generate-content', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(request)
      });

      if (response.ok) {
        const content = await response.json();
        setGeneratedContent(content);
      }
    } catch (error) {
      console.error('Error generating content:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  const handleCopy = async () => {
    if (!generatedContent) return;

    try {
      await navigator.clipboard.writeText(generatedContent.content);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Error copying to clipboard:', error);
    }
  };

  const handleOptimize = async () => {
    if (!generatedContent) return;

    setIsGenerating(true);
    try {
      const response = await fetch('/api/ai/optimize-content', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          content: generatedContent.content,
          platform: request.platform,
          goals: ['engagement', 'reach']
        })
      });

      if (response.ok) {
        const optimized = await response.json();
        setGeneratedContent(prev => prev ? {
          ...prev,
          content: optimized.optimizedContent,
          suggestions: optimized.suggestions
        } : null);
      }
    } catch (error) {
      console.error('Error optimizing content:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <div className="flex items-center justify-center space-x-2">
          <Sparkles className="w-8 h-8 text-purple-500" />
          <h1 className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
            AI Content Generator
          </h1>
        </div>
        <p className="text-gray-600">
          Create engaging, platform-optimized content with the power of AI
        </p>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
        {[
          { id: 'generate', label: 'Generate', icon: Wand2 },
          { id: 'optimize', label: 'Optimize', icon: TrendingUp },
          { id: 'analyze', label: 'Analyze', icon: Target }
        ].map(tab => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id as any)}
            className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-all ${
              activeTab === tab.id
                ? 'bg-white text-purple-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <tab.icon className="w-4 h-4" />
            <span>{tab.label}</span>
          </button>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Input Panel */}
        <div className="space-y-6">
          <div className="bg-white rounded-xl shadow-sm border p-6 space-y-4">
            <h2 className="text-xl font-semibold flex items-center space-x-2">
              <MessageSquare className="w-5 h-5 text-blue-500" />
              <span>Content Parameters</span>
            </h2>

            {/* Topic Input */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Topic or Theme
              </label>
              <textarea
                value={request.topic}
                onChange={(e) => setRequest(prev => ({ ...prev, topic: e.target.value }))}
                placeholder="What would you like to create content about?"
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent resize-none"
                rows={3}
              />
            </div>

            {/* Platform & Type */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Platform
                </label>
                <select
                  value={request.platform}
                  onChange={(e) => setRequest(prev => ({ ...prev, platform: e.target.value as any }))}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500"
                >
                  <option value="instagram">Instagram</option>
                  <option value="facebook">Facebook</option>
                  <option value="twitter">Twitter</option>
                  <option value="linkedin">LinkedIn</option>
                  <option value="tiktok">TikTok</option>
                  <option value="youtube">YouTube</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Content Type
                </label>
                <select
                  value={request.type}
                  onChange={(e) => setRequest(prev => ({ ...prev, type: e.target.value as any }))}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500"
                >
                  <option value="post">Post</option>
                  <option value="caption">Caption</option>
                  <option value="story">Story</option>
                  <option value="article">Article</option>
                  <option value="ad_copy">Ad Copy</option>
                </select>
              </div>
            </div>

            {/* Tone & Length */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Tone
                </label>
                <select
                  value={request.tone}
                  onChange={(e) => setRequest(prev => ({ ...prev, tone: e.target.value as any }))}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500"
                >
                  <option value="professional">Professional</option>
                  <option value="casual">Casual</option>
                  <option value="humorous">Humorous</option>
                  <option value="inspirational">Inspirational</option>
                  <option value="educational">Educational</option>
                  <option value="promotional">Promotional</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Length
                </label>
                <select
                  value={request.length}
                  onChange={(e) => setRequest(prev => ({ ...prev, length: e.target.value as any }))}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500"
                >
                  <option value="short">Short</option>
                  <option value="medium">Medium</option>
                  <option value="long">Long</option>
                </select>
              </div>
            </div>

            {/* Advanced Options */}
            <div className="space-y-3">
              <h3 className="text-sm font-medium text-gray-700">Advanced Options</h3>
              
              <div className="flex flex-wrap gap-4">
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={request.includeEmojis}
                    onChange={(e) => setRequest(prev => ({ ...prev, includeEmojis: e.target.checked }))}
                    className="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                  />
                  <span className="text-sm text-gray-700">Include Emojis</span>
                </label>

                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={request.includeHashtags}
                    onChange={(e) => setRequest(prev => ({ ...prev, includeHashtags: e.target.checked }))}
                    className="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                  />
                  <span className="text-sm text-gray-700">Include Hashtags</span>
                </label>
              </div>

              {/* Target Audience */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Target Audience (Optional)
                </label>
                <input
                  type="text"
                  value={request.targetAudience || ''}
                  onChange={(e) => setRequest(prev => ({ ...prev, targetAudience: e.target.value }))}
                  placeholder="e.g., Young professionals, Tech enthusiasts"
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500"
                />
              </div>

              {/* Call to Action */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Call to Action (Optional)
                </label>
                <input
                  type="text"
                  value={request.callToAction || ''}
                  onChange={(e) => setRequest(prev => ({ ...prev, callToAction: e.target.value }))}
                  placeholder="e.g., Visit our website, Sign up today"
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500"
                />
              </div>
            </div>

            {/* Generate Button */}
            <button
              onClick={handleGenerate}
              disabled={!request.topic.trim() || isGenerating}
              className="w-full bg-gradient-to-r from-purple-600 to-blue-600 text-white py-3 px-6 rounded-lg font-medium hover:from-purple-700 hover:to-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all flex items-center justify-center space-x-2"
            >
              {isGenerating ? (
                <>
                  <RefreshCw className="w-4 h-4 animate-spin" />
                  <span>Generating...</span>
                </>
              ) : (
                <>
                  <Sparkles className="w-4 h-4" />
                  <span>Generate Content</span>
                </>
              )}
            </button>
          </div>
        </div>

        {/* Output Panel */}
        <div className="space-y-6">
          <AnimatePresence>
            {generatedContent && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className="bg-white rounded-xl shadow-sm border p-6 space-y-4"
              >
                <div className="flex items-center justify-between">
                  <h2 className="text-xl font-semibold flex items-center space-x-2">
                    <Lightbulb className="w-5 h-5 text-yellow-500" />
                    <span>Generated Content</span>
                  </h2>
                  
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={handleOptimize}
                      disabled={isGenerating}
                      className="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition-colors"
                    >
                      Optimize
                    </button>
                    <button
                      onClick={handleCopy}
                      className="p-2 text-gray-500 hover:text-gray-700 transition-colors"
                    >
                      {copied ? <Check className="w-4 h-4 text-green-500" /> : <Copy className="w-4 h-4" />}
                    </button>
                  </div>
                </div>

                {/* Content */}
                <div className="bg-gray-50 rounded-lg p-4">
                  <p className="text-gray-800 whitespace-pre-wrap leading-relaxed">
                    {generatedContent.content}
                  </p>
                </div>

                {/* Hashtags */}
                {generatedContent.hashtags && generatedContent.hashtags.length > 0 && (
                  <div>
                    <h3 className="text-sm font-medium text-gray-700 mb-2 flex items-center space-x-1">
                      <Hash className="w-4 h-4" />
                      <span>Suggested Hashtags</span>
                    </h3>
                    <div className="flex flex-wrap gap-2">
                      {generatedContent.hashtags.map((hashtag, index) => (
                        <span
                          key={index}
                          className="px-2 py-1 bg-blue-100 text-blue-700 text-sm rounded-md"
                        >
                          #{hashtag}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {/* Metadata */}
                <div className="grid grid-cols-2 gap-4 pt-4 border-t">
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Confidence:</span>
                      <span className="font-medium">{Math.round(generatedContent.confidence * 100)}%</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Word Count:</span>
                      <span className="font-medium">{generatedContent.metadata.wordCount}</span>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Characters:</span>
                      <span className="font-medium">{generatedContent.metadata.characterCount}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Est. Engagement:</span>
                      <span className="font-medium">{Math.round(generatedContent.metadata.estimatedEngagement * 100)}%</span>
                    </div>
                  </div>
                </div>

                {/* Suggestions */}
                {generatedContent.suggestions && generatedContent.suggestions.length > 0 && (
                  <div>
                    <h3 className="text-sm font-medium text-gray-700 mb-2">Optimization Suggestions</h3>
                    <ul className="space-y-1">
                      {generatedContent.suggestions.map((suggestion, index) => (
                        <li key={index} className="text-sm text-gray-600 flex items-start space-x-2">
                          <span className="text-blue-500 mt-1">•</span>
                          <span>{suggestion}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </motion.div>
            )}
          </AnimatePresence>

          {/* Placeholder when no content */}
          {!generatedContent && (
            <div className="bg-gray-50 rounded-xl border-2 border-dashed border-gray-300 p-12 text-center">
              <Wand2 className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Ready to Create</h3>
              <p className="text-gray-600">
                Fill in the parameters and click "Generate Content" to see AI-powered content appear here.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AIContentGenerator;
