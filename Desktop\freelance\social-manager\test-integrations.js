const axios = require('axios');

async function testIntegrations() {
  try {
    console.log('🔐 Getting auth token...');
    
    // Login first
    const loginResponse = await axios.post('http://localhost:5000/api/auth/login', {
      email: '<EMAIL>',
      password: 'password123'
    });
    
    const token = loginResponse.data.data.token;
    console.log('✅ Login successful!');
    
    // Test integrations endpoint
    console.log('\n🔗 Testing integrations API...');
    const integrationsResponse = await axios.get('http://localhost:5000/api/integrations', {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    if (integrationsResponse.data.success) {
      console.log('✅ Integrations API working!');
      console.log('Integrations found:', integrationsResponse.data.data.length);
    } else {
      console.log('❌ Integrations API failed');
    }
    
    // Test platform capabilities
    console.log('\n📱 Testing platform capabilities...');
    const capabilitiesResponse = await axios.get('http://localhost:5000/api/integrations/platforms/INSTAGRAM/capabilities', {
      headers: { Authorization: `Bear<PERSON> ${token}` }
    });
    
    if (capabilitiesResponse.data.success) {
      console.log('✅ Platform capabilities working!');
      console.log('Instagram capabilities:', capabilitiesResponse.data.data.contentTypes);
    } else {
      console.log('❌ Platform capabilities failed');
    }
    
    // Test connecting a new integration
    console.log('\n🔌 Testing integration connection...');
    const connectResponse = await axios.post('http://localhost:5000/api/integrations/connect', {
      platform: 'INSTAGRAM',
      accountName: 'test_account',
      accountId: 'test_123',
      accessToken: 'test_token_123',
      refreshToken: 'refresh_token_123',
      expiresAt: '2024-12-31T23:59:59Z'
    }, {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    if (connectResponse.data.success) {
      console.log('✅ Integration connection working!');
      console.log('Connected account:', connectResponse.data.data.accountName);
      
      const integrationId = connectResponse.data.data.id;
      
      // Test integration test endpoint
      console.log('\n🧪 Testing integration test...');
      const testResponse = await axios.post(`http://localhost:5000/api/integrations/${integrationId}/test`, {}, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      if (testResponse.data.success) {
        console.log('✅ Integration test working!');
        console.log('Connection status:', testResponse.data.data.connected);
      } else {
        console.log('❌ Integration test failed');
      }
      
      // Clean up - disconnect the test integration
      console.log('\n🧹 Cleaning up test integration...');
      await axios.delete(`http://localhost:5000/api/integrations/${integrationId}`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      console.log('✅ Test integration cleaned up');
      
    } else {
      console.log('❌ Integration connection failed');
    }
    
    console.log('\n🎉 All integrations tests completed!');
    
  } catch (error) {
    console.log('❌ Error:', error.response?.data?.error?.message || error.message);
    console.log('Status:', error.response?.status);
  }
}

testIntegrations();
