import { apiClient } from './client';

export interface SocialMediaAccount {
  id: string;
  platform: 'INSTAGRAM' | 'FACEBOOK' | 'TWITTER' | 'LINKEDIN' | 'TIKTOK' | 'YOUTUBE' | 'PINTEREST';
  accountId: string;
  accountName: string;
  isActive: boolean;
  settings: Record<string, any>;
}

export interface PublishRequest {
  content: string;
  mediaUrls?: string[];
  platforms: string[];
  scheduledAt?: string;
}

export interface ScheduleRequest {
  content: string;
  mediaUrls?: string[];
  platforms: string[];
  scheduledAt: string;
  timezone?: string;
  recurring?: {
    frequency: 'DAILY' | 'WEEKLY' | 'MONTHLY';
    interval: number;
    endDate?: string;
    daysOfWeek?: number[];
    timeOfDay: string;
  };
}

export interface BulkScheduleRequest {
  contents: {
    content: string;
    mediaUrls?: string[];
    platforms: string[];
  }[];
  schedule: {
    startDate: string;
    endDate?: string;
    frequency: 'HOURLY' | 'DAILY' | 'WEEKLY';
    interval: number;
    optimalTiming: boolean;
  };
}

export interface ScheduledContent {
  id: string;
  content: string;
  mediaUrls?: string[];
  platforms: string[];
  scheduledAt: string;
  status: 'SCHEDULED' | 'PUBLISHED' | 'FAILED';
  timezone: string;
  recurring?: any;
}

export interface OptimalTiming {
  platform: string;
  bestTimes: {
    dayOfWeek: number;
    hour: number;
    engagementScore: number;
  }[];
  audienceTimezone: string;
  peakEngagementHours: number[];
}

export interface ContentAnalytics {
  likes: number;
  shares: number;
  comments: number;
  views: number;
  clicks: number;
  reach: number;
  impressions: number;
  engagementRate: number;
}

export interface QueueStats {
  publishQueue: {
    waiting: number;
    active: number;
    completed: number;
    failed: number;
    delayed: number;
  };
  analyticsQueue: {
    waiting: number;
    active: number;
    completed: number;
    failed: number;
    delayed: number;
  };
}

export const socialMediaApi = {
  // Account Management
  async connectAccount(platform: string, authCode: string) {
    const response = await apiClient.post('/social-media/connect', {
      platform,
      authCode
    });
    return response.data;
  },

  async getConnectedAccounts(): Promise<SocialMediaAccount[]> {
    const response = await apiClient.get('/social-media/accounts');
    return response.data.data;
  },

  async disconnectAccount(accountId: string) {
    const response = await apiClient.delete(`/social-media/accounts/${accountId}`);
    return response.data;
  },

  // Content Publishing
  async publishContent(request: PublishRequest) {
    const response = await apiClient.post('/social-media/publish', request);
    return response.data;
  },

  async scheduleContent(request: ScheduleRequest) {
    const response = await apiClient.post('/social-media/schedule', request);
    return response.data;
  },

  async bulkScheduleContent(request: BulkScheduleRequest) {
    const response = await apiClient.post('/social-media/bulk-schedule', request);
    return response.data;
  },

  // Scheduled Content Management
  async getScheduledContent(startDate?: string, endDate?: string): Promise<ScheduledContent[]> {
    const params = new URLSearchParams();
    if (startDate) params.append('startDate', startDate);
    if (endDate) params.append('endDate', endDate);
    
    const response = await apiClient.get(`/social-media/scheduled?${params.toString()}`);
    return response.data.data;
  },

  async rescheduleContent(contentId: string, scheduledAt: string) {
    const response = await apiClient.put(`/social-media/scheduled/${contentId}`, {
      scheduledAt
    });
    return response.data;
  },

  async cancelScheduledContent(contentId: string) {
    const response = await apiClient.delete(`/social-media/scheduled/${contentId}`);
    return response.data;
  },

  // Analytics & Insights
  async getOptimalPostingTimes(platforms?: string[]): Promise<OptimalTiming[]> {
    const params = new URLSearchParams();
    if (platforms && platforms.length > 0) {
      params.append('platforms', platforms.join(','));
    }
    
    const response = await apiClient.get(`/social-media/optimal-times?${params.toString()}`);
    return response.data.data;
  },

  async getContentAnalytics(contentId: string): Promise<ContentAnalytics> {
    const response = await apiClient.get(`/social-media/analytics/${contentId}`);
    return response.data.data;
  },

  async getQueueStats(): Promise<QueueStats> {
    const response = await apiClient.get('/social-media/queue-stats');
    return response.data.data;
  },

  // OAuth URLs (these would be generated by the backend)
  getOAuthUrl(platform: string): string {
    const baseUrl = import.meta.env.VITE_API_URL || 'http://localhost:5000';
    return `${baseUrl}/api/social-media/auth/${platform.toLowerCase()}`;
  },

  // Platform-specific helpers
  getPlatformDisplayName(platform: string): string {
    const names: Record<string, string> = {
      INSTAGRAM: 'Instagram',
      FACEBOOK: 'Facebook',
      TWITTER: 'Twitter',
      LINKEDIN: 'LinkedIn',
      TIKTOK: 'TikTok',
      YOUTUBE: 'YouTube',
      PINTEREST: 'Pinterest'
    };
    return names[platform] || platform;
  },

  getPlatformIcon(platform: string): string {
    const icons: Record<string, string> = {
      INSTAGRAM: '📷',
      FACEBOOK: '👥',
      TWITTER: '🐦',
      LINKEDIN: '💼',
      TIKTOK: '🎵',
      YOUTUBE: '📺',
      PINTEREST: '📌'
    };
    return icons[platform] || '📱';
  },

  getPlatformColor(platform: string): string {
    const colors: Record<string, string> = {
      INSTAGRAM: '#E4405F',
      FACEBOOK: '#1877F2',
      TWITTER: '#1DA1F2',
      LINKEDIN: '#0A66C2',
      TIKTOK: '#000000',
      YOUTUBE: '#FF0000',
      PINTEREST: '#BD081C'
    };
    return colors[platform] || '#6B7280';
  },

  // Content optimization helpers
  getOptimalContentLength(platform: string): { min: number; max: number; recommended: number } {
    const lengths: Record<string, { min: number; max: number; recommended: number }> = {
      INSTAGRAM: { min: 1, max: 2200, recommended: 125 },
      FACEBOOK: { min: 1, max: 63206, recommended: 80 },
      TWITTER: { min: 1, max: 280, recommended: 100 },
      LINKEDIN: { min: 1, max: 3000, recommended: 150 },
      TIKTOK: { min: 1, max: 150, recommended: 100 },
      YOUTUBE: { min: 1, max: 5000, recommended: 200 },
      PINTEREST: { min: 1, max: 500, recommended: 100 }
    };
    return lengths[platform] || { min: 1, max: 1000, recommended: 100 };
  },

  getRecommendedHashtagCount(platform: string): { min: number; max: number; recommended: number } {
    const counts: Record<string, { min: number; max: number; recommended: number }> = {
      INSTAGRAM: { min: 3, max: 30, recommended: 11 },
      FACEBOOK: { min: 1, max: 5, recommended: 2 },
      TWITTER: { min: 1, max: 2, recommended: 1 },
      LINKEDIN: { min: 1, max: 5, recommended: 3 },
      TIKTOK: { min: 3, max: 5, recommended: 4 },
      YOUTUBE: { min: 5, max: 15, recommended: 10 },
      PINTEREST: { min: 2, max: 20, recommended: 8 }
    };
    return counts[platform] || { min: 1, max: 10, recommended: 5 };
  },

  // Validation helpers
  validateContentForPlatform(content: string, platform: string): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    const lengths = this.getOptimalContentLength(platform);

    if (content.length < lengths.min) {
      errors.push(`Content too short for ${platform}. Minimum ${lengths.min} characters.`);
    }

    if (content.length > lengths.max) {
      errors.push(`Content too long for ${platform}. Maximum ${lengths.max} characters.`);
    }

    // Platform-specific validations
    if (platform === 'TWITTER' && content.length > 280) {
      errors.push('Twitter posts cannot exceed 280 characters.');
    }

    if (platform === 'INSTAGRAM' && !content.includes('#') && content.length > 50) {
      errors.push('Instagram posts should include hashtags for better reach.');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
};
