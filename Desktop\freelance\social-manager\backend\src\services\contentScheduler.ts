import Bull from 'bull';
import { PrismaClient } from '@prisma/client';
import { SocialMediaIntegrationService } from './socialMediaIntegrations.js';
import { logger } from '../utils/logger.js';
import { CacheService } from '../config/redis.js';

export interface ScheduledContent {
  id: string;
  content: string;
  mediaUrls?: string[];
  platforms: string[];
  scheduledAt: Date;
  organizationId: string;
  userId: string;
  timezone: string;
  recurring?: RecurringSchedule;
}

export interface RecurringSchedule {
  frequency: 'DAILY' | 'WEEKLY' | 'MONTHLY';
  interval: number;
  endDate?: Date;
  daysOfWeek?: number[]; // 0-6, Sunday = 0
  timeOfDay: string; // HH:MM format
}

export interface OptimalTimingAnalysis {
  platform: string;
  bestTimes: {
    dayOfWeek: number;
    hour: number;
    engagementScore: number;
  }[];
  audienceTimezone: string;
  peakEngagementHours: number[];
}

export interface BulkScheduleRequest {
  contents: {
    content: string;
    mediaUrls?: string[];
    platforms: string[];
  }[];
  schedule: {
    startDate: Date;
    endDate?: Date;
    frequency: 'HOURLY' | 'DAILY' | 'WEEKLY';
    interval: number;
    optimalTiming: boolean;
  };
  organizationId: string;
  userId: string;
}

export class ContentSchedulerService {
  private publishQueue: Bull.Queue;
  private analyticsQueue: Bull.Queue;
  private prisma: PrismaClient;
  private socialMediaService: SocialMediaIntegrationService;

  constructor(prisma: PrismaClient, socialMediaService: SocialMediaIntegrationService) {
    this.prisma = prisma;
    this.socialMediaService = socialMediaService;
    
    // Initialize Bull queues
    this.publishQueue = new Bull('content publishing', {
      redis: {
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT || '6379'),
        password: process.env.REDIS_PASSWORD
      },
      defaultJobOptions: {
        removeOnComplete: 100,
        removeOnFail: 50,
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000
        }
      }
    });

    this.analyticsQueue = new Bull('analytics collection', {
      redis: {
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT || '6379'),
        password: process.env.REDIS_PASSWORD
      }
    });

    this.setupQueueProcessors();
  }

  /**
   * Schedule content for publishing
   */
  async scheduleContent(request: ScheduledContent): Promise<string> {
    try {
      // Create content record in database
      const content = await this.prisma.contentItem.create({
        data: {
          title: request.content.substring(0, 100),
          description: request.content,
          type: 'POST',
          platforms: JSON.stringify(request.platforms),
          contentData: JSON.stringify({
            text: request.content,
            mediaUrls: request.mediaUrls || []
          }),
          status: 'SCHEDULED',
          scheduledAt: request.scheduledAt,
          projectId: await this.getDefaultProjectId(request.organizationId),
          createdById: request.userId,
          metadata: JSON.stringify({
            timezone: request.timezone,
            recurring: request.recurring
          })
        }
      });

      // Add job to publishing queue
      const jobId = await this.addPublishJob(content.id, request.scheduledAt);

      // Handle recurring schedule
      if (request.recurring) {
        await this.scheduleRecurringContent(content.id, request.recurring, request.scheduledAt);
      }

      logger.info(`Scheduled content ${content.id} for publishing at ${request.scheduledAt}`);
      return content.id;
    } catch (error) {
      logger.error('Error scheduling content:', error);
      throw new Error('Failed to schedule content');
    }
  }

  /**
   * Bulk schedule multiple content items
   */
  async bulkScheduleContent(request: BulkScheduleRequest): Promise<string[]> {
    try {
      const scheduledContentIds: string[] = [];
      let currentDate = new Date(request.schedule.startDate);

      for (let i = 0; i < request.contents.length; i++) {
        const contentItem = request.contents[i];
        
        // Calculate optimal timing if requested
        let scheduledAt = currentDate;
        if (request.schedule.optimalTiming) {
          scheduledAt = await this.calculateOptimalTiming(
            contentItem.platforms,
            currentDate,
            request.organizationId
          );
        }

        // Schedule individual content
        const contentId = await this.scheduleContent({
          id: '', // Will be generated
          content: contentItem.content,
          mediaUrls: contentItem.mediaUrls,
          platforms: contentItem.platforms,
          scheduledAt,
          organizationId: request.organizationId,
          userId: request.userId,
          timezone: 'UTC' // Default timezone
        });

        scheduledContentIds.push(contentId);

        // Calculate next schedule time
        currentDate = this.calculateNextScheduleTime(
          currentDate,
          request.schedule.frequency,
          request.schedule.interval
        );

        // Check if we've reached the end date
        if (request.schedule.endDate && currentDate > request.schedule.endDate) {
          break;
        }
      }

      logger.info(`Bulk scheduled ${scheduledContentIds.length} content items`);
      return scheduledContentIds;
    } catch (error) {
      logger.error('Error bulk scheduling content:', error);
      throw new Error('Failed to bulk schedule content');
    }
  }

  /**
   * Get optimal posting times for platforms
   */
  async getOptimalPostingTimes(
    platforms: string[],
    organizationId: string
  ): Promise<OptimalTimingAnalysis[]> {
    try {
      const analyses: OptimalTimingAnalysis[] = [];

      for (const platform of platforms) {
        // Get historical engagement data
        const engagementData = await this.getHistoricalEngagement(platform, organizationId);
        
        // Analyze optimal times
        const analysis = await this.analyzeOptimalTimes(platform, engagementData);
        analyses.push(analysis);
      }

      return analyses;
    } catch (error) {
      logger.error('Error getting optimal posting times:', error);
      throw new Error('Failed to get optimal posting times');
    }
  }

  /**
   * Cancel scheduled content
   */
  async cancelScheduledContent(contentId: string): Promise<void> {
    try {
      // Update content status
      await this.prisma.contentItem.update({
        where: { id: contentId },
        data: { status: 'CANCELLED' }
      });

      // Remove from queue
      const jobs = await this.publishQueue.getJobs(['delayed', 'waiting']);
      for (const job of jobs) {
        if (job.data.contentId === contentId) {
          await job.remove();
          logger.info(`Removed job ${job.id} for content ${contentId}`);
        }
      }

      logger.info(`Cancelled scheduled content: ${contentId}`);
    } catch (error) {
      logger.error('Error cancelling scheduled content:', error);
      throw new Error('Failed to cancel scheduled content');
    }
  }

  /**
   * Reschedule content
   */
  async rescheduleContent(contentId: string, newScheduledAt: Date): Promise<void> {
    try {
      // Cancel existing schedule
      await this.cancelScheduledContent(contentId);

      // Update database
      await this.prisma.contentItem.update({
        where: { id: contentId },
        data: {
          scheduledAt: newScheduledAt,
          status: 'SCHEDULED'
        }
      });

      // Add new job
      await this.addPublishJob(contentId, newScheduledAt);

      logger.info(`Rescheduled content ${contentId} to ${newScheduledAt}`);
    } catch (error) {
      logger.error('Error rescheduling content:', error);
      throw new Error('Failed to reschedule content');
    }
  }

  /**
   * Get scheduled content for organization
   */
  async getScheduledContent(
    organizationId: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<ScheduledContent[]> {
    try {
      const whereClause: any = {
        project: {
          organizationId
        },
        status: 'SCHEDULED'
      };

      if (startDate || endDate) {
        whereClause.scheduledAt = {};
        if (startDate) whereClause.scheduledAt.gte = startDate;
        if (endDate) whereClause.scheduledAt.lte = endDate;
      }

      const contents = await this.prisma.contentItem.findMany({
        where: whereClause,
        include: {
          createdBy: {
            select: { id: true, name: true, email: true }
          }
        },
        orderBy: { scheduledAt: 'asc' }
      });

      return contents.map(content => ({
        id: content.id,
        content: content.description,
        mediaUrls: JSON.parse(content.contentData || '{}').mediaUrls || [],
        platforms: JSON.parse(content.platforms),
        scheduledAt: content.scheduledAt!,
        organizationId,
        userId: content.createdById,
        timezone: JSON.parse(content.metadata || '{}').timezone || 'UTC',
        recurring: JSON.parse(content.metadata || '{}').recurring
      }));
    } catch (error) {
      logger.error('Error getting scheduled content:', error);
      throw new Error('Failed to get scheduled content');
    }
  }

  /**
   * Setup queue processors
   */
  private setupQueueProcessors(): void {
    // Content publishing processor
    this.publishQueue.process('publish-content', async (job) => {
      const { contentId } = job.data;
      
      try {
        // Get content details
        const content = await this.prisma.contentItem.findUnique({
          where: { id: contentId },
          include: { project: true }
        });

        if (!content) {
          throw new Error(`Content ${contentId} not found`);
        }

        const contentData = JSON.parse(content.contentData || '{}');
        const platforms = JSON.parse(content.platforms);

        // Publish to social media platforms
        const publishResult = await this.socialMediaService.publishContent({
          content: content.description,
          mediaUrls: contentData.mediaUrls || [],
          platforms,
          organizationId: content.project.organizationId,
          userId: content.createdById
        });

        // Update content status
        await this.prisma.contentItem.update({
          where: { id: contentId },
          data: {
            status: 'PUBLISHED',
            publishedAt: new Date()
          }
        });

        // Schedule analytics collection
        await this.scheduleAnalyticsCollection(contentId);

        logger.info(`Successfully published content: ${contentId}`);
        return publishResult;
      } catch (error) {
        logger.error(`Error publishing content ${contentId}:`, error);
        
        // Update content status to failed
        await this.prisma.contentItem.update({
          where: { id: contentId },
          data: { status: 'FAILED' }
        });

        throw error;
      }
    });

    // Analytics collection processor
    this.analyticsQueue.process('collect-analytics', async (job) => {
      const { contentId } = job.data;
      
      try {
        const metrics = await this.socialMediaService.getContentAnalytics(contentId);
        
        // Store analytics data
        await this.prisma.contentAnalytics.upsert({
          where: { contentId },
          update: {
            metrics: JSON.stringify(metrics),
            lastUpdated: new Date()
          },
          create: {
            contentId,
            metrics: JSON.stringify(metrics),
            lastUpdated: new Date()
          }
        });

        logger.info(`Collected analytics for content: ${contentId}`);
      } catch (error) {
        logger.error(`Error collecting analytics for content ${contentId}:`, error);
        throw error;
      }
    });
  }

  /**
   * Helper methods
   */
  private async addPublishJob(contentId: string, scheduledAt: Date): Promise<string> {
    const delay = scheduledAt.getTime() - Date.now();

    const job = await this.publishQueue.add(
      'publish-content',
      { contentId },
      {
        delay: Math.max(0, delay),
        jobId: `publish-${contentId}-${scheduledAt.getTime()}`
      }
    );

    return job.id?.toString() || '';
  }

  private async scheduleRecurringContent(
    contentId: string,
    recurring: RecurringSchedule,
    initialDate: Date
  ): Promise<void> {
    const endDate = recurring.endDate || new Date(Date.now() + 365 * 24 * 60 * 60 * 1000); // 1 year default
    let currentDate = new Date(initialDate);

    while (currentDate <= endDate) {
      currentDate = this.calculateNextRecurringDate(currentDate, recurring);

      if (currentDate <= endDate) {
        // Create new content instance for recurring schedule
        const originalContent = await this.prisma.contentItem.findUnique({
          where: { id: contentId }
        });

        if (originalContent) {
          const newContent = await this.prisma.contentItem.create({
            data: {
              title: originalContent.title,
              description: originalContent.description,
              type: originalContent.type,
              platforms: originalContent.platforms,
              contentData: originalContent.contentData,
              status: 'SCHEDULED',
              scheduledAt: currentDate,
              projectId: originalContent.projectId,
              createdById: originalContent.createdById,
              metadata: originalContent.metadata
            }
          });

          await this.addPublishJob(newContent.id, currentDate);
        }
      }
    }
  }

  private calculateNextRecurringDate(currentDate: Date, recurring: RecurringSchedule): Date {
    const nextDate = new Date(currentDate);

    switch (recurring.frequency) {
      case 'DAILY':
        nextDate.setDate(nextDate.getDate() + recurring.interval);
        break;
      case 'WEEKLY':
        nextDate.setDate(nextDate.getDate() + (7 * recurring.interval));
        break;
      case 'MONTHLY':
        nextDate.setMonth(nextDate.getMonth() + recurring.interval);
        break;
    }

    // Adjust for specific days of week if specified
    if (recurring.daysOfWeek && recurring.daysOfWeek.length > 0) {
      while (!recurring.daysOfWeek.includes(nextDate.getDay())) {
        nextDate.setDate(nextDate.getDate() + 1);
      }
    }

    // Set specific time of day
    if (recurring.timeOfDay) {
      const [hours, minutes] = recurring.timeOfDay.split(':').map(Number);
      nextDate.setHours(hours, minutes, 0, 0);
    }

    return nextDate;
  }

  private calculateNextScheduleTime(
    currentDate: Date,
    frequency: 'HOURLY' | 'DAILY' | 'WEEKLY',
    interval: number
  ): Date {
    const nextDate = new Date(currentDate);

    switch (frequency) {
      case 'HOURLY':
        nextDate.setHours(nextDate.getHours() + interval);
        break;
      case 'DAILY':
        nextDate.setDate(nextDate.getDate() + interval);
        break;
      case 'WEEKLY':
        nextDate.setDate(nextDate.getDate() + (7 * interval));
        break;
    }

    return nextDate;
  }

  private async calculateOptimalTiming(
    platforms: string[],
    baseDate: Date,
    organizationId: string
  ): Promise<Date> {
    try {
      const optimalTimes = await this.getOptimalPostingTimes(platforms, organizationId);

      if (optimalTimes.length === 0) {
        return baseDate;
      }

      // Find the best time across all platforms
      const dayOfWeek = baseDate.getDay();
      let bestHour = baseDate.getHours();
      let maxScore = 0;

      for (const analysis of optimalTimes) {
        const dayTimes = analysis.bestTimes.filter(t => t.dayOfWeek === dayOfWeek);
        for (const time of dayTimes) {
          if (time.engagementScore > maxScore) {
            maxScore = time.engagementScore;
            bestHour = time.hour;
          }
        }
      }

      const optimalDate = new Date(baseDate);
      optimalDate.setHours(bestHour, 0, 0, 0);

      return optimalDate;
    } catch (error) {
      logger.error('Error calculating optimal timing:', error);
      return baseDate;
    }
  }

  private async getHistoricalEngagement(platform: string, organizationId: string): Promise<any[]> {
    // Get historical engagement data from analytics
    const analytics = await this.prisma.contentAnalytics.findMany({
      where: {
        content: {
          project: { organizationId },
          platforms: { contains: platform }
        }
      },
      include: {
        content: {
          select: {
            publishedAt: true,
            platforms: true
          }
        }
      },
      orderBy: { lastUpdated: 'desc' },
      take: 100
    });

    return analytics.map(a => ({
      publishedAt: a.content.publishedAt,
      metrics: JSON.parse(a.metrics || '{}'),
      platform
    }));
  }

  private async analyzeOptimalTimes(platform: string, engagementData: any[]): Promise<OptimalTimingAnalysis> {
    const timeSlots: { [key: string]: { total: number; count: number } } = {};

    // Analyze engagement by day of week and hour
    for (const data of engagementData) {
      if (!data.publishedAt) continue;

      const date = new Date(data.publishedAt);
      const dayOfWeek = date.getDay();
      const hour = date.getHours();
      const key = `${dayOfWeek}-${hour}`;

      const engagementRate = data.metrics.engagementRate || 0;

      if (!timeSlots[key]) {
        timeSlots[key] = { total: 0, count: 0 };
      }

      timeSlots[key].total += engagementRate;
      timeSlots[key].count += 1;
    }

    // Calculate average engagement for each time slot
    const bestTimes = Object.entries(timeSlots)
      .map(([key, data]) => {
        const [dayOfWeek, hour] = key.split('-').map(Number);
        return {
          dayOfWeek,
          hour,
          engagementScore: data.total / data.count
        };
      })
      .sort((a, b) => b.engagementScore - a.engagementScore)
      .slice(0, 10); // Top 10 best times

    // Find peak engagement hours
    const hourlyEngagement: { [hour: number]: number } = {};
    for (const time of bestTimes) {
      hourlyEngagement[time.hour] = (hourlyEngagement[time.hour] || 0) + time.engagementScore;
    }

    const peakEngagementHours = Object.entries(hourlyEngagement)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([hour]) => Number(hour));

    return {
      platform,
      bestTimes,
      audienceTimezone: 'UTC', // Default, could be determined from audience data
      peakEngagementHours
    };
  }

  private async scheduleAnalyticsCollection(contentId: string): Promise<void> {
    // Schedule analytics collection at intervals
    const delays = [
      60 * 60 * 1000,      // 1 hour
      24 * 60 * 60 * 1000, // 1 day
      7 * 24 * 60 * 60 * 1000, // 1 week
      30 * 24 * 60 * 60 * 1000 // 1 month
    ];

    for (const delay of delays) {
      await this.analyticsQueue.add(
        'collect-analytics',
        { contentId },
        { delay }
      );
    }
  }

  private async getDefaultProjectId(organizationId: string): Promise<string> {
    const project = await this.prisma.project.findFirst({
      where: { organizationId },
      orderBy: { createdAt: 'desc' }
    });

    if (!project) {
      const defaultProject = await this.prisma.project.create({
        data: {
          name: 'Default Project',
          description: 'Default project for social media posts',
          organizationId,
          status: 'ACTIVE'
        }
      });
      return defaultProject.id;
    }

    return project.id;
  }

  /**
   * Get queue statistics
   */
  async getQueueStats(): Promise<{
    publishQueue: any;
    analyticsQueue: any;
  }> {
    const publishStats = {
      waiting: await this.publishQueue.getWaiting(),
      active: await this.publishQueue.getActive(),
      completed: await this.publishQueue.getCompleted(),
      failed: await this.publishQueue.getFailed(),
      delayed: await this.publishQueue.getDelayed()
    };

    const analyticsStats = {
      waiting: await this.analyticsQueue.getWaiting(),
      active: await this.analyticsQueue.getActive(),
      completed: await this.analyticsQueue.getCompleted(),
      failed: await this.analyticsQueue.getFailed(),
      delayed: await this.analyticsQueue.getDelayed()
    };

    return {
      publishQueue: {
        waiting: publishStats.waiting.length,
        active: publishStats.active.length,
        completed: publishStats.completed.length,
        failed: publishStats.failed.length,
        delayed: publishStats.delayed.length
      },
      analyticsQueue: {
        waiting: analyticsStats.waiting.length,
        active: analyticsStats.active.length,
        completed: analyticsStats.completed.length,
        failed: analyticsStats.failed.length,
        delayed: analyticsStats.delayed.length
      }
    };
  }
}
