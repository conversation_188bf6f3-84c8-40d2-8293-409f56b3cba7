import { PrismaClient } from '@prisma/client';
import { logger } from '../utils/logger.js';
import { AIContentGenerator } from './aiContentGenerator.js';
import { RecommendationEngine } from './recommendationEngine.js';
import { PredictiveAnalyticsService } from './predictiveAnalytics.js';
import { AutomationEngine } from './automationEngine.js';
import { SentimentAnalysisService } from './sentimentAnalysis.js';
import { AdvancedAnalyticsService } from './advancedAnalytics.js';
import { AIModelOptimizer } from './aiModelOptimizer.js';
import { AuditLogger, AuditAction, AuditSeverity } from './auditLogger.js';

export interface AIServiceConfig {
  enableContentGeneration: boolean;
  enableRecommendations: boolean;
  enablePredictiveAnalytics: boolean;
  enableAutomation: boolean;
  enableSentimentAnalysis: boolean;
  enableAdvancedAnalytics: boolean;
  enableModelOptimization: boolean;
  organizationId: string;
  userId: string;
}

export interface AIInsight {
  type: 'content' | 'performance' | 'audience' | 'trend' | 'optimization';
  title: string;
  description: string;
  confidence: number;
  impact: 'low' | 'medium' | 'high';
  actionable: boolean;
  data: any;
  generatedAt: Date;
}

export interface AIWorkflow {
  id: string;
  name: string;
  description: string;
  steps: AIWorkflowStep[];
  triggers: string[];
  isActive: boolean;
  organizationId: string;
}

export interface AIWorkflowStep {
  id: string;
  type: 'generate_content' | 'analyze_sentiment' | 'predict_performance' | 'optimize_content' | 'send_notification';
  config: any;
  dependencies: string[];
}

export interface AIPerformanceReport {
  organizationId: string;
  period: string;
  metrics: {
    contentGenerated: number;
    recommendationsProvided: number;
    predictionsAccuracy: number;
    automationExecutions: number;
    sentimentAnalyzed: number;
    modelsOptimized: number;
  };
  insights: AIInsight[];
  recommendations: string[];
  roi: number;
  generatedAt: Date;
}

export class AIIntegrationService {
  private prisma: PrismaClient;
  private contentGenerator: AIContentGenerator;
  private recommendationEngine: RecommendationEngine;
  private predictiveAnalytics: PredictiveAnalyticsService;
  private automationEngine: AutomationEngine;
  private sentimentAnalysis: SentimentAnalysisService;
  private advancedAnalytics: AdvancedAnalyticsService;
  private modelOptimizer: AIModelOptimizer;
  private auditLogger: AuditLogger;

  constructor(prisma: PrismaClient, auditLogger: AuditLogger) {
    this.prisma = prisma;
    this.auditLogger = auditLogger;
    
    // Initialize AI services
    this.contentGenerator = new AIContentGenerator(prisma);
    this.recommendationEngine = new RecommendationEngine(prisma);
    this.predictiveAnalytics = new PredictiveAnalyticsService(prisma);
    this.sentimentAnalysis = new SentimentAnalysisService(prisma);
    this.advancedAnalytics = new AdvancedAnalyticsService(prisma);
    this.modelOptimizer = new AIModelOptimizer(prisma);
    
    this.automationEngine = new AutomationEngine(
      prisma,
      this.contentGenerator,
      this.recommendationEngine,
      this.predictiveAnalytics
    );
  }

  /**
   * Initialize AI services for an organization
   */
  async initializeAIServices(config: AIServiceConfig): Promise<void> {
    try {
      logger.info(`Initializing AI services for organization: ${config.organizationId}`);

      // Store AI configuration
      await this.storeAIConfiguration(config);

      // Initialize enabled services
      if (config.enableContentGeneration) {
        await this.initializeContentGeneration(config);
      }

      if (config.enableRecommendations) {
        await this.initializeRecommendations(config);
      }

      if (config.enablePredictiveAnalytics) {
        await this.initializePredictiveAnalytics(config);
      }

      if (config.enableAutomation) {
        await this.initializeAutomation(config);
      }

      if (config.enableSentimentAnalysis) {
        await this.initializeSentimentAnalysis(config);
      }

      if (config.enableAdvancedAnalytics) {
        await this.initializeAdvancedAnalytics(config);
      }

      if (config.enableModelOptimization) {
        await this.initializeModelOptimization(config);
      }

      // Log initialization
      await this.auditLogger.logSystem(
        AuditAction.SYSTEM_MAINTENANCE,
        {
          action: 'ai_services_initialized',
          organizationId: config.organizationId,
          enabledServices: this.getEnabledServices(config)
        }
      );

      logger.info(`AI services initialized successfully for organization: ${config.organizationId}`);
    } catch (error) {
      logger.error('Error initializing AI services:', error);
      throw error;
    }
  }

  /**
   * Generate comprehensive AI insights
   */
  async generateAIInsights(
    organizationId: string,
    userId: string,
    timeframe: number = 30
  ): Promise<AIInsight[]> {
    try {
      const insights: AIInsight[] = [];

      // Get content insights
      const contentInsights = await this.generateContentInsights(organizationId, timeframe);
      insights.push(...contentInsights);

      // Get performance insights
      const performanceInsights = await this.generatePerformanceInsights(organizationId, timeframe);
      insights.push(...performanceInsights);

      // Get audience insights
      const audienceInsights = await this.generateAudienceInsights(organizationId, timeframe);
      insights.push(...audienceInsights);

      // Get trend insights
      const trendInsights = await this.generateTrendInsights(organizationId, timeframe);
      insights.push(...trendInsights);

      // Get optimization insights
      const optimizationInsights = await this.generateOptimizationInsights(organizationId, timeframe);
      insights.push(...optimizationInsights);

      // Sort by impact and confidence
      insights.sort((a, b) => {
        const impactWeight = { high: 3, medium: 2, low: 1 };
        const aScore = impactWeight[a.impact] * a.confidence;
        const bScore = impactWeight[b.impact] * b.confidence;
        return bScore - aScore;
      });

      // Log insight generation
      await this.auditLogger.logDataAccess(
        AuditAction.DATA_EXPORTED,
        userId,
        organizationId,
        'ai_insights',
        'generated',
        { insightCount: insights.length, timeframe }
      );

      return insights.slice(0, 20); // Return top 20 insights
    } catch (error) {
      logger.error('Error generating AI insights:', error);
      throw error;
    }
  }

  /**
   * Execute AI workflow
   */
  async executeAIWorkflow(
    workflowId: string,
    organizationId: string,
    userId: string,
    context?: any
  ): Promise<{ success: boolean; results: any[]; errors: string[] }> {
    try {
      const workflow = await this.getAIWorkflow(workflowId, organizationId);
      if (!workflow) {
        throw new Error(`Workflow ${workflowId} not found`);
      }

      logger.info(`Executing AI workflow: ${workflow.name} for organization: ${organizationId}`);

      const results: any[] = [];
      const errors: string[] = [];

      // Execute workflow steps in order
      for (const step of workflow.steps) {
        try {
          const result = await this.executeWorkflowStep(step, organizationId, userId, context);
          results.push(result);
        } catch (stepError) {
          const errorMessage = `Step ${step.id} failed: ${stepError}`;
          errors.push(errorMessage);
          logger.error(errorMessage);
        }
      }

      // Log workflow execution
      await this.auditLogger.logSystem(
        AuditAction.BULK_OPERATION,
        {
          action: 'ai_workflow_executed',
          workflowId,
          organizationId,
          userId,
          stepCount: workflow.steps.length,
          successCount: results.length,
          errorCount: errors.length
        }
      );

      return {
        success: errors.length === 0,
        results,
        errors
      };
    } catch (error) {
      logger.error(`Error executing AI workflow ${workflowId}:`, error);
      throw error;
    }
  }

  /**
   * Generate AI performance report
   */
  async generateAIPerformanceReport(
    organizationId: string,
    period: string = '30d'
  ): Promise<AIPerformanceReport> {
    try {
      const startDate = this.calculateStartDate(period);

      // Gather metrics from all AI services
      const metrics = await this.gatherAIMetrics(organizationId, startDate);

      // Generate insights
      const insights = await this.generateAIInsights(organizationId, 'system', 30);

      // Generate recommendations
      const recommendations = await this.generateAIRecommendations(organizationId, metrics);

      // Calculate ROI
      const roi = await this.calculateAIROI(organizationId, startDate);

      const report: AIPerformanceReport = {
        organizationId,
        period,
        metrics,
        insights: insights.slice(0, 10), // Top 10 insights
        recommendations,
        roi,
        generatedAt: new Date()
      };

      // Store report
      await this.storeAIPerformanceReport(report);

      return report;
    } catch (error) {
      logger.error('Error generating AI performance report:', error);
      throw error;
    }
  }

  /**
   * Optimize AI services performance
   */
  async optimizeAIServices(organizationId: string): Promise<{
    optimizations: string[];
    improvements: { [key: string]: number };
    recommendations: string[];
  }> {
    try {
      const optimizations: string[] = [];
      const improvements: { [key: string]: number } = {};
      const recommendations: string[] = [];

      // Optimize content generation
      const contentOptimization = await this.optimizeContentGeneration(organizationId);
      optimizations.push('Content Generation Optimized');
      improvements['content_generation'] = contentOptimization.improvement;

      // Optimize recommendation engine
      const recommendationOptimization = await this.optimizeRecommendationEngine(organizationId);
      optimizations.push('Recommendation Engine Optimized');
      improvements['recommendations'] = recommendationOptimization.improvement;

      // Optimize predictive models
      const predictiveOptimization = await this.optimizePredictiveModels(organizationId);
      optimizations.push('Predictive Models Optimized');
      improvements['predictions'] = predictiveOptimization.improvement;

      // Generate optimization recommendations
      recommendations.push(...this.generateOptimizationRecommendations(improvements));

      logger.info(`AI services optimized for organization: ${organizationId}`);

      return {
        optimizations,
        improvements,
        recommendations
      };
    } catch (error) {
      logger.error('Error optimizing AI services:', error);
      throw error;
    }
  }

  /**
   * Private helper methods
   */
  private async storeAIConfiguration(config: AIServiceConfig): Promise<void> {
    // Store AI configuration in database
    await this.prisma.aiConfiguration.upsert({
      where: { organizationId: config.organizationId },
      create: {
        organizationId: config.organizationId,
        enableContentGeneration: config.enableContentGeneration,
        enableRecommendations: config.enableRecommendations,
        enablePredictiveAnalytics: config.enablePredictiveAnalytics,
        enableAutomation: config.enableAutomation,
        enableSentimentAnalysis: config.enableSentimentAnalysis,
        enableAdvancedAnalytics: config.enableAdvancedAnalytics,
        enableModelOptimization: config.enableModelOptimization,
        createdBy: config.userId,
        updatedBy: config.userId
      },
      update: {
        enableContentGeneration: config.enableContentGeneration,
        enableRecommendations: config.enableRecommendations,
        enablePredictiveAnalytics: config.enablePredictiveAnalytics,
        enableAutomation: config.enableAutomation,
        enableSentimentAnalysis: config.enableSentimentAnalysis,
        enableAdvancedAnalytics: config.enableAdvancedAnalytics,
        enableModelOptimization: config.enableModelOptimization,
        updatedBy: config.userId,
        updatedAt: new Date()
      }
    });
  }

  private getEnabledServices(config: AIServiceConfig): string[] {
    const services = [];
    if (config.enableContentGeneration) services.push('content_generation');
    if (config.enableRecommendations) services.push('recommendations');
    if (config.enablePredictiveAnalytics) services.push('predictive_analytics');
    if (config.enableAutomation) services.push('automation');
    if (config.enableSentimentAnalysis) services.push('sentiment_analysis');
    if (config.enableAdvancedAnalytics) services.push('advanced_analytics');
    if (config.enableModelOptimization) services.push('model_optimization');
    return services;
  }

  private async initializeContentGeneration(config: AIServiceConfig): Promise<void> {
    // Initialize content generation service
    logger.debug('Initializing content generation service');
  }

  private async initializeRecommendations(config: AIServiceConfig): Promise<void> {
    // Initialize recommendation engine
    logger.debug('Initializing recommendation engine');
  }

  private async initializePredictiveAnalytics(config: AIServiceConfig): Promise<void> {
    // Initialize predictive analytics
    logger.debug('Initializing predictive analytics');
  }

  private async initializeAutomation(config: AIServiceConfig): Promise<void> {
    // Initialize automation engine
    logger.debug('Initializing automation engine');
  }

  private async initializeSentimentAnalysis(config: AIServiceConfig): Promise<void> {
    // Initialize sentiment analysis
    logger.debug('Initializing sentiment analysis');
  }

  private async initializeAdvancedAnalytics(config: AIServiceConfig): Promise<void> {
    // Initialize advanced analytics
    logger.debug('Initializing advanced analytics');
  }

  private async initializeModelOptimization(config: AIServiceConfig): Promise<void> {
    // Initialize model optimization
    logger.debug('Initializing model optimization');
  }

  private async generateContentInsights(organizationId: string, timeframe: number): Promise<AIInsight[]> {
    // Generate content-related insights
    return [
      {
        type: 'content',
        title: 'Video Content Performing 40% Better',
        description: 'Video posts are generating significantly higher engagement than image posts',
        confidence: 0.92,
        impact: 'high',
        actionable: true,
        data: { contentType: 'video', improvement: 0.4 },
        generatedAt: new Date()
      }
    ];
  }

  private async generatePerformanceInsights(organizationId: string, timeframe: number): Promise<AIInsight[]> {
    // Generate performance-related insights
    return [
      {
        type: 'performance',
        title: 'Engagement Rate Trending Upward',
        description: 'Your engagement rate has increased by 15% over the past month',
        confidence: 0.88,
        impact: 'medium',
        actionable: false,
        data: { metric: 'engagement_rate', change: 0.15 },
        generatedAt: new Date()
      }
    ];
  }

  private async generateAudienceInsights(organizationId: string, timeframe: number): Promise<AIInsight[]> {
    // Generate audience-related insights
    return [];
  }

  private async generateTrendInsights(organizationId: string, timeframe: number): Promise<AIInsight[]> {
    // Generate trend-related insights
    return [];
  }

  private async generateOptimizationInsights(organizationId: string, timeframe: number): Promise<AIInsight[]> {
    // Generate optimization-related insights
    return [];
  }

  private async getAIWorkflow(workflowId: string, organizationId: string): Promise<AIWorkflow | null> {
    // Get AI workflow from database
    return null;
  }

  private async executeWorkflowStep(step: AIWorkflowStep, organizationId: string, userId: string, context?: any): Promise<any> {
    // Execute individual workflow step
    switch (step.type) {
      case 'generate_content':
        return await this.contentGenerator.generateContent(step.config, userId, organizationId);
      case 'analyze_sentiment':
        return await this.sentimentAnalysis.analyzeSentiment(step.config.content);
      case 'predict_performance':
        return await this.predictiveAnalytics.predictContentPerformance(step.config, organizationId);
      default:
        throw new Error(`Unknown workflow step type: ${step.type}`);
    }
  }

  private calculateStartDate(period: string): Date {
    const now = new Date();
    switch (period) {
      case '7d': return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      case '30d': return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      case '90d': return new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
      default: return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    }
  }

  private async gatherAIMetrics(organizationId: string, startDate: Date): Promise<any> {
    // Gather metrics from all AI services
    return {
      contentGenerated: 150,
      recommendationsProvided: 75,
      predictionsAccuracy: 0.87,
      automationExecutions: 45,
      sentimentAnalyzed: 200,
      modelsOptimized: 3
    };
  }

  private async generateAIRecommendations(organizationId: string, metrics: any): Promise<string[]> {
    // Generate AI recommendations based on metrics
    return [
      'Increase video content production to capitalize on higher engagement',
      'Optimize posting times based on audience activity patterns',
      'Implement automated sentiment monitoring for brand mentions'
    ];
  }

  private async calculateAIROI(organizationId: string, startDate: Date): Promise<number> {
    // Calculate ROI of AI services
    return 3.2; // 320% ROI
  }

  private async storeAIPerformanceReport(report: AIPerformanceReport): Promise<void> {
    // Store AI performance report in database
    logger.debug(`Storing AI performance report for organization: ${report.organizationId}`);
  }

  private async optimizeContentGeneration(organizationId: string): Promise<{ improvement: number }> {
    // Optimize content generation service
    return { improvement: 0.15 };
  }

  private async optimizeRecommendationEngine(organizationId: string): Promise<{ improvement: number }> {
    // Optimize recommendation engine
    return { improvement: 0.12 };
  }

  private async optimizePredictiveModels(organizationId: string): Promise<{ improvement: number }> {
    // Optimize predictive models
    return { improvement: 0.08 };
  }

  private generateOptimizationRecommendations(improvements: { [key: string]: number }): string[] {
    const recommendations = [];
    
    Object.entries(improvements).forEach(([service, improvement]) => {
      if (improvement > 0.1) {
        recommendations.push(`${service} optimization yielded ${(improvement * 100).toFixed(1)}% improvement`);
      }
    });

    return recommendations;
  }
}
