{"name": "socialhub-pro", "version": "1.0.0", "description": "Complete Social Media Management SaaS Platform", "private": true, "workspaces": ["frontend", "backend"], "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run dev", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "test": "npm run test:frontend && npm run test:backend", "test:frontend": "cd frontend && npm run test", "test:backend": "cd backend && npm run test", "lint": "npm run lint:frontend && npm run lint:backend", "lint:frontend": "cd frontend && npm run lint", "lint:backend": "cd backend && npm run lint", "docker:dev": "docker-compose -f docker-compose.dev.yml up", "docker:prod": "docker-compose -f docker-compose.prod.yml up", "setup": "npm install && npm run setup:frontend && npm run setup:backend", "setup:frontend": "cd frontend && npm install", "setup:backend": "cd backend && npm install", "db:migrate": "cd backend && npx prisma migrate dev", "db:generate": "cd backend && npx prisma generate", "db:seed": "cd backend && npx prisma db seed", "db:studio": "cd backend && npx prisma studio"}, "devDependencies": {"@types/node": "^20.10.0", "concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-org/socialhub-pro.git"}, "keywords": ["social-media", "saas", "content-management", "analytics", "collaboration", "react", "nodejs", "postgresql"], "author": "SocialHub Pro Team", "license": "MIT", "dependencies": {"axios": "^1.9.0"}}