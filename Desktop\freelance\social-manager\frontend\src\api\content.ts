import { apiClient } from './client'
import { ContentItem, CreateContentData, UpdateContentData } from '../types/content'
import { ApiResponse } from '../types/auth'

interface ContentFilters {
  projectId?: string
  status?: string
  type?: string
  platforms?: string
}

export const contentAPI = {
  // Get all content items
  getContentItems: (filters?: ContentFilters) => {
    const params = new URLSearchParams()
    if (filters?.projectId) params.append('projectId', filters.projectId)
    if (filters?.status) params.append('status', filters.status)
    if (filters?.type) params.append('type', filters.type)
    if (filters?.platforms) params.append('platforms', filters.platforms)
    
    const queryString = params.toString()
    return apiClient.get<ApiResponse<{ contentItems: ContentItem[] }>>(
      `/content${queryString ? `?${queryString}` : ''}`
    )
  },

  // Get a specific content item
  getContentItem: (id: string) => {
    return apiClient.get<ApiResponse<{ contentItem: ContentItem }>>(`/content/${id}`)
  },

  // Create a new content item
  createContentItem: (data: CreateContentData) => {
    return apiClient.post<ApiResponse<{ contentItem: ContentItem }>>('/content', data)
  },

  // Update a content item
  updateContentItem: (id: string, data: UpdateContentData) => {
    return apiClient.put<ApiResponse<{ contentItem: ContentItem }>>(`/content/${id}`, data)
  },

  // Delete a content item
  deleteContentItem: (id: string) => {
    return apiClient.delete<ApiResponse<{ message: string }>>(`/content/${id}`)
  },

  // Bulk update content status
  bulkUpdateStatus: (contentIds: string[], status: string) => {
    return apiClient.patch<ApiResponse<{ updatedCount: number }>>('/content/bulk-status', {
      contentIds,
      status,
    })
  },
}
