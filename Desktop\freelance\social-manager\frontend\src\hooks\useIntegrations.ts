import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { integrationsAPI, ConnectIntegrationData, UpdateIntegrationData } from '../api/integrations'
import toast from 'react-hot-toast'

export const useIntegrations = () => {
  return useQuery({
    queryKey: ['integrations'],
    queryFn: async () => {
      const response = await integrationsAPI.getIntegrations()
      return response.data.data
    },
  })
}

export const useConnectIntegration = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: ConnectIntegrationData) => integrationsAPI.connectIntegration(data),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ['integrations'] })
      toast.success(response.data.message || 'Integration connected successfully')
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error?.message || 'Failed to connect integration')
    },
  })
}

export const useUpdateIntegration = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateIntegrationData }) => 
      integrationsAPI.updateIntegration(id, data),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ['integrations'] })
      toast.success(response.data.message || 'Integration updated successfully')
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error?.message || 'Failed to update integration')
    },
  })
}

export const useDisconnectIntegration = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => integrationsAPI.disconnectIntegration(id),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ['integrations'] })
      toast.success(response.data.message || 'Integration disconnected successfully')
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error?.message || 'Failed to disconnect integration')
    },
  })
}

export const useRefreshToken = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => integrationsAPI.refreshToken(id),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ['integrations'] })
      toast.success(response.data.message || 'Token refreshed successfully')
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error?.message || 'Failed to refresh token')
    },
  })
}

export const useTestConnection = () => {
  return useMutation({
    mutationFn: (id: string) => integrationsAPI.testConnection(id),
    onSuccess: (response) => {
      toast.success(response.data.message || 'Connection test successful')
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error?.message || 'Connection test failed')
    },
  })
}

export const usePlatformCapabilities = (platform: string) => {
  return useQuery({
    queryKey: ['platform-capabilities', platform],
    queryFn: async () => {
      const response = await integrationsAPI.getPlatformCapabilities(platform)
      return response.data.data
    },
    enabled: !!platform,
  })
}
