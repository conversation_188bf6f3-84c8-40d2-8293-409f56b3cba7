import express from 'express';
import { PrismaClient } from '@prisma/client';
import { BillingService } from '../services/billingService.js';
import { authMiddleware } from '../middleware/auth.js';
import { logger } from '../utils/logger.js';
import Stripe from 'stripe';

const router = express.Router();
const prisma = new PrismaClient();
const billingService = new BillingService(prisma);
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2023-10-16'
});

/**
 * @route GET /api/billing/plans
 * @desc Get all subscription plans
 * @access Public
 */
router.get('/plans', async (req, res) => {
  try {
    const plans = await billingService.getSubscriptionPlans();

    res.json({
      success: true,
      data: plans
    });
  } catch (error) {
    logger.error('Error getting subscription plans:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get subscription plans'
    });
  }
});

/**
 * @route POST /api/billing/create-payment-intent
 * @desc Create payment intent for subscription
 * @access Private
 */
router.post('/create-payment-intent', authMiddleware, async (req, res) => {
  try {
    const { planId } = req.body;
    const { organizationId } = req.user;

    if (!planId) {
      return res.status(400).json({
        success: false,
        message: 'Plan ID is required'
      });
    }

    // Get plan details
    const plans = await billingService.getSubscriptionPlans();
    const plan = plans.find(p => p.id === planId);

    if (!plan) {
      return res.status(404).json({
        success: false,
        message: 'Plan not found'
      });
    }

    // Create payment intent
    const paymentIntent = await stripe.paymentIntents.create({
      amount: plan.price * 100, // Convert to cents
      currency: plan.currency,
      metadata: {
        organizationId,
        planId
      }
    });

    res.json({
      success: true,
      data: {
        clientSecret: paymentIntent.client_secret,
        amount: plan.price,
        currency: plan.currency
      }
    });
  } catch (error) {
    logger.error('Error creating payment intent:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create payment intent'
    });
  }
});

/**
 * @route POST /api/billing/subscribe
 * @desc Create subscription
 * @access Private
 */
router.post('/subscribe', authMiddleware, async (req, res) => {
  try {
    const { planId, paymentMethodId } = req.body;
    const { organizationId } = req.user;

    if (!planId || !paymentMethodId) {
      return res.status(400).json({
        success: false,
        message: 'Plan ID and payment method ID are required'
      });
    }

    const result = await billingService.createSubscription(
      organizationId,
      planId,
      paymentMethodId
    );

    res.json({
      success: true,
      message: 'Subscription created successfully',
      data: result
    });
  } catch (error) {
    logger.error('Error creating subscription:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create subscription'
    });
  }
});

/**
 * @route PUT /api/billing/subscription
 * @desc Update subscription plan
 * @access Private
 */
router.put('/subscription', authMiddleware, async (req, res) => {
  try {
    const { planId } = req.body;
    const { organizationId } = req.user;

    if (!planId) {
      return res.status(400).json({
        success: false,
        message: 'Plan ID is required'
      });
    }

    await billingService.updateSubscription(organizationId, planId);

    res.json({
      success: true,
      message: 'Subscription updated successfully'
    });
  } catch (error) {
    logger.error('Error updating subscription:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update subscription'
    });
  }
});

/**
 * @route DELETE /api/billing/subscription
 * @desc Cancel subscription
 * @access Private
 */
router.delete('/subscription', authMiddleware, async (req, res) => {
  try {
    const { immediate } = req.query;
    const { organizationId } = req.user;

    await billingService.cancelSubscription(organizationId, immediate === 'true');

    res.json({
      success: true,
      message: immediate === 'true' ? 'Subscription cancelled immediately' : 'Subscription will be cancelled at period end'
    });
  } catch (error) {
    logger.error('Error cancelling subscription:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to cancel subscription'
    });
  }
});

/**
 * @route GET /api/billing/subscription
 * @desc Get current subscription
 * @access Private
 */
router.get('/subscription', authMiddleware, async (req, res) => {
  try {
    const { organizationId } = req.user;

    const subscription = await billingService.getCurrentSubscription(organizationId);

    res.json({
      success: true,
      data: subscription
    });
  } catch (error) {
    logger.error('Error getting current subscription:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get current subscription'
    });
  }
});

/**
 * @route GET /api/billing/usage
 * @desc Get usage metrics
 * @access Private
 */
router.get('/usage', authMiddleware, async (req, res) => {
  try {
    const { organizationId } = req.user;
    const { period } = req.query;

    const usage = await billingService.getUsageMetrics(
      organizationId,
      period as string
    );

    res.json({
      success: true,
      data: usage
    });
  } catch (error) {
    logger.error('Error getting usage metrics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get usage metrics'
    });
  }
});

/**
 * @route GET /api/billing/invoices
 * @desc Get invoices
 * @access Private
 */
router.get('/invoices', authMiddleware, async (req, res) => {
  try {
    const { organizationId } = req.user;
    const { limit } = req.query;

    const invoices = await billingService.getInvoices(
      organizationId,
      limit ? parseInt(limit as string) : 10
    );

    res.json({
      success: true,
      data: invoices
    });
  } catch (error) {
    logger.error('Error getting invoices:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get invoices'
    });
  }
});

/**
 * @route GET /api/billing/payment-methods
 * @desc Get payment methods
 * @access Private
 */
router.get('/payment-methods', authMiddleware, async (req, res) => {
  try {
    const { organizationId } = req.user;

    const paymentMethods = await billingService.getPaymentMethods(organizationId);

    res.json({
      success: true,
      data: paymentMethods
    });
  } catch (error) {
    logger.error('Error getting payment methods:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get payment methods'
    });
  }
});

/**
 * @route POST /api/billing/payment-methods
 * @desc Add payment method
 * @access Private
 */
router.post('/payment-methods', authMiddleware, async (req, res) => {
  try {
    const { paymentMethodId } = req.body;
    const { organizationId } = req.user;

    if (!paymentMethodId) {
      return res.status(400).json({
        success: false,
        message: 'Payment method ID is required'
      });
    }

    await billingService.addPaymentMethod(organizationId, paymentMethodId);

    res.json({
      success: true,
      message: 'Payment method added successfully'
    });
  } catch (error) {
    logger.error('Error adding payment method:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to add payment method'
    });
  }
});

/**
 * @route PUT /api/billing/payment-methods/:paymentMethodId/default
 * @desc Set default payment method
 * @access Private
 */
router.put('/payment-methods/:paymentMethodId/default', authMiddleware, async (req, res) => {
  try {
    const { paymentMethodId } = req.params;
    const { organizationId } = req.user;

    await billingService.setDefaultPaymentMethod(organizationId, paymentMethodId);

    res.json({
      success: true,
      message: 'Default payment method updated successfully'
    });
  } catch (error) {
    logger.error('Error setting default payment method:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to set default payment method'
    });
  }
});

/**
 * @route DELETE /api/billing/payment-methods/:paymentMethodId
 * @desc Remove payment method
 * @access Private
 */
router.delete('/payment-methods/:paymentMethodId', authMiddleware, async (req, res) => {
  try {
    const { paymentMethodId } = req.params;

    await billingService.removePaymentMethod(paymentMethodId);

    res.json({
      success: true,
      message: 'Payment method removed successfully'
    });
  } catch (error) {
    logger.error('Error removing payment method:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to remove payment method'
    });
  }
});

/**
 * @route POST /api/billing/webhook
 * @desc Handle Stripe webhooks
 * @access Public
 */
router.post('/webhook', express.raw({ type: 'application/json' }), async (req, res) => {
  try {
    const sig = req.headers['stripe-signature'] as string;
    const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET!;

    let event: Stripe.Event;

    try {
      event = stripe.webhooks.constructEvent(req.body, sig, endpointSecret);
    } catch (err) {
      logger.error('Webhook signature verification failed:', err);
      return res.status(400).send('Webhook signature verification failed');
    }

    await billingService.handleWebhook(event);

    res.json({ received: true });
  } catch (error) {
    logger.error('Error handling webhook:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to handle webhook'
    });
  }
});

export default router;
