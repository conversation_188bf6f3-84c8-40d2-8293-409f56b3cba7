import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { Progress } from '../ui/progress';
import { 
  Clock, 
  Play, 
  Pause, 
  RotateCcw, 
  AlertCircle, 
  CheckCircle,
  XCircle,
  Loader
} from 'lucide-react';
import useSocialMedia from '../../hooks/useSocialMedia';

export const PublishingQueue: React.FC = () => {
  const { queueStats, loadQueueStats } = useSocialMedia();
  const [refreshInterval, setRefreshInterval] = useState<NodeJS.Timeout | null>(null);

  useEffect(() => {
    // Auto-refresh every 10 seconds
    const interval = setInterval(loadQueueStats, 10000);
    setRefreshInterval(interval);

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [loadQueueStats]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-600 bg-green-100';
      case 'active': return 'text-blue-600 bg-blue-100';
      case 'waiting': return 'text-yellow-600 bg-yellow-100';
      case 'failed': return 'text-red-600 bg-red-100';
      case 'delayed': return 'text-orange-600 bg-orange-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="w-4 h-4" />;
      case 'active': return <Loader className="w-4 h-4 animate-spin" />;
      case 'waiting': return <Clock className="w-4 h-4" />;
      case 'failed': return <XCircle className="w-4 h-4" />;
      case 'delayed': return <AlertCircle className="w-4 h-4" />;
      default: return <Clock className="w-4 h-4" />;
    }
  };

  if (!queueStats) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Clock className="w-5 h-5 mr-2" />
            Publishing Queue
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const publishQueue = queueStats.publishQueue;
  const analyticsQueue = queueStats.analyticsQueue;

  const totalPublishJobs = Object.values(publishQueue).reduce((sum, count) => sum + count, 0);
  const totalAnalyticsJobs = Object.values(analyticsQueue).reduce((sum, count) => sum + count, 0);

  const publishProgress = totalPublishJobs > 0 
    ? ((publishQueue.completed / totalPublishJobs) * 100) 
    : 0;

  const analyticsProgress = totalAnalyticsJobs > 0 
    ? ((analyticsQueue.completed / totalAnalyticsJobs) * 100) 
    : 0;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Publishing Queue</h2>
          <p className="text-gray-600 mt-1">
            Monitor and manage your content publishing and analytics jobs
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" size="sm" onClick={loadQueueStats}>
            <RotateCcw className="w-4 h-4 mr-1" />
            Refresh
          </Button>
          <Button variant="outline" size="sm">
            <Pause className="w-4 h-4 mr-1" />
            Pause Queue
          </Button>
        </div>
      </div>

      {/* Queue Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Publishing Queue */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center">
                <Play className="w-5 h-5 mr-2" />
                Publishing Queue
              </div>
              <Badge variant={publishQueue.active > 0 ? "default" : "secondary"}>
                {publishQueue.active > 0 ? "Active" : "Idle"}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* Progress */}
              <div>
                <div className="flex justify-between text-sm mb-2">
                  <span>Progress</span>
                  <span>{publishProgress.toFixed(1)}%</span>
                </div>
                <Progress value={publishProgress} className="h-2" />
              </div>

              {/* Stats */}
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-3 bg-blue-50 rounded-lg">
                  <div className="flex items-center justify-center mb-1">
                    <Loader className="w-4 h-4 text-blue-600 mr-1" />
                    <span className="text-sm font-medium text-blue-700">Active</span>
                  </div>
                  <p className="text-2xl font-bold text-blue-900">{publishQueue.active}</p>
                </div>
                <div className="text-center p-3 bg-yellow-50 rounded-lg">
                  <div className="flex items-center justify-center mb-1">
                    <Clock className="w-4 h-4 text-yellow-600 mr-1" />
                    <span className="text-sm font-medium text-yellow-700">Waiting</span>
                  </div>
                  <p className="text-2xl font-bold text-yellow-900">{publishQueue.waiting}</p>
                </div>
                <div className="text-center p-3 bg-green-50 rounded-lg">
                  <div className="flex items-center justify-center mb-1">
                    <CheckCircle className="w-4 h-4 text-green-600 mr-1" />
                    <span className="text-sm font-medium text-green-700">Completed</span>
                  </div>
                  <p className="text-2xl font-bold text-green-900">{publishQueue.completed}</p>
                </div>
                <div className="text-center p-3 bg-red-50 rounded-lg">
                  <div className="flex items-center justify-center mb-1">
                    <XCircle className="w-4 h-4 text-red-600 mr-1" />
                    <span className="text-sm font-medium text-red-700">Failed</span>
                  </div>
                  <p className="text-2xl font-bold text-red-900">{publishQueue.failed}</p>
                </div>
              </div>

              {/* Delayed Jobs */}
              {publishQueue.delayed > 0 && (
                <div className="border border-orange-200 bg-orange-50 rounded-lg p-3">
                  <div className="flex items-center">
                    <AlertCircle className="w-4 h-4 text-orange-600 mr-2" />
                    <span className="text-sm font-medium text-orange-700">
                      {publishQueue.delayed} jobs delayed
                    </span>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Analytics Queue */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center">
                <RotateCcw className="w-5 h-5 mr-2" />
                Analytics Queue
              </div>
              <Badge variant={analyticsQueue.active > 0 ? "default" : "secondary"}>
                {analyticsQueue.active > 0 ? "Active" : "Idle"}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* Progress */}
              <div>
                <div className="flex justify-between text-sm mb-2">
                  <span>Progress</span>
                  <span>{analyticsProgress.toFixed(1)}%</span>
                </div>
                <Progress value={analyticsProgress} className="h-2" />
              </div>

              {/* Stats */}
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-3 bg-blue-50 rounded-lg">
                  <div className="flex items-center justify-center mb-1">
                    <Loader className="w-4 h-4 text-blue-600 mr-1" />
                    <span className="text-sm font-medium text-blue-700">Active</span>
                  </div>
                  <p className="text-2xl font-bold text-blue-900">{analyticsQueue.active}</p>
                </div>
                <div className="text-center p-3 bg-yellow-50 rounded-lg">
                  <div className="flex items-center justify-center mb-1">
                    <Clock className="w-4 h-4 text-yellow-600 mr-1" />
                    <span className="text-sm font-medium text-yellow-700">Waiting</span>
                  </div>
                  <p className="text-2xl font-bold text-yellow-900">{analyticsQueue.waiting}</p>
                </div>
                <div className="text-center p-3 bg-green-50 rounded-lg">
                  <div className="flex items-center justify-center mb-1">
                    <CheckCircle className="w-4 h-4 text-green-600 mr-1" />
                    <span className="text-sm font-medium text-green-700">Completed</span>
                  </div>
                  <p className="text-2xl font-bold text-green-900">{analyticsQueue.completed}</p>
                </div>
                <div className="text-center p-3 bg-red-50 rounded-lg">
                  <div className="flex items-center justify-center mb-1">
                    <XCircle className="w-4 h-4 text-red-600 mr-1" />
                    <span className="text-sm font-medium text-red-700">Failed</span>
                  </div>
                  <p className="text-2xl font-bold text-red-900">{analyticsQueue.failed}</p>
                </div>
              </div>

              {/* Delayed Jobs */}
              {analyticsQueue.delayed > 0 && (
                <div className="border border-orange-200 bg-orange-50 rounded-lg p-3">
                  <div className="flex items-center">
                    <AlertCircle className="w-4 h-4 text-orange-600 mr-2" />
                    <span className="text-sm font-medium text-orange-700">
                      {analyticsQueue.delayed} jobs delayed
                    </span>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Queue Health Status */}
      <Card>
        <CardHeader>
          <CardTitle>Queue Health</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl mb-2">
                {(publishQueue.failed + analyticsQueue.failed) === 0 ? '✅' : '⚠️'}
              </div>
              <p className="font-medium">System Status</p>
              <p className="text-sm text-gray-600">
                {(publishQueue.failed + analyticsQueue.failed) === 0 ? 'All systems operational' : 'Some jobs failed'}
              </p>
            </div>
            
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl mb-2">⚡</div>
              <p className="font-medium">Processing Speed</p>
              <p className="text-sm text-gray-600">
                {publishQueue.active + analyticsQueue.active} jobs active
              </p>
            </div>
            
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl mb-2">📊</div>
              <p className="font-medium">Total Processed</p>
              <p className="text-sm text-gray-600">
                {publishQueue.completed + analyticsQueue.completed} jobs completed
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default PublishingQueue;
