import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { Clock, TrendingUp, Zap, Calendar } from 'lucide-react';
import useSocialMedia from '../../hooks/useSocialMedia';

export const OptimalTimingInsights: React.FC = () => {
  const { getOptimalPostingTimes, getConnectedPlatforms } = useSocialMedia();
  const [optimalTimes, setOptimalTimes] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  const connectedPlatforms = getConnectedPlatforms();

  useEffect(() => {
    loadOptimalTimes();
  }, [connectedPlatforms]);

  const loadOptimalTimes = async () => {
    if (connectedPlatforms.length === 0) return;
    
    setLoading(true);
    try {
      const times = await getOptimalPostingTimes(connectedPlatforms);
      setOptimalTimes(times);
    } catch (error) {
      console.error('Error loading optimal times:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatTime = (hour: number) => {
    const period = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
    return `${displayHour}:00 ${period}`;
  };

  const getDayName = (dayOfWeek: number) => {
    const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    return days[dayOfWeek];
  };

  if (connectedPlatforms.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Clock className="w-5 h-5 mr-2" />
            Optimal Posting Times
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Clock className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">Connect social media accounts to see optimal posting times</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center">
            <Clock className="w-5 h-5 mr-2" />
            Optimal Posting Times
          </div>
          <Button variant="outline" size="sm" onClick={loadOptimalTimes} disabled={loading}>
            <Zap className="w-4 h-4 mr-1" />
            Refresh
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        ) : optimalTimes.length > 0 ? (
          <div className="space-y-6">
            {optimalTimes.map((timing, index) => (
              <div key={index} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-4">
                  <h4 className="font-medium text-lg">{timing.platform}</h4>
                  <Badge variant="outline">{timing.audienceTimezone}</Badge>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Best Times */}
                  <div>
                    <h5 className="font-medium text-sm text-gray-700 mb-3">Best Times This Week</h5>
                    <div className="space-y-2">
                      {timing.bestTimes.slice(0, 5).map((time: any, timeIndex: number) => (
                        <div key={timeIndex} className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <Calendar className="w-4 h-4 text-gray-500" />
                            <span className="text-sm">
                              {getDayName(time.dayOfWeek)} at {formatTime(time.hour)}
                            </span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <TrendingUp className="w-3 h-3 text-green-500" />
                            <span className="text-xs text-green-600">
                              {(time.engagementScore * 100).toFixed(1)}%
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Peak Hours */}
                  <div>
                    <h5 className="font-medium text-sm text-gray-700 mb-3">Peak Engagement Hours</h5>
                    <div className="grid grid-cols-4 gap-2">
                      {timing.peakEngagementHours.map((hour: number, hourIndex: number) => (
                        <div
                          key={hourIndex}
                          className="text-center p-2 bg-blue-50 rounded text-xs font-medium text-blue-700"
                        >
                          {formatTime(hour)}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <Clock className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600 mb-2">No optimal timing data available yet</p>
            <p className="text-sm text-gray-500">
              Publish some content first to generate insights
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default OptimalTimingInsights;
