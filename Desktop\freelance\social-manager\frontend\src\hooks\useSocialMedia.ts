import { useState, useEffect, useCallback } from 'react';
import { 
  socialMediaApi, 
  SocialMediaAccount, 
  PublishRequest, 
  ScheduleRequest, 
  BulkScheduleRequest,
  ScheduledContent,
  OptimalTiming,
  ContentAnalytics,
  QueueStats
} from '../api/socialMedia';

export const useSocialMedia = () => {
  const [accounts, setAccounts] = useState<SocialMediaAccount[]>([]);
  const [scheduledContent, setScheduledContent] = useState<ScheduledContent[]>([]);
  const [queueStats, setQueueStats] = useState<QueueStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load connected accounts
  const loadAccounts = useCallback(async () => {
    try {
      setLoading(true);
      const data = await socialMediaApi.getConnectedAccounts();
      setAccounts(data);
      setError(null);
    } catch (err) {
      setError('Failed to load connected accounts');
      console.error('Error loading accounts:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  // Load scheduled content
  const loadScheduledContent = useCallback(async (startDate?: string, endDate?: string) => {
    try {
      setLoading(true);
      const data = await socialMediaApi.getScheduledContent(startDate, endDate);
      setScheduledContent(data);
      setError(null);
    } catch (err) {
      setError('Failed to load scheduled content');
      console.error('Error loading scheduled content:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  // Load queue statistics
  const loadQueueStats = useCallback(async () => {
    try {
      const data = await socialMediaApi.getQueueStats();
      setQueueStats(data);
    } catch (err) {
      console.error('Error loading queue stats:', err);
    }
  }, []);

  // Connect social media account
  const connectAccount = useCallback(async (platform: string, authCode: string) => {
    try {
      setLoading(true);
      await socialMediaApi.connectAccount(platform, authCode);
      await loadAccounts(); // Reload accounts
      setError(null);
      return true;
    } catch (err) {
      setError('Failed to connect account');
      console.error('Error connecting account:', err);
      return false;
    } finally {
      setLoading(false);
    }
  }, [loadAccounts]);

  // Disconnect social media account
  const disconnectAccount = useCallback(async (accountId: string) => {
    try {
      setLoading(true);
      await socialMediaApi.disconnectAccount(accountId);
      await loadAccounts(); // Reload accounts
      setError(null);
      return true;
    } catch (err) {
      setError('Failed to disconnect account');
      console.error('Error disconnecting account:', err);
      return false;
    } finally {
      setLoading(false);
    }
  }, [loadAccounts]);

  // Publish content immediately
  const publishContent = useCallback(async (request: PublishRequest) => {
    try {
      setLoading(true);
      const result = await socialMediaApi.publishContent(request);
      setError(null);
      return result;
    } catch (err) {
      setError('Failed to publish content');
      console.error('Error publishing content:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // Schedule content for later
  const scheduleContent = useCallback(async (request: ScheduleRequest) => {
    try {
      setLoading(true);
      const result = await socialMediaApi.scheduleContent(request);
      await loadScheduledContent(); // Reload scheduled content
      setError(null);
      return result;
    } catch (err) {
      setError('Failed to schedule content');
      console.error('Error scheduling content:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [loadScheduledContent]);

  // Bulk schedule content
  const bulkScheduleContent = useCallback(async (request: BulkScheduleRequest) => {
    try {
      setLoading(true);
      const result = await socialMediaApi.bulkScheduleContent(request);
      await loadScheduledContent(); // Reload scheduled content
      setError(null);
      return result;
    } catch (err) {
      setError('Failed to bulk schedule content');
      console.error('Error bulk scheduling content:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [loadScheduledContent]);

  // Reschedule content
  const rescheduleContent = useCallback(async (contentId: string, scheduledAt: string) => {
    try {
      setLoading(true);
      await socialMediaApi.rescheduleContent(contentId, scheduledAt);
      await loadScheduledContent(); // Reload scheduled content
      setError(null);
      return true;
    } catch (err) {
      setError('Failed to reschedule content');
      console.error('Error rescheduling content:', err);
      return false;
    } finally {
      setLoading(false);
    }
  }, [loadScheduledContent]);

  // Cancel scheduled content
  const cancelScheduledContent = useCallback(async (contentId: string) => {
    try {
      setLoading(true);
      await socialMediaApi.cancelScheduledContent(contentId);
      await loadScheduledContent(); // Reload scheduled content
      setError(null);
      return true;
    } catch (err) {
      setError('Failed to cancel scheduled content');
      console.error('Error cancelling scheduled content:', err);
      return false;
    } finally {
      setLoading(false);
    }
  }, [loadScheduledContent]);

  // Get optimal posting times
  const getOptimalPostingTimes = useCallback(async (platforms?: string[]): Promise<OptimalTiming[]> => {
    try {
      return await socialMediaApi.getOptimalPostingTimes(platforms);
    } catch (err) {
      console.error('Error getting optimal posting times:', err);
      return [];
    }
  }, []);

  // Get content analytics
  const getContentAnalytics = useCallback(async (contentId: string): Promise<ContentAnalytics | null> => {
    try {
      return await socialMediaApi.getContentAnalytics(contentId);
    } catch (err) {
      console.error('Error getting content analytics:', err);
      return null;
    }
  }, []);

  // Utility functions
  const getConnectedPlatforms = useCallback(() => {
    return accounts.filter(account => account.isActive).map(account => account.platform);
  }, [accounts]);

  const isConnected = useCallback((platform: string) => {
    return accounts.some(account => account.platform === platform && account.isActive);
  }, [accounts]);

  const getAccountByPlatform = useCallback((platform: string) => {
    return accounts.find(account => account.platform === platform && account.isActive);
  }, [accounts]);

  const validateContent = useCallback((content: string, platforms: string[]) => {
    const errors: string[] = [];
    
    for (const platform of platforms) {
      const validation = socialMediaApi.validateContentForPlatform(content, platform);
      if (!validation.isValid) {
        errors.push(...validation.errors);
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }, []);

  // Load initial data
  useEffect(() => {
    loadAccounts();
    loadQueueStats();
  }, [loadAccounts, loadQueueStats]);

  // Auto-refresh queue stats every 30 seconds
  useEffect(() => {
    const interval = setInterval(loadQueueStats, 30000);
    return () => clearInterval(interval);
  }, [loadQueueStats]);

  return {
    // State
    accounts,
    scheduledContent,
    queueStats,
    loading,
    error,

    // Actions
    connectAccount,
    disconnectAccount,
    publishContent,
    scheduleContent,
    bulkScheduleContent,
    rescheduleContent,
    cancelScheduledContent,
    getOptimalPostingTimes,
    getContentAnalytics,

    // Data loading
    loadAccounts,
    loadScheduledContent,
    loadQueueStats,

    // Utility functions
    getConnectedPlatforms,
    isConnected,
    getAccountByPlatform,
    validateContent,

    // Helper functions from API
    getPlatformDisplayName: socialMediaApi.getPlatformDisplayName,
    getPlatformIcon: socialMediaApi.getPlatformIcon,
    getPlatformColor: socialMediaApi.getPlatformColor,
    getOptimalContentLength: socialMediaApi.getOptimalContentLength,
    getRecommendedHashtagCount: socialMediaApi.getRecommendedHashtagCount,
    getOAuthUrl: socialMediaApi.getOAuthUrl
  };
};

export default useSocialMedia;
