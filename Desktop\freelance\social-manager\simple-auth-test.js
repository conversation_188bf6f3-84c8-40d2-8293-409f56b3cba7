const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api';

async function testAuth() {
  console.log('🔐 Simple Authentication Test\n');

  // Test user data
  const testUser = {
    email: '<EMAIL>',
    password: 'password123',
    name: 'Test User',
    organizationName: 'Test Organization'
  };

  try {
    // Step 1: Try to register user
    console.log('1. Testing Registration...');
    try {
      const registerResponse = await axios.post(`${BASE_URL}/auth/register`, testUser);
      console.log('✅ Registration successful:', registerResponse.data);
    } catch (error) {
      if (error.response?.status === 400 && error.response?.data?.message?.includes('already exists')) {
        console.log('ℹ️  User already exists, proceeding to login...');
      } else {
        console.log('❌ Registration failed:', error.response?.data?.message || error.message);
        console.log('Full error:', error.response?.data);
      }
    }

    // Step 2: Test login
    console.log('\n2. Testing Login...');
    const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
      email: testUser.email,
      password: testUser.password
    });
    
    console.log('✅ Login successful!');
    console.log('User:', loginResponse.data.data.user.name);
    console.log('Token received:', !!loginResponse.data.data.token);
    
    const token = loginResponse.data.data.token;

    // Step 3: Test protected route
    console.log('\n3. Testing Protected Route...');
    const profileResponse = await axios.get(`${BASE_URL}/auth/me`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    console.log('✅ Protected route access successful!');
    console.log('Profile:', profileResponse.data.data.user.name);

    // Step 4: Test organizations
    console.log('\n4. Testing Organizations...');
    const orgsResponse = await axios.get(`${BASE_URL}/organizations`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    console.log('✅ Organizations access successful!');
    console.log('Organizations found:', orgsResponse.data.data?.organizations?.length || 0);

    console.log('\n🎉 All authentication tests passed!');
    console.log('\n📊 Summary:');
    console.log('✅ User registration/login: Working');
    console.log('✅ JWT token generation: Working');
    console.log('✅ Protected routes: Working');
    console.log('✅ Authorization: Working');

  } catch (error) {
    console.error('❌ Authentication test failed:', error.response?.data || error.message);
    console.error('Status:', error.response?.status);
    console.error('URL:', error.config?.url);
  }
}

testAuth();
