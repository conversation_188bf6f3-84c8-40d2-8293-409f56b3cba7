import { Request, Response, NextFunction } from 'express';
import rateLimit from 'express-rate-limit';
import helmet from 'helmet';
import { body, validationResult, ValidationChain } from 'express-validator';
import { logger } from '../utils/logger.js';
import { CacheService } from '../config/redis.js';
import crypto from 'crypto';

interface SecurityOptions {
  enableCSRF?: boolean;
  enableXSS?: boolean;
  enableSQLInjection?: boolean;
  enableRateLimit?: boolean;
  trustedProxies?: string[];
}

/**
 * Comprehensive security middleware
 */
export function securityMiddleware(options: SecurityOptions = {}) {
  const {
    enableCSRF = true,
    enableXSS = true,
    enableSQLInjection = true,
    enableRateLimit = true,
    trustedProxies = []
  } = options;

  return [
    // Helmet for basic security headers
    helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
          fontSrc: ["'self'", "https://fonts.gstatic.com"],
          imgSrc: ["'self'", "data:", "https:", "blob:"],
          scriptSrc: ["'self'"],
          connectSrc: ["'self'", "https://api.socialhub.com"],
          frameSrc: ["'none'"],
          objectSrc: ["'none'"],
          baseUri: ["'self'"],
          formAction: ["'self'"]
        }
      },
      crossOriginEmbedderPolicy: false // Allow embedding for iframe integrations
    }),

    // Custom security headers
    (req: Request, res: Response, next: NextFunction) => {
      // Security headers
      res.setHeader('X-Content-Type-Options', 'nosniff');
      res.setHeader('X-Frame-Options', 'DENY');
      res.setHeader('X-XSS-Protection', '1; mode=block');
      res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
      res.setHeader('Permissions-Policy', 'geolocation=(), microphone=(), camera=()');
      
      // Remove server information
      res.removeHeader('X-Powered-By');
      res.setHeader('Server', 'SocialHub');

      next();
    },

    // Request ID for tracking
    (req: Request, res: Response, next: NextFunction) => {
      const requestId = crypto.randomUUID();
      req.requestId = requestId;
      res.setHeader('X-Request-ID', requestId);
      next();
    },

    // IP validation and proxy handling
    (req: Request, res: Response, next: NextFunction) => {
      // Handle trusted proxies
      if (trustedProxies.length > 0) {
        const forwarded = req.headers['x-forwarded-for'] as string;
        if (forwarded) {
          const ips = forwarded.split(',').map(ip => ip.trim());
          req.realIP = ips[0];
        }
      }

      // Validate IP format
      const ip = req.realIP || req.ip;
      if (ip && !isValidIP(ip)) {
        logger.warn(`Invalid IP detected: ${ip}`, { requestId: req.requestId });
        return res.status(400).json({ error: 'Invalid request source' });
      }

      next();
    }
  ];
}

/**
 * CSRF Protection middleware
 */
export function csrfProtection() {
  return async (req: Request, res: Response, next: NextFunction) => {
    // Skip CSRF for GET requests and API endpoints with valid API keys
    if (req.method === 'GET' || req.headers['x-api-key']) {
      return next();
    }

    const token = req.headers['x-csrf-token'] as string;
    const sessionId = req.session?.id || req.headers['x-session-id'] as string;

    if (!token || !sessionId) {
      logger.warn('Missing CSRF token or session ID', { 
        requestId: req.requestId,
        ip: req.ip 
      });
      return res.status(403).json({ error: 'CSRF token required' });
    }

    // Verify CSRF token
    const isValid = await verifyCsrfToken(sessionId, token);
    if (!isValid) {
      logger.warn('Invalid CSRF token', { 
        requestId: req.requestId,
        ip: req.ip,
        sessionId 
      });
      return res.status(403).json({ error: 'Invalid CSRF token' });
    }

    next();
  };
}

/**
 * XSS Protection middleware
 */
export function xssProtection() {
  return (req: Request, res: Response, next: NextFunction) => {
    // Sanitize request body
    if (req.body && typeof req.body === 'object') {
      req.body = sanitizeObject(req.body);
    }

    // Sanitize query parameters
    if (req.query && typeof req.query === 'object') {
      req.query = sanitizeObject(req.query);
    }

    next();
  };
}

/**
 * SQL Injection Protection middleware
 */
export function sqlInjectionProtection() {
  return (req: Request, res: Response, next: NextFunction) => {
    const suspiciousPatterns = [
      /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/gi,
      /(--|\/\*|\*\/|;)/g,
      /(\b(OR|AND)\b.*=.*)/gi,
      /('|(\\')|(;)|(\\))/g
    ];

    const checkForSQLInjection = (value: any): boolean => {
      if (typeof value === 'string') {
        return suspiciousPatterns.some(pattern => pattern.test(value));
      }
      if (typeof value === 'object' && value !== null) {
        return Object.values(value).some(checkForSQLInjection);
      }
      return false;
    };

    // Check query parameters
    if (checkForSQLInjection(req.query)) {
      logger.warn('SQL injection attempt detected in query', { 
        requestId: req.requestId,
        ip: req.ip,
        query: req.query 
      });
      return res.status(400).json({ error: 'Invalid request parameters' });
    }

    // Check request body
    if (checkForSQLInjection(req.body)) {
      logger.warn('SQL injection attempt detected in body', { 
        requestId: req.requestId,
        ip: req.ip 
      });
      return res.status(400).json({ error: 'Invalid request data' });
    }

    next();
  };
}

/**
 * API Key validation middleware
 */
export function apiKeyValidation() {
  return async (req: Request, res: Response, next: NextFunction) => {
    const apiKey = req.headers['x-api-key'] as string;
    
    if (!apiKey) {
      return res.status(401).json({ error: 'API key required' });
    }

    try {
      // Validate API key format
      if (!isValidApiKeyFormat(apiKey)) {
        logger.warn('Invalid API key format', { 
          requestId: req.requestId,
          ip: req.ip 
        });
        return res.status(401).json({ error: 'Invalid API key format' });
      }

      // Check if API key exists and is active
      const keyData = await validateApiKey(apiKey);
      if (!keyData) {
        logger.warn('Invalid API key', { 
          requestId: req.requestId,
          ip: req.ip,
          apiKey: apiKey.substring(0, 8) + '...' 
        });
        return res.status(401).json({ error: 'Invalid API key' });
      }

      // Add API key data to request
      req.apiKey = keyData;
      next();
    } catch (error) {
      logger.error('Error validating API key:', error);
      return res.status(500).json({ error: 'Internal server error' });
    }
  };
}

/**
 * Request size limiter
 */
export function requestSizeLimiter(maxSize: string = '10mb') {
  return (req: Request, res: Response, next: NextFunction) => {
    const contentLength = req.headers['content-length'];
    
    if (contentLength) {
      const sizeInBytes = parseInt(contentLength, 10);
      const maxSizeInBytes = parseSize(maxSize);
      
      if (sizeInBytes > maxSizeInBytes) {
        logger.warn('Request size limit exceeded', { 
          requestId: req.requestId,
          ip: req.ip,
          size: sizeInBytes,
          limit: maxSizeInBytes 
        });
        return res.status(413).json({ error: 'Request entity too large' });
      }
    }

    next();
  };
}

/**
 * Suspicious activity detector
 */
export function suspiciousActivityDetector() {
  return async (req: Request, res: Response, next: NextFunction) => {
    const ip = req.ip;
    const userAgent = req.headers['user-agent'] || '';
    const path = req.path;

    // Check for suspicious patterns
    const suspiciousPatterns = [
      /bot|crawler|spider|scraper/i,
      /curl|wget|python|php/i,
      /sqlmap|nikto|nmap|masscan/i
    ];

    const isSuspiciousUserAgent = suspiciousPatterns.some(pattern => 
      pattern.test(userAgent)
    );

    const isSuspiciousPath = [
      '/admin',
      '/wp-admin',
      '/.env',
      '/config',
      '/backup'
    ].some(suspiciousPath => path.includes(suspiciousPath));

    if (isSuspiciousUserAgent || isSuspiciousPath) {
      // Track suspicious activity
      await trackSuspiciousActivity(ip, {
        userAgent,
        path,
        timestamp: new Date(),
        requestId: req.requestId
      });

      logger.warn('Suspicious activity detected', {
        requestId: req.requestId,
        ip,
        userAgent,
        path
      });

      // Don't block immediately, but increase monitoring
      req.suspiciousActivity = true;
    }

    next();
  };
}

// Helper functions

function isValidIP(ip: string): boolean {
  const ipv4Regex = /^(\d{1,3}\.){3}\d{1,3}$/;
  const ipv6Regex = /^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;
  return ipv4Regex.test(ip) || ipv6Regex.test(ip);
}

function sanitizeObject(obj: any): any {
  if (typeof obj === 'string') {
    return obj
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/<[^>]*>/g, '')
      .replace(/javascript:/gi, '')
      .replace(/on\w+\s*=/gi, '');
  }
  
  if (Array.isArray(obj)) {
    return obj.map(sanitizeObject);
  }
  
  if (typeof obj === 'object' && obj !== null) {
    const sanitized: any = {};
    for (const [key, value] of Object.entries(obj)) {
      sanitized[key] = sanitizeObject(value);
    }
    return sanitized;
  }
  
  return obj;
}

async function verifyCsrfToken(sessionId: string, token: string): Promise<boolean> {
  try {
    const storedToken = await CacheService.get(`csrf:${sessionId}`);
    return storedToken === token;
  } catch (error) {
    logger.error('Error verifying CSRF token:', error);
    return false;
  }
}

function isValidApiKeyFormat(apiKey: string): boolean {
  // API key should be 32-64 characters, alphanumeric with dashes
  const apiKeyRegex = /^[a-zA-Z0-9\-_]{32,64}$/;
  return apiKeyRegex.test(apiKey);
}

async function validateApiKey(apiKey: string): Promise<any> {
  try {
    // This would typically query your database
    // For now, we'll use cache
    const keyData = await CacheService.get(`apikey:${apiKey}`);
    return keyData ? JSON.parse(keyData) : null;
  } catch (error) {
    logger.error('Error validating API key:', error);
    return null;
  }
}

function parseSize(size: string): number {
  const units: { [key: string]: number } = {
    b: 1,
    kb: 1024,
    mb: 1024 * 1024,
    gb: 1024 * 1024 * 1024
  };
  
  const match = size.toLowerCase().match(/^(\d+)(b|kb|mb|gb)$/);
  if (!match) return 0;
  
  const [, num, unit] = match;
  return parseInt(num, 10) * units[unit];
}

async function trackSuspiciousActivity(ip: string, activity: any): Promise<void> {
  try {
    const key = `suspicious:${ip}`;
    const existing = await CacheService.get(key);
    const activities = existing ? JSON.parse(existing) : [];
    
    activities.push(activity);
    
    // Keep only last 100 activities
    if (activities.length > 100) {
      activities.splice(0, activities.length - 100);
    }
    
    await CacheService.set(key, JSON.stringify(activities), 3600); // 1 hour
  } catch (error) {
    logger.error('Error tracking suspicious activity:', error);
  }
}

// Export validation chains for common use cases
export const validationChains = {
  email: body('email').isEmail().normalizeEmail(),
  password: body('password').isLength({ min: 8 }).matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/),
  uuid: body('id').isUUID(),
  alphanumeric: body('name').isAlphanumeric(),
  url: body('url').isURL(),
  json: body('data').isJSON()
};
