const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api';
let authToken = '';
let testUserId = '';
let testProjectId = '';
let testContentId = '';
let testIntegrationId = '';

// Test results tracking
const testResults = {
  passed: 0,
  failed: 0,
  tests: [],
  categories: {
    authentication: { passed: 0, failed: 0 },
    projects: { passed: 0, failed: 0 },
    content: { passed: 0, failed: 0 },
    analytics: { passed: 0, failed: 0 },
    integrations: { passed: 0, failed: 0 },
  }
};

function logTest(category, name, passed, details = '') {
  const status = passed ? '✅ PASS' : '❌ FAIL';
  console.log(`${status}: [${category.toUpperCase()}] ${name}`);
  if (details) console.log(`   ${details}`);
  
  testResults.tests.push({ category, name, passed, details });
  testResults.categories[category][passed ? 'passed' : 'failed']++;
  if (passed) testResults.passed++;
  else testResults.failed++;
}

async function testAuthentication() {
  console.log('\n🔐 TESTING AUTHENTICATION SYSTEM...');
  
  try {
    // Test 1: Valid login
    const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'password123'
    });
    
    if (loginResponse.data.success && loginResponse.data.data.token) {
      authToken = loginResponse.data.data.token;
      testUserId = loginResponse.data.data.user.id;
      logTest('authentication', 'Valid login', true, `Token: ${authToken.substring(0, 20)}...`);
    } else {
      logTest('authentication', 'Valid login', false, 'No token received');
      return false;
    }
    
    // Test 2: Protected route access
    const meResponse = await axios.get(`${BASE_URL}/auth/me`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    
    if (meResponse.data.success && meResponse.data.data.user) {
      logTest('authentication', 'Protected route access', true, `User: ${meResponse.data.data.user.firstName}`);
    } else {
      logTest('authentication', 'Protected route access', false);
    }
    
    // Test 3: Invalid credentials rejection
    try {
      await axios.post(`${BASE_URL}/auth/login`, {
        email: '<EMAIL>',
        password: 'wrongpassword'
      });
      logTest('authentication', 'Invalid credentials rejection', false, 'Should have failed but succeeded');
    } catch (error) {
      if (error.response && error.response.status === 401) {
        logTest('authentication', 'Invalid credentials rejection', true, 'Correctly rejected');
      } else {
        logTest('authentication', 'Invalid credentials rejection', false, `Unexpected error: ${error.message}`);
      }
    }
    
    // Test 4: Unauthorized access prevention
    try {
      await axios.get(`${BASE_URL}/auth/me`);
      logTest('authentication', 'Unauthorized access prevention', false, 'Should have failed but succeeded');
    } catch (error) {
      if (error.response && error.response.status === 401) {
        logTest('authentication', 'Unauthorized access prevention', true, 'Correctly blocked');
      } else {
        logTest('authentication', 'Unauthorized access prevention', false, `Unexpected error: ${error.message}`);
      }
    }
    
    return true;
    
  } catch (error) {
    logTest('authentication', 'Authentication system', false, error.message);
    return false;
  }
}

async function testProjects() {
  console.log('\n📁 TESTING PROJECTS MANAGEMENT...');
  
  try {
    // Test 1: Create project
    const createResponse = await axios.post(`${BASE_URL}/projects`, {
      name: 'E2E Test Project',
      description: 'A comprehensive test project',
      type: 'CAMPAIGN',
      budget: 10000,
      deadline: '2024-12-31',
      startDate: '2024-01-01'
    }, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    
    if (createResponse.data.success && createResponse.data.data.project) {
      testProjectId = createResponse.data.data.project.id;
      logTest('projects', 'Create project', true, `Project ID: ${testProjectId}`);
    } else {
      logTest('projects', 'Create project', false);
      return false;
    }
    
    // Test 2: List projects
    const listResponse = await axios.get(`${BASE_URL}/projects`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    
    if (listResponse.data.success && Array.isArray(listResponse.data.data.projects)) {
      const projectCount = listResponse.data.data.projects.length;
      logTest('projects', 'List projects', true, `Found ${projectCount} projects`);
    } else {
      logTest('projects', 'List projects', false);
    }
    
    // Test 3: Get project by ID
    const getResponse = await axios.get(`${BASE_URL}/projects/${testProjectId}`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    
    if (getResponse.data.success && getResponse.data.data.project) {
      logTest('projects', 'Get project by ID', true, `Name: ${getResponse.data.data.project.name}`);
    } else {
      logTest('projects', 'Get project by ID', false);
    }
    
    // Test 4: Update project
    const updateResponse = await axios.put(`${BASE_URL}/projects/${testProjectId}`, {
      name: 'Updated E2E Test Project',
      status: 'IN_PROGRESS',
      budget: 15000
    }, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    
    if (updateResponse.data.success) {
      logTest('projects', 'Update project', true, 'Project updated successfully');
    } else {
      logTest('projects', 'Update project', false);
    }
    
    // Test 5: Project pagination
    const paginationResponse = await axios.get(`${BASE_URL}/projects?page=1&limit=5`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    
    if (paginationResponse.data.success && paginationResponse.data.data.pagination) {
      logTest('projects', 'Project pagination', true, `Page: ${paginationResponse.data.data.pagination.page}`);
    } else {
      logTest('projects', 'Project pagination', false);
    }
    
    return true;
    
  } catch (error) {
    logTest('projects', 'Projects system', false, error.message);
    return false;
  }
}

async function testContent() {
  console.log('\n📝 TESTING CONTENT MANAGEMENT...');
  
  try {
    // Test 1: Create content
    const createResponse = await axios.post(`${BASE_URL}/content`, {
      title: 'E2E Test Content',
      description: 'Test content for comprehensive testing',
      type: 'POST',
      platforms: ['INSTAGRAM', 'FACEBOOK'],
      contentData: {
        text: 'This is a test post for our E2E testing suite!',
        hashtags: ['#testing', '#e2e', '#socialhub'],
        mentions: []
      },
      projectId: testProjectId,
      scheduledAt: '2024-12-25T10:00:00Z'
    }, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    
    if (createResponse.data.success && createResponse.data.data.contentItem) {
      testContentId = createResponse.data.data.contentItem.id;
      logTest('content', 'Create content', true, `Content ID: ${testContentId}`);
    } else {
      logTest('content', 'Create content', false);
      return false;
    }
    
    // Test 2: List content
    const listResponse = await axios.get(`${BASE_URL}/content`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    
    if (listResponse.data.success && Array.isArray(listResponse.data.data.content)) {
      const contentCount = listResponse.data.data.content.length;
      logTest('content', 'List content', true, `Found ${contentCount} content items`);
    } else {
      logTest('content', 'List content', false);
    }
    
    // Test 3: Update content
    const updateResponse = await axios.put(`${BASE_URL}/content/${testContentId}`, {
      title: 'Updated E2E Test Content',
      status: 'APPROVED',
      description: 'Updated description for testing'
    }, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    
    if (updateResponse.data.success) {
      logTest('content', 'Update content', true, 'Content updated successfully');
    } else {
      logTest('content', 'Update content', false);
    }
    
    // Test 4: Content filtering
    const filterResponse = await axios.get(`${BASE_URL}/content?status=APPROVED&type=POST`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    
    if (filterResponse.data.success) {
      logTest('content', 'Content filtering', true, 'Filtering works correctly');
    } else {
      logTest('content', 'Content filtering', false);
    }
    
    return true;
    
  } catch (error) {
    logTest('content', 'Content system', false, error.message);
    return false;
  }
}

async function testAnalytics() {
  console.log('\n📊 TESTING ANALYTICS SYSTEM...');
  
  try {
    // Test 1: Analytics overview
    const overviewResponse = await axios.get(`${BASE_URL}/analytics/overview`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    
    if (overviewResponse.data.success) {
      logTest('analytics', 'Analytics overview', true, 'Overview data retrieved');
    } else {
      logTest('analytics', 'Analytics overview', false);
    }
    
    // Test 2: Performance metrics
    const performanceResponse = await axios.get(`${BASE_URL}/analytics/performance`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    
    if (performanceResponse.data.success) {
      logTest('analytics', 'Performance metrics', true, 'Performance data retrieved');
    } else {
      logTest('analytics', 'Performance metrics', false);
    }
    
    // Test 3: Analytics with filters
    const filteredResponse = await axios.get(`${BASE_URL}/analytics/overview?period=7d&projectId=${testProjectId}`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    
    if (filteredResponse.data.success) {
      logTest('analytics', 'Analytics filtering', true, 'Filtered analytics work');
    } else {
      logTest('analytics', 'Analytics filtering', false);
    }
    
    // Test 4: Platform-specific analytics
    const platformResponse = await axios.get(`${BASE_URL}/analytics/performance?platform=INSTAGRAM&period=30d`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    
    if (platformResponse.data.success) {
      logTest('analytics', 'Platform-specific analytics', true, 'Platform filtering works');
    } else {
      logTest('analytics', 'Platform-specific analytics', false);
    }
    
    return true;
    
  } catch (error) {
    logTest('analytics', 'Analytics system', false, error.message);
    return false;
  }
}

async function testIntegrations() {
  console.log('\n🔗 TESTING INTEGRATIONS SYSTEM...');
  
  try {
    // Test 1: List integrations
    const listResponse = await axios.get(`${BASE_URL}/integrations`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    
    if (listResponse.data.success) {
      logTest('integrations', 'List integrations', true, `Found ${listResponse.data.data.length} integrations`);
    } else {
      logTest('integrations', 'List integrations', false);
    }
    
    // Test 2: Platform capabilities
    const capabilitiesResponse = await axios.get(`${BASE_URL}/integrations/platforms/INSTAGRAM/capabilities`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    
    if (capabilitiesResponse.data.success && capabilitiesResponse.data.data) {
      logTest('integrations', 'Platform capabilities', true, `Content types: ${capabilitiesResponse.data.data.contentTypes.join(', ')}`);
    } else {
      logTest('integrations', 'Platform capabilities', false);
    }
    
    // Test 3: Connect integration
    const connectResponse = await axios.post(`${BASE_URL}/integrations/connect`, {
      platform: 'INSTAGRAM',
      accountName: 'test_e2e_account',
      accountId: 'test_e2e_123',
      accessToken: 'test_token_e2e_123',
      refreshToken: 'refresh_token_e2e_123',
      expiresAt: '2024-12-31T23:59:59Z'
    }, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    
    if (connectResponse.data.success && connectResponse.data.data) {
      testIntegrationId = connectResponse.data.data.id;
      logTest('integrations', 'Connect integration', true, `Integration ID: ${testIntegrationId}`);
    } else {
      logTest('integrations', 'Connect integration', false);
      return false;
    }
    
    // Test 4: Test integration connection
    const testResponse = await axios.post(`${BASE_URL}/integrations/${testIntegrationId}/test`, {}, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    
    if (testResponse.data.success) {
      logTest('integrations', 'Test integration connection', true, `Connected: ${testResponse.data.data.connected}`);
    } else {
      logTest('integrations', 'Test integration connection', false);
    }
    
    // Test 5: Update integration
    const updateResponse = await axios.put(`${BASE_URL}/integrations/${testIntegrationId}`, {
      isActive: false,
      settings: { autoPost: false, notifications: true }
    }, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    
    if (updateResponse.data.success) {
      logTest('integrations', 'Update integration', true, 'Integration updated successfully');
    } else {
      logTest('integrations', 'Update integration', false);
    }
    
    return true;
    
  } catch (error) {
    logTest('integrations', 'Integrations system', false, error.message);
    return false;
  }
}

async function cleanup() {
  console.log('\n🧹 CLEANING UP TEST DATA...');
  
  try {
    // Delete test content
    if (testContentId) {
      await axios.delete(`${BASE_URL}/content/${testContentId}`, {
        headers: { Authorization: `Bearer ${authToken}` }
      });
      logTest('cleanup', 'Delete test content', true);
    }
    
    // Delete test integration
    if (testIntegrationId) {
      await axios.delete(`${BASE_URL}/integrations/${testIntegrationId}`, {
        headers: { Authorization: `Bearer ${authToken}` }
      });
      logTest('cleanup', 'Delete test integration', true);
    }
    
    // Delete test project
    if (testProjectId) {
      await axios.delete(`${BASE_URL}/projects/${testProjectId}`, {
        headers: { Authorization: `Bearer ${authToken}` }
      });
      logTest('cleanup', 'Delete test project', true);
    }
    
  } catch (error) {
    logTest('cleanup', 'Cleanup process', false, error.message);
  }
}

function printDetailedResults() {
  console.log('\n📊 COMPREHENSIVE TEST RESULTS:');
  console.log('=====================================');
  
  // Overall summary
  const totalTests = testResults.passed + testResults.failed;
  const successRate = ((testResults.passed / totalTests) * 100).toFixed(1);
  
  console.log(`\n🎯 OVERALL SUMMARY:`);
  console.log(`   Total Tests: ${totalTests}`);
  console.log(`   ✅ Passed: ${testResults.passed}`);
  console.log(`   ❌ Failed: ${testResults.failed}`);
  console.log(`   📈 Success Rate: ${successRate}%`);
  
  // Category breakdown
  console.log(`\n📋 CATEGORY BREAKDOWN:`);
  Object.entries(testResults.categories).forEach(([category, results]) => {
    const categoryTotal = results.passed + results.failed;
    const categoryRate = categoryTotal > 0 ? ((results.passed / categoryTotal) * 100).toFixed(1) : '0.0';
    console.log(`   ${category.toUpperCase()}: ${results.passed}/${categoryTotal} (${categoryRate}%)`);
  });
  
  // Failed tests details
  if (testResults.failed > 0) {
    console.log(`\n❌ FAILED TESTS:`);
    testResults.tests.filter(t => !t.passed).forEach(test => {
      console.log(`   [${test.category.toUpperCase()}] ${test.name}: ${test.details}`);
    });
  }
  
  // Success indicators
  if (successRate >= 95) {
    console.log(`\n🏆 EXCELLENT! Your platform is production-ready!`);
  } else if (successRate >= 85) {
    console.log(`\n🎉 GREAT! Your platform is nearly production-ready!`);
  } else if (successRate >= 70) {
    console.log(`\n👍 GOOD! Your platform needs minor fixes before production.`);
  } else {
    console.log(`\n⚠️  NEEDS WORK! Your platform requires significant fixes.`);
  }
}

async function runComprehensiveTests() {
  console.log('🚀 STARTING COMPREHENSIVE E2E TESTING SUITE...\n');
  console.log('Testing against:', BASE_URL);
  console.log('Timestamp:', new Date().toISOString());
  
  const authSuccess = await testAuthentication();
  if (!authSuccess) {
    console.log('\n❌ Authentication failed. Cannot continue with other tests.');
    return;
  }
  
  await testProjects();
  await testContent();
  await testAnalytics();
  await testIntegrations();
  await cleanup();
  
  printDetailedResults();
  
  console.log('\n🎉 COMPREHENSIVE E2E TESTING COMPLETE!');
  console.log(`⏱️  Completed at: ${new Date().toISOString()}`);
}

// Run the comprehensive test suite
runComprehensiveTests().catch(console.error);
