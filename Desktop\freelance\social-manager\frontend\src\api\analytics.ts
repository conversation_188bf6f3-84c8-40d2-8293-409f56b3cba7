import { apiClient } from './client';

export interface AnalyticsReport {
  organizationId: string;
  period: {
    start: string;
    end: string;
  };
  overview: OverviewMetrics;
  platformBreakdown: PlatformMetrics[];
  contentPerformance: ContentMetrics[];
  audienceInsights: AudienceMetrics;
  trends: TrendAnalysis;
  recommendations: Recommendation[];
}

export interface OverviewMetrics {
  totalPosts: number;
  totalEngagement: number;
  totalReach: number;
  totalImpressions: number;
  averageEngagementRate: number;
  followerGrowth: number;
  topPerformingPlatform: string;
  bestPerformingContent: string;
}

export interface PlatformMetrics {
  platform: string;
  posts: number;
  engagement: number;
  reach: number;
  impressions: number;
  engagementRate: number;
  followerCount: number;
  followerGrowth: number;
  bestPostingTimes: { hour: number; engagementRate: number }[];
}

export interface ContentMetrics {
  contentId: string;
  title: string;
  platform: string;
  publishedAt: string;
  engagement: number;
  reach: number;
  impressions: number;
  engagementRate: number;
  clicks: number;
  shares: number;
  comments: number;
  likes: number;
  contentType: string;
  performance: 'excellent' | 'good' | 'average' | 'poor';
}

export interface AudienceMetrics {
  totalFollowers: number;
  followerGrowth: number;
  demographics: {
    ageGroups: { range: string; percentage: number }[];
    genders: { gender: string; percentage: number }[];
    locations: { country: string; percentage: number }[];
  };
  interests: { category: string; percentage: number }[];
  activeHours: { hour: number; activity: number }[];
}

export interface TrendAnalysis {
  engagementTrend: { date: string; value: number }[];
  reachTrend: { date: string; value: number }[];
  followerTrend: { date: string; value: number }[];
  contentTypeTrends: { type: string; trend: 'up' | 'down' | 'stable'; change: number }[];
  hashtagPerformance: { hashtag: string; usage: number; avgEngagement: number }[];
}

export interface Recommendation {
  type: 'content' | 'timing' | 'platform' | 'audience';
  priority: 'high' | 'medium' | 'low';
  title: string;
  description: string;
  actionItems: string[];
  expectedImpact: string;
}

export interface CustomDashboard {
  id: string;
  name: string;
  widgets: DashboardWidget[];
  layout: DashboardLayout;
  isDefault: boolean;
}

export interface DashboardWidget {
  id: string;
  type: 'metric' | 'chart' | 'table' | 'text';
  title: string;
  config: WidgetConfig;
  position: { x: number; y: number; width: number; height: number };
}

export interface WidgetConfig {
  metric?: string;
  chartType?: 'line' | 'bar' | 'pie' | 'area';
  timeRange?: string;
  platforms?: string[];
  filters?: Record<string, any>;
}

export interface DashboardLayout {
  columns: number;
  rows: number;
  gridSize: number;
}

export interface RealtimeMetrics {
  activeUsers: number;
  todayPosts: number;
  todayEngagement: number;
  liveMetrics: { platform: string; metric: string; value: number; change: number }[];
}

export interface CompetitorAnalysis {
  competitors: {
    handle: string;
    platform: string;
    followers: number;
    avgEngagement: number;
    postFrequency: number;
    topContent: any[];
  }[];
  insights: string[];
}

export interface PredictiveInsights {
  predictions: {
    metric: string;
    currentValue: number;
    predictedValue: number;
    confidence: number;
    trend: 'up' | 'down' | 'stable';
  }[];
  recommendations: string[];
}

export interface ContentPerformanceData {
  content: ContentMetrics[];
  summary: {
    totalContent: number;
    averageEngagement: number;
    topPerformer: ContentMetrics;
    performanceDistribution: {
      excellent: number;
      good: number;
      average: number;
      poor: number;
    };
  };
}

export const analyticsApi = {
  // Comprehensive Analytics Report
  async generateReport(
    startDate: string,
    endDate: string,
    platforms?: string[]
  ): Promise<AnalyticsReport> {
    const params = new URLSearchParams();
    params.append('startDate', startDate);
    params.append('endDate', endDate);
    if (platforms && platforms.length > 0) {
      params.append('platforms', platforms.join(','));
    }

    const response = await apiClient.get(`/analytics/report?${params.toString()}`);
    return response.data.data;
  },

  // Dashboard Overview
  async getOverview(period: string = '30d'): Promise<any> {
    const response = await apiClient.get(`/analytics/overview?period=${period}`);
    return response.data.data;
  },

  // Real-time Metrics
  async getRealtimeMetrics(): Promise<RealtimeMetrics> {
    const response = await apiClient.get('/analytics/realtime');
    return response.data.data;
  },

  // Competitor Analysis
  async getCompetitorAnalysis(
    competitors: string[],
    platforms: string[]
  ): Promise<CompetitorAnalysis> {
    const params = new URLSearchParams();
    params.append('competitors', competitors.join(','));
    params.append('platforms', platforms.join(','));

    const response = await apiClient.get(`/analytics/competitor-analysis?${params.toString()}`);
    return response.data.data;
  },

  // Predictive Analytics
  async getPredictiveInsights(
    timeframe: 'week' | 'month' | 'quarter' = 'month'
  ): Promise<PredictiveInsights> {
    const response = await apiClient.get(`/analytics/predictions?timeframe=${timeframe}`);
    return response.data.data;
  },

  // Content Performance
  async getContentPerformance(
    startDate: string,
    endDate: string,
    platform?: string,
    sortBy?: string,
    limit?: number
  ): Promise<ContentPerformanceData> {
    const params = new URLSearchParams();
    params.append('startDate', startDate);
    params.append('endDate', endDate);
    if (platform) params.append('platform', platform);
    if (sortBy) params.append('sortBy', sortBy);
    if (limit) params.append('limit', limit.toString());

    const response = await apiClient.get(`/analytics/content-performance?${params.toString()}`);
    return response.data.data;
  },

  // Platform Analytics
  async getPlatformAnalytics(
    period: string = '30d',
    platforms?: string[]
  ): Promise<{ platformBreakdown: PlatformMetrics[]; period: string }> {
    const params = new URLSearchParams();
    params.append('period', period);
    if (platforms && platforms.length > 0) {
      params.append('platforms', platforms.join(','));
    }

    const response = await apiClient.get(`/analytics/platforms?${params.toString()}`);
    return response.data.data;
  },

  // Custom Dashboards
  async getCustomDashboards(): Promise<CustomDashboard[]> {
    const response = await apiClient.get('/analytics/dashboards');
    return response.data.data;
  },

  async createCustomDashboard(
    name: string,
    widgets: DashboardWidget[],
    layout: DashboardLayout
  ): Promise<CustomDashboard> {
    const response = await apiClient.post('/analytics/dashboards', {
      name,
      widgets,
      layout
    });
    return response.data.data;
  },

  // Export Reports
  async exportReport(
    startDate: string,
    endDate: string,
    format: 'pdf' | 'excel' | 'csv',
    platforms?: string[]
  ): Promise<{ downloadUrl: string; filename: string }> {
    const response = await apiClient.post('/analytics/export', {
      startDate,
      endDate,
      format,
      platforms
    });
    return response.data.data;
  },

  // Utility Functions
  formatMetric(value: number, type: 'number' | 'percentage' | 'currency' = 'number'): string {
    switch (type) {
      case 'percentage':
        return `${(value * 100).toFixed(1)}%`;
      case 'currency':
        return new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: 'USD'
        }).format(value);
      default:
        return new Intl.NumberFormat('en-US').format(value);
    }
  },

  getPerformanceColor(performance: ContentMetrics['performance']): string {
    const colors = {
      excellent: '#10B981',
      good: '#3B82F6',
      average: '#F59E0B',
      poor: '#EF4444'
    };
    return colors[performance];
  },

  getTrendIcon(trend: 'up' | 'down' | 'stable'): string {
    const icons = {
      up: '📈',
      down: '📉',
      stable: '➡️'
    };
    return icons[trend];
  },

  calculateGrowthRate(current: number, previous: number): number {
    if (previous === 0) return current > 0 ? 100 : 0;
    return ((current - previous) / previous) * 100;
  },

  getEngagementRateCategory(rate: number): 'low' | 'average' | 'good' | 'excellent' {
    if (rate >= 0.06) return 'excellent';
    if (rate >= 0.03) return 'good';
    if (rate >= 0.01) return 'average';
    return 'low';
  },

  getBestPostingTime(platformMetrics: PlatformMetrics): string {
    if (platformMetrics.bestPostingTimes.length === 0) return 'No data';

    const bestTime = platformMetrics.bestPostingTimes[0];
    const hour = bestTime.hour;
    const period = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;

    return `${displayHour}:00 ${period}`;
  }
};
