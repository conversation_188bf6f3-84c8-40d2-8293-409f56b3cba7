import { useState } from 'react'
import { useAnalyticsOverview, usePlatformAnalytics } from '../../hooks/useAnalytics'
import { useProjects } from '../../hooks/useProjects'
import LoadingSpinner from '../../components/ui/LoadingSpinner'
import AnalyticsOverviewCards from '../../components/analytics/AnalyticsOverviewCards'
import ContentChart from '../../components/analytics/ContentChart'
import PlatformChart from '../../components/analytics/PlatformChart'
import ProjectPerformance from '../../components/analytics/ProjectPerformance'
import TopPerformingContent from '../../components/analytics/TopPerformingContent'
import {
  CalendarIcon,
  ArrowDownTrayIcon,
  FunnelIcon,
} from '@heroicons/react/24/outline'

const AnalyticsPage: React.FC = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('30d')
  const [selectedProject, setSelectedProject] = useState('')

  const { data: analyticsData, isLoading: analyticsLoading } = useAnalyticsOverview(selectedPeriod, selectedProject)
  const { data: platformData, isLoading: platformLoading } = usePlatformAnalytics(selectedPeriod)
  const { data: projects } = useProjects()

  const periodOptions = [
    { value: '7d', label: 'Last 7 days' },
    { value: '30d', label: 'Last 30 days' },
    { value: '90d', label: 'Last 90 days' },
    { value: '1y', label: 'Last year' },
  ]

  const handleExportReport = () => {
    // In a real app, this would generate and download a PDF/Excel report
    alert('Export functionality would be implemented here')
  }

  if (analyticsLoading || platformLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Analytics</h1>
          <p className="text-gray-600">Track your social media performance and engagement</p>
        </div>

        <div className="flex items-center space-x-3">
          {/* Period Selector */}
          <div className="flex items-center space-x-2">
            <CalendarIcon className="h-5 w-5 text-gray-400" />
            <select
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(e.target.value)}
              className="input w-auto"
            >
              {periodOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          {/* Project Filter */}
          <div className="flex items-center space-x-2">
            <FunnelIcon className="h-5 w-5 text-gray-400" />
            <select
              value={selectedProject}
              onChange={(e) => setSelectedProject(e.target.value)}
              className="input w-auto"
            >
              <option value="">All projects</option>
              {projects?.map((project) => (
                <option key={project.id} value={project.id}>
                  {project.name}
                </option>
              ))}
            </select>
          </div>

          <button
            onClick={handleExportReport}
            className="btn-outline flex items-center space-x-2"
          >
            <ArrowDownTrayIcon className="h-5 w-5" />
            <span>Export Report</span>
          </button>
        </div>
      </div>

      {analyticsData && (
        <>
          {/* Overview Cards */}
          <AnalyticsOverviewCards data={analyticsData.overview} />

          {/* Charts Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Content Creation Chart */}
            <div className="card">
              <div className="card-header">
                <h3 className="card-title">Content Creation Trend</h3>
                <p className="card-description">Daily content creation and publishing</p>
              </div>
              <div className="card-content">
                <ContentChart data={analyticsData.dailyStats} />
              </div>
            </div>

            {/* Platform Distribution */}
            <div className="card">
              <div className="card-header">
                <h3 className="card-title">Platform Distribution</h3>
                <p className="card-description">Content distribution across platforms</p>
              </div>
              <div className="card-content">
                <PlatformChart data={analyticsData.platformStats} />
              </div>
            </div>
          </div>

          {/* Content Type Distribution */}
          <div className="card">
            <div className="card-header">
              <h3 className="card-title">Content Type Performance</h3>
              <p className="card-description">Performance breakdown by content type</p>
            </div>
            <div className="card-content">
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
                {analyticsData.contentByType.map((item) => (
                  <div key={item.type} className="text-center p-4 bg-gray-50 rounded-lg">
                    <div className="text-2xl mb-2">
                      {item.type === 'POST' && '📝'}
                      {item.type === 'STORY' && '📖'}
                      {item.type === 'REEL' && '🎬'}
                      {item.type === 'VIDEO' && '🎥'}
                      {item.type === 'ARTICLE' && '📄'}
                      {item.type === 'CAROUSEL' && '🎠'}
                    </div>
                    <div className="text-lg font-semibold text-gray-900">{item.count}</div>
                    <div className="text-sm text-gray-600">{item.type}</div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Project Performance & Top Content */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <ProjectPerformance data={analyticsData.projectPerformance} />
            <TopPerformingContent data={analyticsData.engagement.topPerformingContent} />
          </div>

          {/* Engagement Overview */}
          <div className="card">
            <div className="card-header">
              <h3 className="card-title">Engagement Overview</h3>
              <p className="card-description">Total engagement metrics across all platforms</p>
            </div>
            <div className="card-content">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-600">
                    {analyticsData.engagement.totalReach.toLocaleString()}
                  </div>
                  <div className="text-sm text-gray-600">Total Reach</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-green-600">
                    {analyticsData.engagement.totalImpressions.toLocaleString()}
                  </div>
                  <div className="text-sm text-gray-600">Total Impressions</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-purple-600">
                    {analyticsData.engagement.totalEngagements.toLocaleString()}
                  </div>
                  <div className="text-sm text-gray-600">Total Engagements</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-orange-600">
                    {analyticsData.engagement.engagementRate}%
                  </div>
                  <div className="text-sm text-gray-600">Avg. Engagement Rate</div>
                </div>
              </div>
            </div>
          </div>
        </>
      )}

      {/* Platform Comparison */}
      {platformData && (
        <div className="card">
          <div className="card-header">
            <h3 className="card-title">Platform Performance Comparison</h3>
            <p className="card-description">Compare performance across different social media platforms</p>
          </div>
          <div className="card-content">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Platform
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Total Content
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Published
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Scheduled
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Avg. Engagement
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Total Reach
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {platformData.platformEngagement.map((platform) => (
                    <tr key={platform.platform}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <span className="text-lg mr-2">
                            {platform.platform === 'INSTAGRAM' && '📷'}
                            {platform.platform === 'FACEBOOK' && '👥'}
                            {platform.platform === 'TWITTER' && '🐦'}
                            {platform.platform === 'LINKEDIN' && '💼'}
                            {platform.platform === 'TIKTOK' && '🎵'}
                            {platform.platform === 'YOUTUBE' && '📺'}
                            {platform.platform === 'PINTEREST' && '📌'}
                          </span>
                          <span className="text-sm font-medium text-gray-900">
                            {platform.platform}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {platform.total}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {platform.published}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {platform.scheduled}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {platform.avgEngagementRate}%
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {platform.totalReach.toLocaleString()}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default AnalyticsPage
