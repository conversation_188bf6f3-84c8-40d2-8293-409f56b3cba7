import OpenAI from 'openai';
import { PrismaClient } from '@prisma/client';
import { logger } from '../utils/logger.js';
import { CacheService } from '../config/redis.js';

export interface SentimentAnalysisResult {
  sentiment: 'positive' | 'negative' | 'neutral';
  confidence: number;
  score: number; // -1 to 1 scale
  emotions: {
    joy: number;
    anger: number;
    fear: number;
    sadness: number;
    surprise: number;
    disgust: number;
  };
  keywords: string[];
  topics: string[];
  urgency: 'low' | 'medium' | 'high' | 'critical';
}

export interface BrandMention {
  id: string;
  platform: string;
  content: string;
  author: string;
  authorFollowers?: number;
  url?: string;
  timestamp: Date;
  sentiment: SentimentAnalysisResult;
  reach: number;
  engagement: number;
  isInfluencer: boolean;
  requiresResponse: boolean;
  responseGenerated?: string;
  tags: string[];
}

export interface BrandMonitoringConfig {
  organizationId: string;
  keywords: string[];
  brandNames: string[];
  competitors: string[];
  platforms: string[];
  languages: string[];
  sentimentThreshold: number;
  influencerThreshold: number;
  autoResponse: boolean;
  alertSettings: {
    negativeThreshold: number;
    volumeThreshold: number;
    influencerMentions: boolean;
    competitorMentions: boolean;
  };
}

export interface SentimentTrend {
  period: string;
  positive: number;
  negative: number;
  neutral: number;
  volume: number;
  averageScore: number;
  topKeywords: string[];
  influencerMentions: number;
}

export interface BrandHealthScore {
  overall: number;
  sentiment: number;
  volume: number;
  engagement: number;
  reach: number;
  influencerSentiment: number;
  competitorComparison: number;
  trends: {
    daily: SentimentTrend[];
    weekly: SentimentTrend[];
    monthly: SentimentTrend[];
  };
}

export class SentimentAnalysisService {
  private openai: OpenAI;
  private prisma: PrismaClient;
  private monitoringConfigs: Map<string, BrandMonitoringConfig>;

  constructor(prisma: PrismaClient) {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY
    });
    this.prisma = prisma;
    this.monitoringConfigs = new Map();
    this.initializeMonitoring();
  }

  /**
   * Analyze sentiment of text content
   */
  async analyzeSentiment(
    content: string,
    context?: {
      platform?: string;
      author?: string;
      organizationId?: string;
    }
  ): Promise<SentimentAnalysisResult> {
    try {
      // Check cache first
      const cacheKey = `sentiment:${this.hashContent(content)}`;
      const cached = await CacheService.get(cacheKey);
      if (cached) {
        return JSON.parse(cached);
      }

      // Analyze using OpenAI
      const prompt = this.buildSentimentPrompt(content, context);
      
      const completion = await this.openai.chat.completions.create({
        model: 'gpt-4-turbo-preview',
        messages: [
          {
            role: 'system',
            content: 'You are an expert sentiment analysis AI. Analyze text for sentiment, emotions, and urgency with high accuracy.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 500,
        temperature: 0.1
      });

      const response = completion.choices[0]?.message?.content;
      if (!response) {
        throw new Error('No sentiment analysis generated');
      }

      const result = this.parseSentimentResponse(response, content);

      // Cache result for 1 hour
      await CacheService.set(cacheKey, JSON.stringify(result), 3600);

      return result;
    } catch (error) {
      logger.error('Error analyzing sentiment:', error);
      return this.getFallbackSentiment(content);
    }
  }

  /**
   * Monitor brand mentions across platforms
   */
  async monitorBrandMentions(organizationId: string): Promise<BrandMention[]> {
    try {
      const config = this.monitoringConfigs.get(organizationId);
      if (!config) {
        logger.warn(`No monitoring config found for organization: ${organizationId}`);
        return [];
      }

      const mentions: BrandMention[] = [];

      // Monitor each platform
      for (const platform of config.platforms) {
        const platformMentions = await this.searchPlatformMentions(platform, config);
        mentions.push(...platformMentions);
      }

      // Analyze and process mentions
      const processedMentions = await this.processMentions(mentions, config);

      // Store mentions in database
      await this.storeMentions(processedMentions, organizationId);

      // Check for alerts
      await this.checkAlerts(processedMentions, config);

      logger.info(`Found ${processedMentions.length} brand mentions for organization ${organizationId}`);

      return processedMentions;
    } catch (error) {
      logger.error('Error monitoring brand mentions:', error);
      return [];
    }
  }

  /**
   * Generate brand health score
   */
  async generateBrandHealthScore(organizationId: string): Promise<BrandHealthScore> {
    try {
      const cacheKey = `brand_health:${organizationId}`;
      const cached = await CacheService.get(cacheKey);
      if (cached) {
        return JSON.parse(cached);
      }

      // Get recent mentions (last 30 days)
      const mentions = await this.getRecentMentions(organizationId, 30);

      // Calculate sentiment score
      const sentimentScore = this.calculateSentimentScore(mentions);

      // Calculate volume score
      const volumeScore = this.calculateVolumeScore(mentions);

      // Calculate engagement score
      const engagementScore = this.calculateEngagementScore(mentions);

      // Calculate reach score
      const reachScore = this.calculateReachScore(mentions);

      // Calculate influencer sentiment
      const influencerSentiment = this.calculateInfluencerSentiment(mentions);

      // Calculate competitor comparison
      const competitorComparison = await this.calculateCompetitorComparison(organizationId);

      // Generate trends
      const trends = await this.generateSentimentTrends(organizationId);

      // Calculate overall score
      const overall = (
        sentimentScore * 0.3 +
        volumeScore * 0.2 +
        engagementScore * 0.2 +
        reachScore * 0.15 +
        influencerSentiment * 0.1 +
        competitorComparison * 0.05
      );

      const healthScore: BrandHealthScore = {
        overall: Math.round(overall * 100) / 100,
        sentiment: sentimentScore,
        volume: volumeScore,
        engagement: engagementScore,
        reach: reachScore,
        influencerSentiment,
        competitorComparison,
        trends
      };

      // Cache for 2 hours
      await CacheService.set(cacheKey, JSON.stringify(healthScore), 7200);

      return healthScore;
    } catch (error) {
      logger.error('Error generating brand health score:', error);
      throw error;
    }
  }

  /**
   * Generate automated response to negative mentions
   */
  async generateResponse(
    mention: BrandMention,
    organizationId: string
  ): Promise<string> {
    try {
      const config = this.monitoringConfigs.get(organizationId);
      if (!config?.autoResponse) {
        return '';
      }

      // Get organization context
      const orgContext = await this.getOrganizationContext(organizationId);

      const prompt = `Generate a professional, empathetic response to this ${mention.sentiment.sentiment} brand mention:

Original mention: "${mention.content}"
Platform: ${mention.platform}
Author: ${mention.author}

Organization context:
${orgContext}

Requirements:
- Be professional and empathetic
- Address the concern if negative
- Thank for positive feedback if positive
- Keep it concise (under 280 characters for Twitter, under 500 for others)
- Include appropriate tone for the platform
- Don't be overly promotional

Generate only the response text, no additional formatting.`;

      const completion = await this.openai.chat.completions.create({
        model: 'gpt-4-turbo-preview',
        messages: [
          {
            role: 'system',
            content: 'You are a professional social media manager creating responses to brand mentions.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 200,
        temperature: 0.7
      });

      const response = completion.choices[0]?.message?.content?.trim() || '';

      // Store generated response
      mention.responseGenerated = response;
      await this.updateMention(mention);

      return response;
    } catch (error) {
      logger.error('Error generating response:', error);
      return '';
    }
  }

  /**
   * Set up brand monitoring configuration
   */
  async setupBrandMonitoring(config: BrandMonitoringConfig): Promise<void> {
    try {
      this.monitoringConfigs.set(config.organizationId, config);

      // Save to database
      await this.saveBrandMonitoringConfig(config);

      logger.info(`Brand monitoring configured for organization: ${config.organizationId}`);
    } catch (error) {
      logger.error('Error setting up brand monitoring:', error);
      throw error;
    }
  }

  /**
   * Private helper methods
   */
  private buildSentimentPrompt(content: string, context?: any): string {
    return `Analyze the sentiment of this text and provide a detailed analysis:

"${content}"

${context ? `Context: Platform: ${context.platform}, Author: ${context.author}` : ''}

Provide analysis in this JSON format:
{
  "sentiment": "positive|negative|neutral",
  "confidence": 0.0-1.0,
  "score": -1.0 to 1.0,
  "emotions": {
    "joy": 0.0-1.0,
    "anger": 0.0-1.0,
    "fear": 0.0-1.0,
    "sadness": 0.0-1.0,
    "surprise": 0.0-1.0,
    "disgust": 0.0-1.0
  },
  "keywords": ["keyword1", "keyword2"],
  "topics": ["topic1", "topic2"],
  "urgency": "low|medium|high|critical"
}`;
  }

  private parseSentimentResponse(response: string, content: string): SentimentAnalysisResult {
    try {
      const parsed = JSON.parse(response);
      return {
        sentiment: parsed.sentiment || 'neutral',
        confidence: parsed.confidence || 0.5,
        score: parsed.score || 0,
        emotions: parsed.emotions || {
          joy: 0, anger: 0, fear: 0, sadness: 0, surprise: 0, disgust: 0
        },
        keywords: parsed.keywords || [],
        topics: parsed.topics || [],
        urgency: parsed.urgency || 'low'
      };
    } catch (error) {
      logger.error('Error parsing sentiment response:', error);
      return this.getFallbackSentiment(content);
    }
  }

  private getFallbackSentiment(content: string): SentimentAnalysisResult {
    // Simple fallback sentiment analysis
    const positiveWords = ['good', 'great', 'excellent', 'amazing', 'love', 'awesome', 'fantastic'];
    const negativeWords = ['bad', 'terrible', 'awful', 'hate', 'horrible', 'worst', 'disappointing'];

    const words = content.toLowerCase().split(/\s+/);
    let positiveCount = 0;
    let negativeCount = 0;

    words.forEach(word => {
      if (positiveWords.includes(word)) positiveCount++;
      if (negativeWords.includes(word)) negativeCount++;
    });

    let sentiment: 'positive' | 'negative' | 'neutral' = 'neutral';
    let score = 0;

    if (positiveCount > negativeCount) {
      sentiment = 'positive';
      score = 0.3;
    } else if (negativeCount > positiveCount) {
      sentiment = 'negative';
      score = -0.3;
    }

    return {
      sentiment,
      confidence: 0.5,
      score,
      emotions: { joy: 0, anger: 0, fear: 0, sadness: 0, surprise: 0, disgust: 0 },
      keywords: [],
      topics: [],
      urgency: 'low'
    };
  }

  private hashContent(content: string): string {
    // Simple hash function for caching
    let hash = 0;
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString();
  }

  private async searchPlatformMentions(platform: string, config: BrandMonitoringConfig): Promise<BrandMention[]> {
    // This would integrate with platform APIs to search for mentions
    // For now, return mock data
    return [];
  }

  private async processMentions(mentions: BrandMention[], config: BrandMonitoringConfig): Promise<BrandMention[]> {
    const processed = [];

    for (const mention of mentions) {
      // Analyze sentiment
      mention.sentiment = await this.analyzeSentiment(mention.content, {
        platform: mention.platform,
        author: mention.author
      });

      // Determine if response is required
      mention.requiresResponse = this.shouldGenerateResponse(mention, config);

      // Check if author is influencer
      mention.isInfluencer = (mention.authorFollowers || 0) >= config.influencerThreshold;

      // Add tags
      mention.tags = this.generateTags(mention, config);

      processed.push(mention);
    }

    return processed;
  }

  private shouldGenerateResponse(mention: BrandMention, config: BrandMonitoringConfig): boolean {
    return config.autoResponse && 
           (mention.sentiment.sentiment === 'negative' || mention.sentiment.urgency === 'high');
  }

  private generateTags(mention: BrandMention, config: BrandMonitoringConfig): string[] {
    const tags = [];

    if (mention.sentiment.sentiment === 'negative') tags.push('negative');
    if (mention.sentiment.urgency === 'high' || mention.sentiment.urgency === 'critical') tags.push('urgent');
    if (mention.isInfluencer) tags.push('influencer');
    if (mention.requiresResponse) tags.push('needs_response');

    return tags;
  }

  private async storeMentions(mentions: BrandMention[], organizationId: string): Promise<void> {
    // Store mentions in database
    for (const mention of mentions) {
      try {
        await this.prisma.brandMention.create({
          data: {
            platform: mention.platform,
            content: mention.content,
            author: mention.author,
            authorFollowers: mention.authorFollowers,
            url: mention.url,
            timestamp: mention.timestamp,
            sentiment: mention.sentiment.sentiment,
            sentimentScore: mention.sentiment.score,
            sentimentConfidence: mention.sentiment.confidence,
            reach: mention.reach,
            engagement: mention.engagement,
            isInfluencer: mention.isInfluencer,
            requiresResponse: mention.requiresResponse,
            responseGenerated: mention.responseGenerated,
            tags: mention.tags,
            organizationId
          }
        });
      } catch (error) {
        logger.error('Error storing mention:', error);
      }
    }
  }

  private async checkAlerts(mentions: BrandMention[], config: BrandMonitoringConfig): Promise<void> {
    // Check for alert conditions and send notifications
    const negativeMentions = mentions.filter(m => m.sentiment.sentiment === 'negative');
    const influencerMentions = mentions.filter(m => m.isInfluencer);

    if (negativeMentions.length >= config.alertSettings.negativeThreshold) {
      await this.sendAlert('negative_threshold', config.organizationId, {
        count: negativeMentions.length,
        mentions: negativeMentions.slice(0, 5)
      });
    }

    if (influencerMentions.length > 0 && config.alertSettings.influencerMentions) {
      await this.sendAlert('influencer_mention', config.organizationId, {
        mentions: influencerMentions
      });
    }
  }

  private async sendAlert(type: string, organizationId: string, data: any): Promise<void> {
    // Send alert notification
    logger.info(`Sending ${type} alert for organization ${organizationId}`, data);
  }

  private initializeMonitoring(): void {
    // Load monitoring configurations from database
    this.loadMonitoringConfigs();
    
    // Start monitoring scheduler
    this.startMonitoringScheduler();
  }

  private async loadMonitoringConfigs(): Promise<void> {
    // Load configurations from database
    logger.info('Loading brand monitoring configurations...');
  }

  private startMonitoringScheduler(): void {
    // Run monitoring every 15 minutes
    setInterval(() => {
      this.runScheduledMonitoring();
    }, 15 * 60 * 1000);
  }

  private async runScheduledMonitoring(): Promise<void> {
    for (const [orgId, config] of this.monitoringConfigs) {
      try {
        await this.monitorBrandMentions(orgId);
      } catch (error) {
        logger.error(`Error in scheduled monitoring for ${orgId}:`, error);
      }
    }
  }

  // Additional helper methods for calculations
  private async getRecentMentions(organizationId: string, days: number): Promise<BrandMention[]> {
    // Fetch recent mentions from database
    return [];
  }

  private calculateSentimentScore(mentions: BrandMention[]): number {
    if (mentions.length === 0) return 0.5;
    
    const avgScore = mentions.reduce((sum, m) => sum + m.sentiment.score, 0) / mentions.length;
    return (avgScore + 1) / 2; // Convert from -1,1 to 0,1 scale
  }

  private calculateVolumeScore(mentions: BrandMention[]): number {
    // Calculate volume score based on mention count
    return Math.min(mentions.length / 100, 1); // Normalize to 0-1
  }

  private calculateEngagementScore(mentions: BrandMention[]): number {
    if (mentions.length === 0) return 0;
    
    const avgEngagement = mentions.reduce((sum, m) => sum + m.engagement, 0) / mentions.length;
    return Math.min(avgEngagement / 1000, 1); // Normalize
  }

  private calculateReachScore(mentions: BrandMention[]): number {
    if (mentions.length === 0) return 0;
    
    const totalReach = mentions.reduce((sum, m) => sum + m.reach, 0);
    return Math.min(totalReach / 100000, 1); // Normalize
  }

  private calculateInfluencerSentiment(mentions: BrandMention[]): number {
    const influencerMentions = mentions.filter(m => m.isInfluencer);
    if (influencerMentions.length === 0) return 0.5;
    
    const avgScore = influencerMentions.reduce((sum, m) => sum + m.sentiment.score, 0) / influencerMentions.length;
    return (avgScore + 1) / 2;
  }

  private async calculateCompetitorComparison(organizationId: string): Promise<number> {
    // Compare brand sentiment with competitors
    return 0.6; // Placeholder
  }

  private async generateSentimentTrends(organizationId: string): Promise<any> {
    // Generate sentiment trends over time
    return {
      daily: [],
      weekly: [],
      monthly: []
    };
  }

  private async getOrganizationContext(organizationId: string): Promise<string> {
    // Get organization information for context
    return 'Professional technology company focused on innovation and customer satisfaction.';
  }

  private async updateMention(mention: BrandMention): Promise<void> {
    // Update mention in database
    logger.debug(`Updating mention: ${mention.id}`);
  }

  private async saveBrandMonitoringConfig(config: BrandMonitoringConfig): Promise<void> {
    // Save configuration to database
    logger.debug(`Saving brand monitoring config for: ${config.organizationId}`);
  }
}
