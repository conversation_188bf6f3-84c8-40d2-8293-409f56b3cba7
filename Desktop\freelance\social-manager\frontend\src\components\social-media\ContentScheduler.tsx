import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Textarea } from '../ui/textarea';
import { Badge } from '../ui/badge';
import { Checkbox } from '../ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Calendar } from '../ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '../ui/popover';
import { 
  Calendar as CalendarIcon,
  Clock,
  Send,
  Image,
  Video,
  Hash,
  Zap,
  Plus,
  X,
  AlertCircle,
  CheckCircle
} from 'lucide-react';
import { format } from 'date-fns';
import useSocialMedia from '../../hooks/useSocialMedia';

const PLATFORM_CONFIG = {
  INSTAGRAM: { name: 'Instagram', icon: '📷', color: '#E4405F', maxLength: 2200 },
  FACEBOOK: { name: 'Facebook', icon: '👥', color: '#1877F2', maxLength: 63206 },
  TWITTER: { name: 'Twitter', icon: '🐦', color: '#1DA1F2', maxLength: 280 },
  LINKEDIN: { name: 'LinkedIn', icon: '💼', color: '#0A66C2', maxLength: 3000 },
  TIKTOK: { name: 'TikTok', icon: '🎵', color: '#000000', maxLength: 150 },
  YOUTUBE: { name: 'YouTube', icon: '📺', color: '#FF0000', maxLength: 5000 },
  PINTEREST: { name: 'Pinterest', icon: '📌', color: '#BD081C', maxLength: 500 }
};

export const ContentScheduler: React.FC = () => {
  const {
    accounts,
    scheduleContent,
    publishContent,
    getConnectedPlatforms,
    validateContent,
    getOptimalContentLength,
    loading
  } = useSocialMedia();

  const [content, setContent] = useState('');
  const [selectedPlatforms, setSelectedPlatforms] = useState<string[]>([]);
  const [scheduledDate, setScheduledDate] = useState<Date>();
  const [scheduledTime, setScheduledTime] = useState('');
  const [mediaFiles, setMediaFiles] = useState<File[]>([]);
  const [isScheduled, setIsScheduled] = useState(false);
  const [timezone, setTimezone] = useState('UTC');
  const [recurring, setRecurring] = useState(false);
  const [recurringConfig, setRecurringConfig] = useState({
    frequency: 'WEEKLY' as 'DAILY' | 'WEEKLY' | 'MONTHLY',
    interval: 1,
    endDate: '',
    daysOfWeek: [] as number[],
    timeOfDay: '09:00'
  });

  const connectedPlatforms = getConnectedPlatforms();
  const validation = validateContent(content, selectedPlatforms);

  useEffect(() => {
    if (connectedPlatforms.length > 0 && selectedPlatforms.length === 0) {
      setSelectedPlatforms([connectedPlatforms[0]]);
    }
  }, [connectedPlatforms, selectedPlatforms.length]);

  const handlePlatformToggle = (platform: string) => {
    setSelectedPlatforms(prev => 
      prev.includes(platform) 
        ? prev.filter(p => p !== platform)
        : [...prev, platform]
    );
  };

  const handleMediaUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    setMediaFiles(prev => [...prev, ...files]);
  };

  const removeMediaFile = (index: number) => {
    setMediaFiles(prev => prev.filter((_, i) => i !== index));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validation.isValid) return;
    if (selectedPlatforms.length === 0) return;

    try {
      const mediaUrls = mediaFiles.map(file => URL.createObjectURL(file)); // In real app, upload to storage first

      if (isScheduled && scheduledDate && scheduledTime) {
        const scheduledAt = new Date(scheduledDate);
        const [hours, minutes] = scheduledTime.split(':').map(Number);
        scheduledAt.setHours(hours, minutes);

        await scheduleContent({
          content,
          mediaUrls,
          platforms: selectedPlatforms,
          scheduledAt: scheduledAt.toISOString(),
          timezone,
          recurring: recurring ? recurringConfig : undefined
        });
      } else {
        await publishContent({
          content,
          mediaUrls,
          platforms: selectedPlatforms
        });
      }

      // Reset form
      setContent('');
      setSelectedPlatforms([connectedPlatforms[0]]);
      setScheduledDate(undefined);
      setScheduledTime('');
      setMediaFiles([]);
      setIsScheduled(false);
      setRecurring(false);
    } catch (error) {
      console.error('Error submitting content:', error);
    }
  };

  const getCharacterCount = () => {
    if (selectedPlatforms.length === 0) return { current: content.length, max: 0 };
    
    const maxLengths = selectedPlatforms.map(platform => 
      PLATFORM_CONFIG[platform as keyof typeof PLATFORM_CONFIG]?.maxLength || 1000
    );
    
    return {
      current: content.length,
      max: Math.min(...maxLengths)
    };
  };

  const { current: charCount, max: maxChars } = getCharacterCount();
  const isOverLimit = charCount > maxChars;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900">Content Scheduler</h2>
        <p className="text-gray-600 mt-1">
          Create and schedule content across your social media platforms
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Content Creation Form */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Send className="w-5 h-5 mr-2" />
                Create Content
              </CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Platform Selection */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    Select Platforms
                  </label>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                    {connectedPlatforms.map((platform) => {
                      const config = PLATFORM_CONFIG[platform as keyof typeof PLATFORM_CONFIG];
                      const isSelected = selectedPlatforms.includes(platform);
                      
                      return (
                        <div
                          key={platform}
                          className={`relative border-2 rounded-lg p-3 cursor-pointer transition-colors ${
                            isSelected 
                              ? 'border-blue-500 bg-blue-50' 
                              : 'border-gray-200 hover:border-gray-300'
                          }`}
                          onClick={() => handlePlatformToggle(platform)}
                        >
                          <div className="flex items-center space-x-2">
                            <Checkbox 
                              checked={isSelected}
                              onChange={() => {}}
                              className="pointer-events-none"
                            />
                            <span className="text-lg">{config.icon}</span>
                            <span className="text-sm font-medium">{config.name}</span>
                          </div>
                          {isSelected && (
                            <CheckCircle className="absolute top-1 right-1 w-4 h-4 text-blue-500" />
                          )}
                        </div>
                      );
                    })}
                  </div>
                </div>

                {/* Content Input */}
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <label className="block text-sm font-medium text-gray-700">
                      Content
                    </label>
                    <div className={`text-sm ${isOverLimit ? 'text-red-500' : 'text-gray-500'}`}>
                      {charCount}/{maxChars} characters
                    </div>
                  </div>
                  <Textarea
                    value={content}
                    onChange={(e) => setContent(e.target.value)}
                    placeholder="What's happening?"
                    className={`min-h-32 ${isOverLimit ? 'border-red-500' : ''}`}
                    required
                  />
                  {!validation.isValid && (
                    <div className="mt-2 space-y-1">
                      {validation.errors.map((error, index) => (
                        <p key={index} className="text-sm text-red-600 flex items-center">
                          <AlertCircle className="w-4 h-4 mr-1" />
                          {error}
                        </p>
                      ))}
                    </div>
                  )}
                </div>

                {/* Media Upload */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Media (Optional)
                  </label>
                  <div className="space-y-3">
                    <div className="flex space-x-2">
                      <Button type="button" variant="outline" size="sm" asChild>
                        <label className="cursor-pointer">
                          <Image className="w-4 h-4 mr-2" />
                          Add Images
                          <input
                            type="file"
                            multiple
                            accept="image/*"
                            onChange={handleMediaUpload}
                            className="hidden"
                          />
                        </label>
                      </Button>
                      <Button type="button" variant="outline" size="sm" asChild>
                        <label className="cursor-pointer">
                          <Video className="w-4 h-4 mr-2" />
                          Add Videos
                          <input
                            type="file"
                            multiple
                            accept="video/*"
                            onChange={handleMediaUpload}
                            className="hidden"
                          />
                        </label>
                      </Button>
                    </div>
                    
                    {mediaFiles.length > 0 && (
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                        {mediaFiles.map((file, index) => (
                          <div key={index} className="relative group">
                            <div className="aspect-square bg-gray-100 rounded-lg flex items-center justify-center">
                              {file.type.startsWith('image/') ? (
                                <img
                                  src={URL.createObjectURL(file)}
                                  alt={file.name}
                                  className="w-full h-full object-cover rounded-lg"
                                />
                              ) : (
                                <Video className="w-8 h-8 text-gray-400" />
                              )}
                            </div>
                            <Button
                              type="button"
                              variant="destructive"
                              size="sm"
                              className="absolute -top-2 -right-2 w-6 h-6 rounded-full p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                              onClick={() => removeMediaFile(index)}
                            >
                              <X className="w-3 h-3" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>

                {/* Scheduling Options */}
                <div className="border-t pt-6">
                  <div className="flex items-center space-x-2 mb-4">
                    <Checkbox
                      checked={isScheduled}
                      onCheckedChange={setIsScheduled}
                    />
                    <label className="text-sm font-medium text-gray-700">
                      Schedule for later
                    </label>
                  </div>

                  {isScheduled && (
                    <div className="space-y-4 pl-6">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Date
                          </label>
                          <Popover>
                            <PopoverTrigger asChild>
                              <Button variant="outline" className="w-full justify-start">
                                <CalendarIcon className="w-4 h-4 mr-2" />
                                {scheduledDate ? format(scheduledDate, 'PPP') : 'Pick a date'}
                              </Button>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0">
                              <Calendar
                                mode="single"
                                selected={scheduledDate}
                                onSelect={setScheduledDate}
                                disabled={(date) => date < new Date()}
                                initialFocus
                              />
                            </PopoverContent>
                          </Popover>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Time
                          </label>
                          <Input
                            type="time"
                            value={scheduledTime}
                            onChange={(e) => setScheduledTime(e.target.value)}
                            required={isScheduled}
                          />
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Timezone
                        </label>
                        <Select value={timezone} onValueChange={setTimezone}>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="UTC">UTC</SelectItem>
                            <SelectItem value="America/New_York">Eastern Time</SelectItem>
                            <SelectItem value="America/Chicago">Central Time</SelectItem>
                            <SelectItem value="America/Denver">Mountain Time</SelectItem>
                            <SelectItem value="America/Los_Angeles">Pacific Time</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      {/* Recurring Options */}
                      <div className="border-t pt-4">
                        <div className="flex items-center space-x-2 mb-3">
                          <Checkbox
                            checked={recurring}
                            onCheckedChange={setRecurring}
                          />
                          <label className="text-sm font-medium text-gray-700">
                            Repeat this post
                          </label>
                        </div>

                        {recurring && (
                          <div className="space-y-3 pl-6">
                            <div className="grid grid-cols-2 gap-3">
                              <Select 
                                value={recurringConfig.frequency} 
                                onValueChange={(value: 'DAILY' | 'WEEKLY' | 'MONTHLY') => 
                                  setRecurringConfig(prev => ({ ...prev, frequency: value }))
                                }
                              >
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="DAILY">Daily</SelectItem>
                                  <SelectItem value="WEEKLY">Weekly</SelectItem>
                                  <SelectItem value="MONTHLY">Monthly</SelectItem>
                                </SelectContent>
                              </Select>
                              <Input
                                type="number"
                                min="1"
                                value={recurringConfig.interval}
                                onChange={(e) => 
                                  setRecurringConfig(prev => ({ 
                                    ...prev, 
                                    interval: parseInt(e.target.value) 
                                  }))
                                }
                                placeholder="Every X"
                              />
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>

                {/* Submit Buttons */}
                <div className="flex space-x-3 pt-6 border-t">
                  <Button
                    type="submit"
                    disabled={!validation.isValid || selectedPlatforms.length === 0 || loading}
                    className="flex-1"
                  >
                    {loading ? (
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                    ) : isScheduled ? (
                      <Clock className="w-4 h-4 mr-2" />
                    ) : (
                      <Send className="w-4 h-4 mr-2" />
                    )}
                    {isScheduled ? 'Schedule Post' : 'Publish Now'}
                  </Button>
                  
                  <Button type="button" variant="outline">
                    <Zap className="w-4 h-4 mr-2" />
                    Optimal Time
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>

        {/* Preview & Tips */}
        <div className="space-y-6">
          {/* Content Preview */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Preview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {selectedPlatforms.map((platform) => {
                  const config = PLATFORM_CONFIG[platform as keyof typeof PLATFORM_CONFIG];
                  const length = getOptimalContentLength(platform);
                  
                  return (
                    <div key={platform} className="border rounded-lg p-3">
                      <div className="flex items-center space-x-2 mb-2">
                        <span className="text-sm">{config.icon}</span>
                        <span className="text-sm font-medium">{config.name}</span>
                      </div>
                      <p className="text-sm text-gray-700 line-clamp-3">
                        {content || 'Your content will appear here...'}
                      </p>
                      <div className="mt-2 text-xs text-gray-500">
                        Recommended: {length.recommended} chars
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>

          {/* Tips */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center">
                <Hash className="w-4 h-4 mr-2" />
                Tips
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 text-sm">
                <div className="flex items-start space-x-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0" />
                  <p>Use hashtags to increase discoverability</p>
                </div>
                <div className="flex items-start space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0" />
                  <p>Post when your audience is most active</p>
                </div>
                <div className="flex items-start space-x-2">
                  <div className="w-2 h-2 bg-purple-500 rounded-full mt-2 flex-shrink-0" />
                  <p>Include engaging visuals for better performance</p>
                </div>
                <div className="flex items-start space-x-2">
                  <div className="w-2 h-2 bg-orange-500 rounded-full mt-2 flex-shrink-0" />
                  <p>Keep content authentic and engaging</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default ContentScheduler;
