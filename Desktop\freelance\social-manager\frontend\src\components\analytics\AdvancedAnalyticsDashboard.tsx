import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>hart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  <PERSON><PERSON>hart,
  Pie,
  Cell,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar
} from 'recharts';
import {
  TrendingUp,
  TrendingDown,
  Brain,
  Target,
  Users,
  DollarSign,
  AlertTriangle,
  CheckCircle,
  Eye,
  MousePointer,
  Heart,
  MessageCircle,
  Share,
  Calendar,
  Filter,
  Download,
  RefreshCw
} from 'lucide-react';

interface PerformanceForecast {
  period: string;
  predictedMetrics: {
    engagement: number;
    reach: number;
    impressions: number;
    clicks: number;
    conversions: number;
    followerGrowth: number;
  };
  confidence: number;
  factors: {
    seasonality: number;
    trendImpact: number;
    contentQuality: number;
    postingFrequency: number;
    audienceGrowth: number;
  };
  recommendations: string[];
}

interface AnomalyDetection {
  anomalies: {
    type: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
    description: string;
    detectedAt: Date;
    affectedMetrics: string[];
    possibleCauses: string[];
    recommendedActions: string[];
    confidence: number;
  }[];
  overallHealthScore: number;
  trendAnalysis: {
    direction: 'improving' | 'declining' | 'stable';
    velocity: number;
    predictedOutcome: string;
  };
}

const AdvancedAnalyticsDashboard: React.FC = () => {
  const [forecast, setForecast] = useState<PerformanceForecast | null>(null);
  const [anomalies, setAnomalies] = useState<AnomalyDetection | null>(null);
  const [loading, setLoading] = useState(true);
  const [timeframe, setTimeframe] = useState<'7d' | '30d' | '90d'>('30d');
  const [activeTab, setActiveTab] = useState<'forecast' | 'anomalies' | 'insights' | 'optimization'>('forecast');

  useEffect(() => {
    fetchAnalyticsData();
  }, [timeframe]);

  const fetchAnalyticsData = async () => {
    setLoading(true);
    try {
      const [forecastRes, anomaliesRes] = await Promise.all([
        fetch(`/api/analytics/forecast?timeframe=${timeframe}`),
        fetch('/api/analytics/anomalies')
      ]);

      if (forecastRes.ok) {
        const forecastData = await forecastRes.json();
        setForecast(forecastData);
      }

      if (anomaliesRes.ok) {
        const anomaliesData = await anomaliesRes.json();
        setAnomalies(anomaliesData);
      }
    } catch (error) {
      console.error('Error fetching analytics data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'text-red-600 bg-red-100';
      case 'high': return 'text-orange-600 bg-orange-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'low': return 'text-blue-600 bg-blue-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getTrendIcon = (direction: string) => {
    switch (direction) {
      case 'improving': return <TrendingUp className="w-4 h-4 text-green-500" />;
      case 'declining': return <TrendingDown className="w-4 h-4 text-red-500" />;
      default: return <div className="w-4 h-4 bg-gray-400 rounded-full" />;
    }
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  const COLORS = ['#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#8dd1e1'];

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Brain className="w-8 h-8 text-purple-500" />
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Advanced Analytics</h1>
            <p className="text-gray-600">AI-powered insights and predictions</p>
          </div>
        </div>

        <div className="flex items-center space-x-4">
          {/* Timeframe Selector */}
          <select
            value={timeframe}
            onChange={(e) => setTimeframe(e.target.value as any)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500"
          >
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
          </select>

          <button
            onClick={fetchAnalyticsData}
            disabled={loading}
            className="flex items-center space-x-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors disabled:opacity-50"
          >
            <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            <span>Refresh</span>
          </button>
        </div>
      </div>

      {/* Health Score Card */}
      {anomalies && (
        <div className="bg-white rounded-xl shadow-sm border p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className={`p-2 rounded-lg ${anomalies.overallHealthScore >= 0.8 ? 'bg-green-100' : anomalies.overallHealthScore >= 0.6 ? 'bg-yellow-100' : 'bg-red-100'}`}>
                {anomalies.overallHealthScore >= 0.8 ? (
                  <CheckCircle className="w-6 h-6 text-green-600" />
                ) : (
                  <AlertTriangle className="w-6 h-6 text-yellow-600" />
                )}
              </div>
              <div>
                <h2 className="text-xl font-semibold text-gray-900">Overall Health Score</h2>
                <p className="text-gray-600">Current performance status</p>
              </div>
            </div>
            
            <div className="text-right">
              <div className="text-3xl font-bold text-gray-900">
                {Math.round(anomalies.overallHealthScore * 100)}%
              </div>
              <div className="flex items-center space-x-1 text-sm text-gray-600">
                {getTrendIcon(anomalies.trendAnalysis.direction)}
                <span className="capitalize">{anomalies.trendAnalysis.direction}</span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Tabs */}
      <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
        {[
          { id: 'forecast', label: 'Performance Forecast', icon: TrendingUp },
          { id: 'anomalies', label: 'Anomaly Detection', icon: AlertTriangle },
          { id: 'insights', label: 'AI Insights', icon: Brain },
          { id: 'optimization', label: 'Optimization', icon: Target }
        ].map(tab => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id as any)}
            className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-all ${
              activeTab === tab.id
                ? 'bg-white text-purple-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <tab.icon className="w-4 h-4" />
            <span>{tab.label}</span>
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <AnimatePresence mode="wait">
        {activeTab === 'forecast' && forecast && (
          <motion.div
            key="forecast"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            {/* Forecast Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-white rounded-xl shadow-sm border p-6">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <Heart className="w-6 h-6 text-blue-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Predicted Engagement</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {formatNumber(forecast.predictedMetrics.engagement)}
                    </p>
                    <p className="text-xs text-gray-500">
                      {Math.round(forecast.confidence * 100)}% confidence
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-xl shadow-sm border p-6">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <Eye className="w-6 h-6 text-green-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Predicted Reach</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {formatNumber(forecast.predictedMetrics.reach)}
                    </p>
                    <p className="text-xs text-gray-500">
                      {Math.round(forecast.confidence * 100)}% confidence
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-xl shadow-sm border p-6">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-purple-100 rounded-lg">
                    <Users className="w-6 h-6 text-purple-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Follower Growth</p>
                    <p className="text-2xl font-bold text-gray-900">
                      +{formatNumber(forecast.predictedMetrics.followerGrowth)}
                    </p>
                    <p className="text-xs text-gray-500">
                      {Math.round(forecast.confidence * 100)}% confidence
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Forecast Chart */}
            <div className="bg-white rounded-xl shadow-sm border p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Performance Forecast</h3>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={[
                  { name: 'Week 1', engagement: forecast.predictedMetrics.engagement * 0.7, reach: forecast.predictedMetrics.reach * 0.7 },
                  { name: 'Week 2', engagement: forecast.predictedMetrics.engagement * 0.85, reach: forecast.predictedMetrics.reach * 0.85 },
                  { name: 'Week 3', engagement: forecast.predictedMetrics.engagement * 0.95, reach: forecast.predictedMetrics.reach * 0.95 },
                  { name: 'Week 4', engagement: forecast.predictedMetrics.engagement, reach: forecast.predictedMetrics.reach }
                ]}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line type="monotone" dataKey="engagement" stroke="#8884d8" strokeWidth={2} />
                  <Line type="monotone" dataKey="reach" stroke="#82ca9d" strokeWidth={2} />
                </LineChart>
              </ResponsiveContainer>
            </div>

            {/* Factors Analysis */}
            <div className="bg-white rounded-xl shadow-sm border p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Performance Factors</h3>
              <ResponsiveContainer width="100%" height={300}>
                <RadarChart data={[
                  { subject: 'Seasonality', value: forecast.factors.seasonality * 100 },
                  { subject: 'Trend Impact', value: forecast.factors.trendImpact * 100 },
                  { subject: 'Content Quality', value: forecast.factors.contentQuality * 100 },
                  { subject: 'Posting Frequency', value: forecast.factors.postingFrequency * 100 },
                  { subject: 'Audience Growth', value: forecast.factors.audienceGrowth * 100 }
                ]}>
                  <PolarGrid />
                  <PolarAngleAxis dataKey="subject" />
                  <PolarRadiusAxis angle={90} domain={[0, 150]} />
                  <Radar name="Impact" dataKey="value" stroke="#8884d8" fill="#8884d8" fillOpacity={0.6} />
                </RadarChart>
              </ResponsiveContainer>
            </div>

            {/* Recommendations */}
            <div className="bg-white rounded-xl shadow-sm border p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">AI Recommendations</h3>
              <div className="space-y-3">
                {forecast.recommendations.map((recommendation, index) => (
                  <div key={index} className="flex items-start space-x-3 p-3 bg-blue-50 rounded-lg">
                    <Brain className="w-5 h-5 text-blue-600 mt-0.5" />
                    <p className="text-gray-700">{recommendation}</p>
                  </div>
                ))}
              </div>
            </div>
          </motion.div>
        )}

        {activeTab === 'anomalies' && anomalies && (
          <motion.div
            key="anomalies"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            {/* Anomalies List */}
            <div className="bg-white rounded-xl shadow-sm border p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Detected Anomalies</h3>
              
              {anomalies.anomalies.length === 0 ? (
                <div className="text-center py-8">
                  <CheckCircle className="w-12 h-12 text-green-500 mx-auto mb-4" />
                  <h4 className="text-lg font-medium text-gray-900 mb-2">No Anomalies Detected</h4>
                  <p className="text-gray-600">Your performance metrics are within normal ranges.</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {anomalies.anomalies.map((anomaly, index) => (
                    <div key={index} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-2">
                            <span className={`px-2 py-1 text-xs font-medium rounded ${getSeverityColor(anomaly.severity)}`}>
                              {anomaly.severity.toUpperCase()}
                            </span>
                            <span className="text-sm text-gray-500">
                              {new Date(anomaly.detectedAt).toLocaleDateString()}
                            </span>
                          </div>
                          
                          <h4 className="font-medium text-gray-900 mb-2">{anomaly.description}</h4>
                          
                          <div className="space-y-2">
                            <div>
                              <p className="text-sm font-medium text-gray-700">Affected Metrics:</p>
                              <div className="flex flex-wrap gap-1 mt-1">
                                {anomaly.affectedMetrics.map((metric, i) => (
                                  <span key={i} className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">
                                    {metric}
                                  </span>
                                ))}
                              </div>
                            </div>
                            
                            <div>
                              <p className="text-sm font-medium text-gray-700">Possible Causes:</p>
                              <ul className="list-disc list-inside text-sm text-gray-600 mt-1">
                                {anomaly.possibleCauses.map((cause, i) => (
                                  <li key={i}>{cause}</li>
                                ))}
                              </ul>
                            </div>
                            
                            <div>
                              <p className="text-sm font-medium text-gray-700">Recommended Actions:</p>
                              <ul className="list-disc list-inside text-sm text-gray-600 mt-1">
                                {anomaly.recommendedActions.map((action, i) => (
                                  <li key={i}>{action}</li>
                                ))}
                              </ul>
                            </div>
                          </div>
                        </div>
                        
                        <div className="text-right">
                          <p className="text-sm text-gray-500">Confidence</p>
                          <p className="text-lg font-semibold text-gray-900">
                            {Math.round(anomaly.confidence * 100)}%
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Trend Analysis */}
            <div className="bg-white rounded-xl shadow-sm border p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Trend Analysis</h3>
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  {getTrendIcon(anomalies.trendAnalysis.direction)}
                  <span className="font-medium capitalize">{anomalies.trendAnalysis.direction}</span>
                </div>
                <div className="text-sm text-gray-600">
                  Velocity: {(anomalies.trendAnalysis.velocity * 100).toFixed(1)}%
                </div>
              </div>
              <p className="text-gray-700 mt-2">{anomalies.trendAnalysis.predictedOutcome}</p>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {loading && (
        <div className="flex items-center justify-center py-12">
          <RefreshCw className="w-8 h-8 animate-spin text-purple-500" />
          <span className="ml-2 text-gray-600">Loading analytics data...</span>
        </div>
      )}
    </div>
  );
};

export default AdvancedAnalyticsDashboard;
