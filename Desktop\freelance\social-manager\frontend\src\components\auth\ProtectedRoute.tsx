import { useEffect } from 'react'
import { useSelector, useDispatch } from 'react-redux'
import { Navigate } from 'react-router-dom'
import { RootState, AppDispatch } from '../../store/store'
import { getCurrentUser } from '../../store/slices/authSlice'
import LoadingSpinner from '../ui/LoadingSpinner'

interface ProtectedRouteProps {
  children: React.ReactNode
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const dispatch = useDispatch<AppDispatch>()
  const { user, token, isLoading, isAuthenticated } = useSelector((state: RootState) => state.auth)

  useEffect(() => {
    // If we have a token but no user, try to get the current user
    if (token && !user && !isLoading) {
      dispatch(getCurrentUser())
    }
  }, [dispatch, token, user, isLoading])

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  // If no token or not authenticated, redirect to login
  if (!token || !isAuthenticated) {
    return <Navigate to="/login" replace />
  }

  // If authenticated, render children
  return <>{children}</>
}

export default ProtectedRoute
