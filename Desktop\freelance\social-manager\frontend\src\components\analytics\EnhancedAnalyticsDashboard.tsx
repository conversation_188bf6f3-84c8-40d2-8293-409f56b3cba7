import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Calendar } from '../ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '../ui/popover';
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  Eye,
  Heart,
  Share2,
  Calendar as CalendarIcon,
  Download,
  Zap,
  Target,
  Brain,
  Globe,
  Clock,
  AlertCircle
} from 'lucide-react';
import { format, subDays } from 'date-fns';
import { 
  useAnalyticsOverview,
  useAnalyticsReport,
  useRealtimeMetrics,
  usePredictiveInsights,
  useContentPerformance,
  usePlatformAnalytics,
  useExportReport,
  useAnalyticsUtils
} from '../../hooks/useAnalytics';
import { AnalyticsChart } from './AnalyticsChart';
import { ContentPerformanceTable } from './ContentPerformanceTable';
import { PlatformComparison } from './PlatformComparison';
import { RealtimeMetrics } from './RealtimeMetrics';
import { PredictiveInsights } from './PredictiveInsights';

export const EnhancedAnalyticsDashboard: React.FC = () => {
  const [dateRange, setDateRange] = useState({
    startDate: subDays(new Date(), 30),
    endDate: new Date()
  });
  const [selectedPlatforms, setSelectedPlatforms] = useState<string[]>([]);
  const [period, setPeriod] = useState('30d');
  const [activeTab, setActiveTab] = useState('overview');

  const { formatMetric, getPerformanceColor, getTrendIcon } = useAnalyticsUtils();
  const { exportReport, isExporting } = useExportReport();

  // Data hooks
  const { data: overview, isLoading: overviewLoading } = useAnalyticsOverview(period);
  const { data: realtimeData } = useRealtimeMetrics();
  const { data: reportData, isLoading: reportLoading } = useAnalyticsReport(
    dateRange.startDate.toISOString().split('T')[0],
    dateRange.endDate.toISOString().split('T')[0],
    selectedPlatforms.length > 0 ? selectedPlatforms : undefined
  );
  const { data: predictiveData } = usePredictiveInsights('month');
  const { data: contentPerformance } = useContentPerformance(
    dateRange.startDate.toISOString().split('T')[0],
    dateRange.endDate.toISOString().split('T')[0]
  );
  const { data: platformData } = usePlatformAnalytics(period, selectedPlatforms);

  const handleExport = async (format: 'pdf' | 'excel' | 'csv') => {
    try {
      await exportReport(
        dateRange.startDate.toISOString().split('T')[0],
        dateRange.endDate.toISOString().split('T')[0],
        format,
        selectedPlatforms.length > 0 ? selectedPlatforms : undefined
      );
    } catch (error) {
      console.error('Export failed:', error);
    }
  };

  const quickStats = [
    {
      title: 'Total Reach',
      value: overview?.totalReach || 0,
      change: 12.5,
      icon: Eye,
      color: 'blue'
    },
    {
      title: 'Engagement',
      value: overview?.totalEngagement || 0,
      change: 8.2,
      icon: Heart,
      color: 'red'
    },
    {
      title: 'Followers',
      value: overview?.platformBreakdown?.reduce((sum, p) => sum + p.followerCount, 0) || 0,
      change: overview?.followerGrowth || 0,
      icon: Users,
      color: 'green'
    },
    {
      title: 'Avg. Engagement Rate',
      value: overview?.averageEngagementRate || 0,
      change: 5.1,
      icon: TrendingUp,
      color: 'purple',
      isPercentage: true
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Analytics Dashboard</h1>
          <p className="text-gray-600 mt-1">
            Comprehensive insights into your social media performance
          </p>
        </div>
        <div className="flex space-x-3">
          <Select value={period} onValueChange={setPeriod}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
            </SelectContent>
          </Select>
          
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline">
                <CalendarIcon className="w-4 h-4 mr-2" />
                Custom Range
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="end">
              <div className="p-4 space-y-4">
                <div>
                  <label className="text-sm font-medium">Start Date</label>
                  <Calendar
                    mode="single"
                    selected={dateRange.startDate}
                    onSelect={(date) => date && setDateRange(prev => ({ ...prev, startDate: date }))}
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">End Date</label>
                  <Calendar
                    mode="single"
                    selected={dateRange.endDate}
                    onSelect={(date) => date && setDateRange(prev => ({ ...prev, endDate: date }))}
                  />
                </div>
              </div>
            </PopoverContent>
          </Popover>

          <Button variant="outline" onClick={() => handleExport('pdf')} disabled={isExporting}>
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Real-time Metrics Bar */}
      {realtimeData && (
        <Card className="border-l-4 border-l-green-500">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-6">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                  <span className="text-sm font-medium">Live</span>
                </div>
                <div className="text-sm">
                  <span className="text-gray-600">Active Users: </span>
                  <span className="font-semibold">{realtimeData.activeUsers}</span>
                </div>
                <div className="text-sm">
                  <span className="text-gray-600">Today's Posts: </span>
                  <span className="font-semibold">{realtimeData.todayPosts}</span>
                </div>
                <div className="text-sm">
                  <span className="text-gray-600">Today's Engagement: </span>
                  <span className="font-semibold">{formatMetric(realtimeData.todayEngagement)}</span>
                </div>
              </div>
              <Button variant="ghost" size="sm">
                <Zap className="w-4 h-4 mr-1" />
                View Live
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {quickStats.map((stat, index) => {
          const Icon = stat.icon;
          const changeColor = stat.change >= 0 ? 'text-green-600' : 'text-red-600';
          const changeIcon = stat.change >= 0 ? '↗' : '↘';

          return (
            <Card key={index}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {stat.isPercentage 
                        ? formatMetric(stat.value, 'percentage')
                        : formatMetric(stat.value)
                      }
                    </p>
                  </div>
                  <div className={`p-3 bg-${stat.color}-100 rounded-full`}>
                    <Icon className={`w-6 h-6 text-${stat.color}-600`} />
                  </div>
                </div>
                <div className={`flex items-center mt-2 text-sm ${changeColor}`}>
                  <span className="mr-1">{changeIcon}</span>
                  <span>{Math.abs(stat.change)}%</span>
                  <span className="text-gray-500 ml-1">vs last period</span>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="content">Content</TabsTrigger>
          <TabsTrigger value="platforms">Platforms</TabsTrigger>
          <TabsTrigger value="audience">Audience</TabsTrigger>
          <TabsTrigger value="predictions">Predictions</TabsTrigger>
          <TabsTrigger value="realtime">Real-time</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Engagement Trend Chart */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <BarChart3 className="w-5 h-5 mr-2" />
                  Engagement Trend
                </CardTitle>
              </CardHeader>
              <CardContent>
                {reportData?.trends.engagementTrend && (
                  <AnalyticsChart 
                    data={reportData.trends.engagementTrend}
                    type="line"
                    height={300}
                  />
                )}
              </CardContent>
            </Card>

            {/* Reach Trend Chart */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Eye className="w-5 h-5 mr-2" />
                  Reach Trend
                </CardTitle>
              </CardHeader>
              <CardContent>
                {reportData?.trends.reachTrend && (
                  <AnalyticsChart 
                    data={reportData.trends.reachTrend}
                    type="area"
                    height={300}
                  />
                )}
              </CardContent>
            </Card>
          </div>

          {/* Top Performing Content */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Target className="w-5 h-5 mr-2" />
                Top Performing Content
              </CardTitle>
            </CardHeader>
            <CardContent>
              {contentPerformance && (
                <ContentPerformanceTable 
                  data={contentPerformance.content.slice(0, 10)}
                  showActions={false}
                />
              )}
            </CardContent>
          </Card>

          {/* Recommendations */}
          {reportData?.recommendations && reportData.recommendations.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Brain className="w-5 h-5 mr-2" />
                  AI Recommendations
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {reportData.recommendations.slice(0, 3).map((rec, index) => (
                    <div key={index} className="border rounded-lg p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-2">
                            <Badge variant={rec.priority === 'high' ? 'destructive' : 
                                          rec.priority === 'medium' ? 'default' : 'secondary'}>
                              {rec.priority} priority
                            </Badge>
                            <Badge variant="outline">{rec.type}</Badge>
                          </div>
                          <h4 className="font-medium text-gray-900 mb-1">{rec.title}</h4>
                          <p className="text-sm text-gray-600 mb-3">{rec.description}</p>
                          <div className="space-y-1">
                            {rec.actionItems.slice(0, 2).map((item, i) => (
                              <p key={i} className="text-xs text-gray-500">• {item}</p>
                            ))}
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="text-xs text-green-600 font-medium">{rec.expectedImpact}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="content">
          <ContentPerformanceTable 
            data={contentPerformance?.content || []}
            showActions={true}
          />
        </TabsContent>

        <TabsContent value="platforms">
          <PlatformComparison data={platformData?.platformBreakdown || []} />
        </TabsContent>

        <TabsContent value="audience" className="space-y-6">
          {reportData?.audienceInsights && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Demographics */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Users className="w-5 h-5 mr-2" />
                    Audience Demographics
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {/* Age Groups */}
                    <div>
                      <h4 className="font-medium mb-3">Age Groups</h4>
                      <div className="space-y-2">
                        {reportData.audienceInsights.demographics.ageGroups.map((group, index) => (
                          <div key={index} className="flex items-center justify-between">
                            <span className="text-sm">{group.range}</span>
                            <div className="flex items-center space-x-2">
                              <div className="w-24 bg-gray-200 rounded-full h-2">
                                <div 
                                  className="bg-blue-500 h-2 rounded-full"
                                  style={{ width: `${group.percentage}%` }}
                                />
                              </div>
                              <span className="text-sm text-gray-600">{group.percentage}%</span>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Genders */}
                    <div>
                      <h4 className="font-medium mb-3">Gender Distribution</h4>
                      <div className="space-y-2">
                        {reportData.audienceInsights.demographics.genders.map((gender, index) => (
                          <div key={index} className="flex items-center justify-between">
                            <span className="text-sm">{gender.gender}</span>
                            <div className="flex items-center space-x-2">
                              <div className="w-24 bg-gray-200 rounded-full h-2">
                                <div 
                                  className="bg-purple-500 h-2 rounded-full"
                                  style={{ width: `${gender.percentage}%` }}
                                />
                              </div>
                              <span className="text-sm text-gray-600">{gender.percentage}%</span>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Top Locations */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Globe className="w-5 h-5 mr-2" />
                    Top Locations
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {reportData.audienceInsights.demographics.locations.map((location, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <span className="text-sm font-medium">{location.country}</span>
                        <div className="flex items-center space-x-2">
                          <div className="w-32 bg-gray-200 rounded-full h-2">
                            <div 
                              className="bg-green-500 h-2 rounded-full"
                              style={{ width: `${location.percentage}%` }}
                            />
                          </div>
                          <span className="text-sm text-gray-600">{location.percentage}%</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Active Hours */}
              <Card className="lg:col-span-2">
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Clock className="w-5 h-5 mr-2" />
                    Audience Activity Hours
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <AnalyticsChart 
                    data={reportData.audienceInsights.activeHours.map(h => ({
                      date: `${h.hour}:00`,
                      value: h.activity
                    }))}
                    type="bar"
                    height={200}
                  />
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>

        <TabsContent value="predictions">
          {predictiveData && <PredictiveInsights data={predictiveData} />}
        </TabsContent>

        <TabsContent value="realtime">
          {realtimeData && <RealtimeMetrics data={realtimeData} />}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default EnhancedAnalyticsDashboard;
