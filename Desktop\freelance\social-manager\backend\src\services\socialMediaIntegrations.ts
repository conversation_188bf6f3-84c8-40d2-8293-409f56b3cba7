import { PrismaClient } from '@prisma/client';
import axios from 'axios';
import { logger } from '../utils/logger.js';
import { CacheService } from '../config/redis.js';

export interface SocialMediaAccount {
  id: string;
  platform: 'INSTAGRAM' | 'FACEBOOK' | 'TWITTER' | 'LINKEDIN' | 'TIKTOK' | 'YOUTUBE' | 'PINTEREST';
  accountId: string;
  accountName: string;
  accessToken: string;
  refreshToken?: string;
  expiresAt?: Date;
  isActive: boolean;
  organizationId: string;
  settings: Record<string, any>;
}

export interface ContentPost {
  id: string;
  content: string;
  mediaUrls?: string[];
  platforms: string[];
  scheduledAt?: Date;
  publishedAt?: Date;
  status: 'DRAFT' | 'SCHEDULED' | 'PUBLISHED' | 'FAILED';
  platformPosts: PlatformPost[];
}

export interface PlatformPost {
  platform: string;
  platformPostId?: string;
  status: 'PENDING' | 'PUBLISHED' | 'FAILED';
  error?: string;
  publishedAt?: Date;
  metrics?: PostMetrics;
}

export interface PostMetrics {
  likes: number;
  shares: number;
  comments: number;
  views: number;
  clicks: number;
  reach: number;
  impressions: number;
  engagementRate: number;
}

export interface PublishRequest {
  content: string;
  mediaUrls?: string[];
  platforms: string[];
  scheduledAt?: Date;
  organizationId: string;
  userId: string;
}

export class SocialMediaIntegrationService {
  private prisma: PrismaClient;
  private platformClients: Map<string, any>;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
    this.platformClients = new Map();
    this.initializePlatformClients();
  }

  /**
   * Initialize platform-specific API clients
   */
  private initializePlatformClients(): void {
    // Facebook/Instagram Graph API client
    this.platformClients.set('FACEBOOK', {
      baseURL: 'https://graph.facebook.com/v18.0',
      apiVersion: 'v18.0'
    });

    this.platformClients.set('INSTAGRAM', {
      baseURL: 'https://graph.facebook.com/v18.0',
      apiVersion: 'v18.0'
    });

    // Twitter API v2 client
    this.platformClients.set('TWITTER', {
      baseURL: 'https://api.twitter.com/2',
      apiVersion: '2'
    });

    // LinkedIn API client
    this.platformClients.set('LINKEDIN', {
      baseURL: 'https://api.linkedin.com/v2',
      apiVersion: 'v2'
    });

    // TikTok for Business API client
    this.platformClients.set('TIKTOK', {
      baseURL: 'https://business-api.tiktok.com/open_api/v1.3',
      apiVersion: 'v1.3'
    });

    // YouTube Data API client
    this.platformClients.set('YOUTUBE', {
      baseURL: 'https://www.googleapis.com/youtube/v3',
      apiVersion: 'v3'
    });

    // Pinterest API client
    this.platformClients.set('PINTEREST', {
      baseURL: 'https://api.pinterest.com/v5',
      apiVersion: 'v5'
    });
  }

  /**
   * Connect a social media account
   */
  async connectAccount(
    platform: string,
    authCode: string,
    organizationId: string,
    userId: string
  ): Promise<SocialMediaAccount> {
    try {
      // Exchange auth code for access token
      const tokenData = await this.exchangeAuthCode(platform, authCode);
      
      // Get account information
      const accountInfo = await this.getAccountInfo(platform, tokenData.accessToken);

      // Save to database
      const account = await this.prisma.socialMediaIntegration.create({
        data: {
          platform,
          accountId: accountInfo.id,
          accountName: accountInfo.name,
          accessToken: tokenData.accessToken,
          refreshToken: tokenData.refreshToken,
          expiresAt: tokenData.expiresAt,
          isActive: true,
          organizationId,
          connectedBy: userId,
          settings: JSON.stringify(accountInfo.settings || {})
        }
      });

      logger.info(`Connected ${platform} account: ${accountInfo.name} for organization: ${organizationId}`);

      return {
        id: account.id,
        platform: account.platform as any,
        accountId: account.accountId,
        accountName: account.accountName,
        accessToken: account.accessToken,
        refreshToken: account.refreshToken || undefined,
        expiresAt: account.expiresAt || undefined,
        isActive: account.isActive,
        organizationId: account.organizationId,
        settings: JSON.parse(account.settings || '{}')
      };
    } catch (error) {
      logger.error(`Error connecting ${platform} account:`, error);
      throw new Error(`Failed to connect ${platform} account`);
    }
  }

  /**
   * Publish content to multiple platforms
   */
  async publishContent(request: PublishRequest): Promise<ContentPost> {
    try {
      // Create content record
      const content = await this.prisma.contentItem.create({
        data: {
          title: request.content.substring(0, 100),
          description: request.content,
          type: 'POST',
          platforms: JSON.stringify(request.platforms),
          contentData: JSON.stringify({
            text: request.content,
            mediaUrls: request.mediaUrls || []
          }),
          status: request.scheduledAt ? 'SCHEDULED' : 'DRAFT',
          scheduledAt: request.scheduledAt,
          projectId: await this.getDefaultProjectId(request.organizationId),
          createdById: request.userId
        }
      });

      const platformPosts: PlatformPost[] = [];

      // If scheduled for future, add to queue
      if (request.scheduledAt && request.scheduledAt > new Date()) {
        await this.scheduleContent(content.id, request.scheduledAt);
        
        // Create pending platform posts
        for (const platform of request.platforms) {
          platformPosts.push({
            platform,
            status: 'PENDING'
          });
        }
      } else {
        // Publish immediately to all platforms
        for (const platform of request.platforms) {
          try {
            const platformPost = await this.publishToPlatform(
              platform,
              request.content,
              request.mediaUrls || [],
              request.organizationId
            );
            platformPosts.push(platformPost);
          } catch (error) {
            logger.error(`Failed to publish to ${platform}:`, error);
            platformPosts.push({
              platform,
              status: 'FAILED',
              error: error instanceof Error ? error.message : 'Unknown error'
            });
          }
        }

        // Update content status
        await this.prisma.contentItem.update({
          where: { id: content.id },
          data: {
            status: platformPosts.some(p => p.status === 'PUBLISHED') ? 'PUBLISHED' : 'FAILED',
            publishedAt: new Date()
          }
        });
      }

      return {
        id: content.id,
        content: request.content,
        mediaUrls: request.mediaUrls,
        platforms: request.platforms,
        scheduledAt: request.scheduledAt,
        publishedAt: request.scheduledAt ? undefined : new Date(),
        status: request.scheduledAt ? 'SCHEDULED' : 
                platformPosts.some(p => p.status === 'PUBLISHED') ? 'PUBLISHED' : 'FAILED',
        platformPosts
      };
    } catch (error) {
      logger.error('Error publishing content:', error);
      throw new Error('Failed to publish content');
    }
  }

  /**
   * Get analytics for published content
   */
  async getContentAnalytics(contentId: string): Promise<PostMetrics> {
    try {
      const content = await this.prisma.contentItem.findUnique({
        where: { id: contentId }
      });

      if (!content) {
        throw new Error('Content not found');
      }

      const platforms = JSON.parse(content.platforms);
      const aggregatedMetrics: PostMetrics = {
        likes: 0,
        shares: 0,
        comments: 0,
        views: 0,
        clicks: 0,
        reach: 0,
        impressions: 0,
        engagementRate: 0
      };

      // Fetch metrics from each platform
      for (const platform of platforms) {
        try {
          const platformMetrics = await this.getPlatformMetrics(platform, contentId);
          
          // Aggregate metrics
          aggregatedMetrics.likes += platformMetrics.likes;
          aggregatedMetrics.shares += platformMetrics.shares;
          aggregatedMetrics.comments += platformMetrics.comments;
          aggregatedMetrics.views += platformMetrics.views;
          aggregatedMetrics.clicks += platformMetrics.clicks;
          aggregatedMetrics.reach += platformMetrics.reach;
          aggregatedMetrics.impressions += platformMetrics.impressions;
        } catch (error) {
          logger.error(`Failed to get metrics from ${platform}:`, error);
        }
      }

      // Calculate engagement rate
      if (aggregatedMetrics.impressions > 0) {
        aggregatedMetrics.engagementRate = 
          (aggregatedMetrics.likes + aggregatedMetrics.shares + aggregatedMetrics.comments) / 
          aggregatedMetrics.impressions;
      }

      return aggregatedMetrics;
    } catch (error) {
      logger.error('Error getting content analytics:', error);
      throw new Error('Failed to get content analytics');
    }
  }

  /**
   * Exchange authorization code for access token
   */
  private async exchangeAuthCode(platform: string, authCode: string): Promise<{
    accessToken: string;
    refreshToken?: string;
    expiresAt?: Date;
  }> {
    switch (platform) {
      case 'FACEBOOK':
      case 'INSTAGRAM':
        return this.exchangeFacebookAuthCode(authCode);
      case 'TWITTER':
        return this.exchangeTwitterAuthCode(authCode);
      case 'LINKEDIN':
        return this.exchangeLinkedInAuthCode(authCode);
      case 'TIKTOK':
        return this.exchangeTikTokAuthCode(authCode);
      case 'YOUTUBE':
        return this.exchangeYouTubeAuthCode(authCode);
      case 'PINTEREST':
        return this.exchangePinterestAuthCode(authCode);
      default:
        throw new Error(`Unsupported platform: ${platform}`);
    }
  }

  /**
   * Facebook/Instagram OAuth token exchange
   */
  private async exchangeFacebookAuthCode(authCode: string): Promise<{
    accessToken: string;
    refreshToken?: string;
    expiresAt?: Date;
  }> {
    try {
      const response = await axios.post('https://graph.facebook.com/v18.0/oauth/access_token', {
        client_id: process.env.FACEBOOK_APP_ID,
        client_secret: process.env.FACEBOOK_APP_SECRET,
        redirect_uri: process.env.FACEBOOK_REDIRECT_URI,
        code: authCode
      });

      const { access_token, expires_in } = response.data;
      
      // Exchange for long-lived token
      const longLivedResponse = await axios.get('https://graph.facebook.com/v18.0/oauth/access_token', {
        params: {
          grant_type: 'fb_exchange_token',
          client_id: process.env.FACEBOOK_APP_ID,
          client_secret: process.env.FACEBOOK_APP_SECRET,
          fb_exchange_token: access_token
        }
      });

      const { access_token: longLivedToken, expires_in: longLivedExpires } = longLivedResponse.data;

      return {
        accessToken: longLivedToken,
        expiresAt: new Date(Date.now() + (longLivedExpires * 1000))
      };
    } catch (error) {
      logger.error('Error exchanging Facebook auth code:', error);
      throw new Error('Failed to exchange Facebook authorization code');
    }
  }

  /**
   * Twitter OAuth token exchange
   */
  private async exchangeTwitterAuthCode(authCode: string): Promise<{
    accessToken: string;
    refreshToken?: string;
    expiresAt?: Date;
  }> {
    try {
      const response = await axios.post('https://api.twitter.com/2/oauth2/token', {
        grant_type: 'authorization_code',
        client_id: process.env.TWITTER_CLIENT_ID,
        client_secret: process.env.TWITTER_CLIENT_SECRET,
        redirect_uri: process.env.TWITTER_REDIRECT_URI,
        code: authCode,
        code_verifier: 'challenge' // In production, use proper PKCE
      }, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      });

      const { access_token, refresh_token, expires_in } = response.data;

      return {
        accessToken: access_token,
        refreshToken: refresh_token,
        expiresAt: new Date(Date.now() + (expires_in * 1000))
      };
    } catch (error) {
      logger.error('Error exchanging Twitter auth code:', error);
      throw new Error('Failed to exchange Twitter authorization code');
    }
  }

  /**
   * LinkedIn OAuth token exchange
   */
  private async exchangeLinkedInAuthCode(authCode: string): Promise<{
    accessToken: string;
    refreshToken?: string;
    expiresAt?: Date;
  }> {
    try {
      const response = await axios.post('https://www.linkedin.com/oauth/v2/accessToken', {
        grant_type: 'authorization_code',
        client_id: process.env.LINKEDIN_CLIENT_ID,
        client_secret: process.env.LINKEDIN_CLIENT_SECRET,
        redirect_uri: process.env.LINKEDIN_REDIRECT_URI,
        code: authCode
      }, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      });

      const { access_token, expires_in } = response.data;

      return {
        accessToken: access_token,
        expiresAt: new Date(Date.now() + (expires_in * 1000))
      };
    } catch (error) {
      logger.error('Error exchanging LinkedIn auth code:', error);
      throw new Error('Failed to exchange LinkedIn authorization code');
    }
  }

  /**
   * TikTok OAuth token exchange
   */
  private async exchangeTikTokAuthCode(authCode: string): Promise<{
    accessToken: string;
    refreshToken?: string;
    expiresAt?: Date;
  }> {
    try {
      const response = await axios.post('https://business-api.tiktok.com/open_api/v1.3/oauth2/access_token/', {
        app_id: process.env.TIKTOK_APP_ID,
        secret: process.env.TIKTOK_APP_SECRET,
        auth_code: authCode
      });

      const { access_token, expires_in } = response.data.data;

      return {
        accessToken: access_token,
        expiresAt: new Date(Date.now() + (expires_in * 1000))
      };
    } catch (error) {
      logger.error('Error exchanging TikTok auth code:', error);
      throw new Error('Failed to exchange TikTok authorization code');
    }
  }

  /**
   * YouTube OAuth token exchange
   */
  private async exchangeYouTubeAuthCode(authCode: string): Promise<{
    accessToken: string;
    refreshToken?: string;
    expiresAt?: Date;
  }> {
    try {
      const response = await axios.post('https://oauth2.googleapis.com/token', {
        grant_type: 'authorization_code',
        client_id: process.env.GOOGLE_CLIENT_ID,
        client_secret: process.env.GOOGLE_CLIENT_SECRET,
        redirect_uri: process.env.GOOGLE_REDIRECT_URI,
        code: authCode
      });

      const { access_token, refresh_token, expires_in } = response.data;

      return {
        accessToken: access_token,
        refreshToken: refresh_token,
        expiresAt: new Date(Date.now() + (expires_in * 1000))
      };
    } catch (error) {
      logger.error('Error exchanging YouTube auth code:', error);
      throw new Error('Failed to exchange YouTube authorization code');
    }
  }

  /**
   * Pinterest OAuth token exchange
   */
  private async exchangePinterestAuthCode(authCode: string): Promise<{
    accessToken: string;
    refreshToken?: string;
    expiresAt?: Date;
  }> {
    try {
      const response = await axios.post('https://api.pinterest.com/v5/oauth/token', {
        grant_type: 'authorization_code',
        client_id: process.env.PINTEREST_CLIENT_ID,
        client_secret: process.env.PINTEREST_CLIENT_SECRET,
        redirect_uri: process.env.PINTEREST_REDIRECT_URI,
        code: authCode
      });

      const { access_token, refresh_token, expires_in } = response.data;

      return {
        accessToken: access_token,
        refreshToken: refresh_token,
        expiresAt: new Date(Date.now() + (expires_in * 1000))
      };
    } catch (error) {
      logger.error('Error exchanging Pinterest auth code:', error);
      throw new Error('Failed to exchange Pinterest authorization code');
    }
  }

  /**
   * Get account information from platform
   */
  private async getAccountInfo(platform: string, accessToken: string): Promise<{
    id: string;
    name: string;
    settings?: any;
  }> {
    switch (platform) {
      case 'FACEBOOK':
        return this.getFacebookAccountInfo(accessToken);
      case 'INSTAGRAM':
        return this.getInstagramAccountInfo(accessToken);
      case 'TWITTER':
        return this.getTwitterAccountInfo(accessToken);
      case 'LINKEDIN':
        return this.getLinkedInAccountInfo(accessToken);
      case 'TIKTOK':
        return this.getTikTokAccountInfo(accessToken);
      case 'YOUTUBE':
        return this.getYouTubeAccountInfo(accessToken);
      case 'PINTEREST':
        return this.getPinterestAccountInfo(accessToken);
      default:
        throw new Error(`Unsupported platform: ${platform}`);
    }
  }

  /**
   * Publish content to specific platform
   */
  private async publishToPlatform(
    platform: string,
    content: string,
    mediaUrls: string[],
    organizationId: string
  ): Promise<PlatformPost> {
    try {
      // Get platform account for organization
      const account = await this.prisma.socialMediaIntegration.findFirst({
        where: {
          platform,
          organizationId,
          isActive: true
        }
      });

      if (!account) {
        throw new Error(`No active ${platform} account found`);
      }

      let platformPostId: string;

      switch (platform) {
        case 'FACEBOOK':
          platformPostId = await this.publishToFacebook(content, mediaUrls, account.accessToken);
          break;
        case 'INSTAGRAM':
          platformPostId = await this.publishToInstagram(content, mediaUrls, account.accessToken);
          break;
        case 'TWITTER':
          platformPostId = await this.publishToTwitter(content, mediaUrls, account.accessToken);
          break;
        case 'LINKEDIN':
          platformPostId = await this.publishToLinkedIn(content, mediaUrls, account.accessToken);
          break;
        case 'TIKTOK':
          platformPostId = await this.publishToTikTok(content, mediaUrls, account.accessToken);
          break;
        case 'YOUTUBE':
          platformPostId = await this.publishToYouTube(content, mediaUrls, account.accessToken);
          break;
        case 'PINTEREST':
          platformPostId = await this.publishToPinterest(content, mediaUrls, account.accessToken);
          break;
        default:
          throw new Error(`Unsupported platform: ${platform}`);
      }

      return {
        platform,
        platformPostId,
        status: 'PUBLISHED',
        publishedAt: new Date()
      };
    } catch (error) {
      logger.error(`Error publishing to ${platform}:`, error);
      return {
        platform,
        status: 'FAILED',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Facebook publishing implementation
   */
  private async publishToFacebook(content: string, mediaUrls: string[], accessToken: string): Promise<string> {
    try {
      const postData: any = {
        message: content,
        access_token: accessToken
      };

      // Add media if provided
      if (mediaUrls.length > 0) {
        // For multiple media, create a multi-photo post
        if (mediaUrls.length > 1) {
          const attachedMedia = [];
          for (const mediaUrl of mediaUrls) {
            // Upload media first
            const uploadResponse = await axios.post(`https://graph.facebook.com/v18.0/me/photos`, {
              url: mediaUrl,
              published: false,
              access_token: accessToken
            });
            attachedMedia.push({ media_fbid: uploadResponse.data.id });
          }
          postData.attached_media = attachedMedia;
        } else {
          // Single media post
          postData.link = mediaUrls[0];
        }
      }

      const response = await axios.post('https://graph.facebook.com/v18.0/me/feed', postData);
      return response.data.id;
    } catch (error) {
      logger.error('Error publishing to Facebook:', error);
      throw new Error('Failed to publish to Facebook');
    }
  }

  /**
   * Instagram publishing implementation
   */
  private async publishToInstagram(content: string, mediaUrls: string[], accessToken: string): Promise<string> {
    try {
      // Get Instagram Business Account ID
      const accountResponse = await axios.get(`https://graph.facebook.com/v18.0/me/accounts`, {
        params: {
          access_token: accessToken,
          fields: 'instagram_business_account'
        }
      });

      const instagramAccountId = accountResponse.data.data[0]?.instagram_business_account?.id;
      if (!instagramAccountId) {
        throw new Error('No Instagram Business Account found');
      }

      let mediaId: string;

      if (mediaUrls.length > 0) {
        // Create media container
        const containerResponse = await axios.post(
          `https://graph.facebook.com/v18.0/${instagramAccountId}/media`,
          {
            image_url: mediaUrls[0], // Instagram supports single image per post
            caption: content,
            access_token: accessToken
          }
        );
        mediaId = containerResponse.data.id;
      } else {
        throw new Error('Instagram posts require media');
      }

      // Publish the media
      const publishResponse = await axios.post(
        `https://graph.facebook.com/v18.0/${instagramAccountId}/media_publish`,
        {
          creation_id: mediaId,
          access_token: accessToken
        }
      );

      return publishResponse.data.id;
    } catch (error) {
      logger.error('Error publishing to Instagram:', error);
      throw new Error('Failed to publish to Instagram');
    }
  }

  /**
   * Twitter publishing implementation
   */
  private async publishToTwitter(content: string, mediaUrls: string[], accessToken: string): Promise<string> {
    try {
      const tweetData: any = {
        text: content
      };

      // Add media if provided
      if (mediaUrls.length > 0) {
        const mediaIds = [];
        for (const mediaUrl of mediaUrls) {
          // Upload media first
          const mediaResponse = await axios.post('https://upload.twitter.com/1.1/media/upload.json', {
            media_data: await this.getBase64FromUrl(mediaUrl)
          }, {
            headers: {
              'Authorization': `Bearer ${accessToken}`,
              'Content-Type': 'application/x-www-form-urlencoded'
            }
          });
          mediaIds.push(mediaResponse.data.media_id_string);
        }
        tweetData.media = { media_ids: mediaIds };
      }

      const response = await axios.post('https://api.twitter.com/2/tweets', tweetData, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        }
      });

      return response.data.data.id;
    } catch (error) {
      logger.error('Error publishing to Twitter:', error);
      throw new Error('Failed to publish to Twitter');
    }
  }

  /**
   * LinkedIn publishing implementation
   */
  private async publishToLinkedIn(content: string, mediaUrls: string[], accessToken: string): Promise<string> {
    try {
      // Get user profile
      const profileResponse = await axios.get('https://api.linkedin.com/v2/people/~', {
        headers: {
          'Authorization': `Bearer ${accessToken}`
        }
      });

      const userId = profileResponse.data.id;

      const postData: any = {
        author: `urn:li:person:${userId}`,
        lifecycleState: 'PUBLISHED',
        specificContent: {
          'com.linkedin.ugc.ShareContent': {
            shareCommentary: {
              text: content
            },
            shareMediaCategory: mediaUrls.length > 0 ? 'IMAGE' : 'NONE'
          }
        },
        visibility: {
          'com.linkedin.ugc.MemberNetworkVisibility': 'PUBLIC'
        }
      };

      // Add media if provided
      if (mediaUrls.length > 0) {
        const media = [];
        for (const mediaUrl of mediaUrls) {
          // Register upload
          const uploadResponse = await axios.post('https://api.linkedin.com/v2/assets?action=registerUpload', {
            registerUploadRequest: {
              recipes: ['urn:li:digitalmediaRecipe:feedshare-image'],
              owner: `urn:li:person:${userId}`,
              serviceRelationships: [{
                relationshipType: 'OWNER',
                identifier: 'urn:li:userGeneratedContent'
              }]
            }
          }, {
            headers: {
              'Authorization': `Bearer ${accessToken}`,
              'Content-Type': 'application/json'
            }
          });

          const uploadUrl = uploadResponse.data.value.uploadMechanism['com.linkedin.digitalmedia.uploading.MediaUploadHttpRequest'].uploadUrl;
          const asset = uploadResponse.data.value.asset;

          // Upload media
          const mediaBuffer = await this.getBufferFromUrl(mediaUrl);
          await axios.put(uploadUrl, mediaBuffer, {
            headers: {
              'Authorization': `Bearer ${accessToken}`
            }
          });

          media.push({
            status: 'READY',
            description: {
              text: 'Image'
            },
            media: asset,
            title: {
              text: 'Image'
            }
          });
        }

        postData.specificContent['com.linkedin.ugc.ShareContent'].media = media;
      }

      const response = await axios.post('https://api.linkedin.com/v2/ugcPosts', postData, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        }
      });

      return response.data.id;
    } catch (error) {
      logger.error('Error publishing to LinkedIn:', error);
      throw new Error('Failed to publish to LinkedIn');
    }
  }

  /**
   * Helper methods for account info retrieval
   */
  private async getFacebookAccountInfo(accessToken: string): Promise<{ id: string; name: string; settings?: any }> {
    const response = await axios.get('https://graph.facebook.com/v18.0/me', {
      params: {
        fields: 'id,name,email',
        access_token: accessToken
      }
    });
    return { id: response.data.id, name: response.data.name };
  }

  private async getInstagramAccountInfo(accessToken: string): Promise<{ id: string; name: string; settings?: any }> {
    const response = await axios.get('https://graph.facebook.com/v18.0/me/accounts', {
      params: {
        fields: 'instagram_business_account{id,name,username}',
        access_token: accessToken
      }
    });
    const igAccount = response.data.data[0]?.instagram_business_account;
    return { id: igAccount.id, name: igAccount.username };
  }

  private async getTwitterAccountInfo(accessToken: string): Promise<{ id: string; name: string; settings?: any }> {
    const response = await axios.get('https://api.twitter.com/2/users/me', {
      headers: { 'Authorization': `Bearer ${accessToken}` }
    });
    return { id: response.data.data.id, name: response.data.data.username };
  }

  private async getLinkedInAccountInfo(accessToken: string): Promise<{ id: string; name: string; settings?: any }> {
    const response = await axios.get('https://api.linkedin.com/v2/people/~', {
      params: { projection: '(id,firstName,lastName)' },
      headers: { 'Authorization': `Bearer ${accessToken}` }
    });
    const name = `${response.data.firstName.localized.en_US} ${response.data.lastName.localized.en_US}`;
    return { id: response.data.id, name };
  }

  private async getTikTokAccountInfo(accessToken: string): Promise<{ id: string; name: string; settings?: any }> {
    const response = await axios.get('https://business-api.tiktok.com/open_api/v1.3/user/info/', {
      headers: { 'Access-Token': accessToken }
    });
    return { id: response.data.data.user_id, name: response.data.data.display_name };
  }

  private async getYouTubeAccountInfo(accessToken: string): Promise<{ id: string; name: string; settings?: any }> {
    const response = await axios.get('https://www.googleapis.com/youtube/v3/channels', {
      params: { part: 'snippet', mine: true },
      headers: { 'Authorization': `Bearer ${accessToken}` }
    });
    const channel = response.data.items[0];
    return { id: channel.id, name: channel.snippet.title };
  }

  private async getPinterestAccountInfo(accessToken: string): Promise<{ id: string; name: string; settings?: any }> {
    const response = await axios.get('https://api.pinterest.com/v5/user_account', {
      headers: { 'Authorization': `Bearer ${accessToken}` }
    });
    return { id: response.data.id, name: response.data.username };
  }

  /**
   * Utility methods
   */
  private async getBase64FromUrl(url: string): Promise<string> {
    const response = await axios.get(url, { responseType: 'arraybuffer' });
    return Buffer.from(response.data, 'binary').toString('base64');
  }

  private async getBufferFromUrl(url: string): Promise<Buffer> {
    const response = await axios.get(url, { responseType: 'arraybuffer' });
    return Buffer.from(response.data);
  }

  private async getDefaultProjectId(organizationId: string): Promise<string> {
    const project = await this.prisma.project.findFirst({
      where: { organizationId },
      orderBy: { createdAt: 'desc' }
    });

    if (!project) {
      // Create default project
      const defaultProject = await this.prisma.project.create({
        data: {
          name: 'Default Project',
          description: 'Default project for social media posts',
          organizationId,
          status: 'ACTIVE'
        }
      });
      return defaultProject.id;
    }

    return project.id;
  }

  private async scheduleContent(contentId: string, scheduledAt: Date): Promise<void> {
    // Implementation for scheduling content using Bull Queue
    // This would integrate with the job queue system
    logger.info(`Scheduling content ${contentId} for ${scheduledAt}`);
  }

  private async getPlatformMetrics(platform: string, contentId: string): Promise<PostMetrics> {
    // Implementation for fetching platform-specific metrics
    // This would call each platform's analytics API
    return {
      likes: 0,
      shares: 0,
      comments: 0,
      views: 0,
      clicks: 0,
      reach: 0,
      impressions: 0,
      engagementRate: 0
    };
  }

  /**
   * Remaining platform publishing methods (simplified implementations)
   */
  private async publishToTikTok(content: string, mediaUrls: string[], accessToken: string): Promise<string> {
    // TikTok publishing implementation
    throw new Error('TikTok publishing not yet implemented');
  }

  private async publishToYouTube(content: string, mediaUrls: string[], accessToken: string): Promise<string> {
    // YouTube publishing implementation
    throw new Error('YouTube publishing not yet implemented');
  }

  private async publishToPinterest(content: string, mediaUrls: string[], accessToken: string): Promise<string> {
    // Pinterest publishing implementation
    throw new Error('Pinterest publishing not yet implemented');
  }

  /**
   * Refresh access token for platform
   */
  async refreshAccessToken(accountId: string): Promise<void> {
    const account = await this.prisma.socialMediaIntegration.findUnique({
      where: { id: accountId }
    });

    if (!account || !account.refreshToken) {
      throw new Error('Account not found or no refresh token available');
    }

    // Platform-specific token refresh logic
    // Implementation depends on each platform's OAuth flow
    logger.info(`Refreshing access token for account: ${accountId}`);
  }

  /**
   * Disconnect social media account
   */
  async disconnectAccount(accountId: string): Promise<void> {
    await this.prisma.socialMediaIntegration.update({
      where: { id: accountId },
      data: { isActive: false }
    });

    logger.info(`Disconnected social media account: ${accountId}`);
  }

  /**
   * Get all connected accounts for organization
   */
  async getConnectedAccounts(organizationId: string): Promise<SocialMediaAccount[]> {
    const accounts = await this.prisma.socialMediaIntegration.findMany({
      where: {
        organizationId,
        isActive: true
      }
    });

    return accounts.map(account => ({
      id: account.id,
      platform: account.platform as any,
      accountId: account.accountId,
      accountName: account.accountName,
      accessToken: account.accessToken,
      refreshToken: account.refreshToken || undefined,
      expiresAt: account.expiresAt || undefined,
      isActive: account.isActive,
      organizationId: account.organizationId,
      settings: JSON.parse(account.settings || '{}')
    }));
  }
}
