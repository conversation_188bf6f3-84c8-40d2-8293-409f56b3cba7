const axios = require('axios');

async function testLogin() {
  try {
    console.log('🔐 Testing login...');
    
    const loginResponse = await axios.post('http://localhost:5000/api/auth/login', {
      email: '<EMAIL>',
      password: 'password123'
    });
    
    if (loginResponse.data.success) {
      console.log('✅ Login successful!');
      console.log('User:', loginResponse.data.data.user.firstName, loginResponse.data.data.user.lastName);
      
      const token = loginResponse.data.data.token;
      console.log('Token received:', token.substring(0, 20) + '...');
      
      // Test protected route
      console.log('\n🔒 Testing protected route...');
      const meResponse = await axios.get('http://localhost:5000/api/auth/me', {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      if (meResponse.data.success) {
        console.log('✅ Protected route works!');
        console.log('User data:', meResponse.data.data.user.firstName, meResponse.data.data.user.lastName);
        console.log('Organization:', meResponse.data.data.user.organization?.name);
      } else {
        console.log('❌ Protected route failed');
      }
      
    } else {
      console.log('❌ Login failed');
    }
    
  } catch (error) {
    console.log('❌ Error:', error.response?.data?.error?.message || error.message);
  }
}

testLogin();
