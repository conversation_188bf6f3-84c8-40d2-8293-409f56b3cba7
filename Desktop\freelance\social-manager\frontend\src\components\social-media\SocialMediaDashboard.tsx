import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '../ui/tabs';
import { 
  Plus, 
  Calendar, 
  BarChart3, 
  Settings, 
  Zap,
  Clock,
  Users,
  TrendingUp,
  AlertCircle
} from 'lucide-react';
import useSocialMedia from '../../hooks/useSocialMedia';
import { ConnectedAccounts } from './ConnectedAccounts';
import { ContentScheduler } from './ContentScheduler';
import { PublishingQueue } from './PublishingQueue';
import { OptimalTimingInsights } from './OptimalTimingInsights';
import { SocialMediaAnalytics } from './SocialMediaAnalytics.tsx';

export const SocialMediaDashboard: React.FC = () => {
  const {
    accounts,
    scheduledContent,
    queueStats,
    loading,
    error,
    loadScheduledContent,
    getConnectedPlatforms,
    isConnected
  } = useSocialMedia();

  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    loadScheduledContent();
  }, [loadScheduledContent]);

  const connectedPlatforms = getConnectedPlatforms();
  const totalScheduled = scheduledContent.length;
  const queuedPosts = queueStats?.publishQueue.waiting || 0;
  const activePosts = queueStats?.publishQueue.active || 0;

  const platformStats = [
    { platform: 'INSTAGRAM', connected: isConnected('INSTAGRAM'), color: '#E4405F' },
    { platform: 'FACEBOOK', connected: isConnected('FACEBOOK'), color: '#1877F2' },
    { platform: 'TWITTER', connected: isConnected('TWITTER'), color: '#1DA1F2' },
    { platform: 'LINKEDIN', connected: isConnected('LINKEDIN'), color: '#0A66C2' },
    { platform: 'TIKTOK', connected: isConnected('TIKTOK'), color: '#000000' },
    { platform: 'YOUTUBE', connected: isConnected('YOUTUBE'), color: '#FF0000' },
    { platform: 'PINTEREST', connected: isConnected('PINTEREST'), color: '#BD081C' }
  ];

  if (loading && accounts.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Social Media Hub</h1>
          <p className="text-gray-600 mt-1">
            Manage all your social media accounts and content from one place
          </p>
        </div>
        <Button className="bg-blue-600 hover:bg-blue-700">
          <Plus className="w-4 h-4 mr-2" />
          Connect Account
        </Button>
      </div>

      {/* Error Alert */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 flex items-center">
          <AlertCircle className="w-5 h-5 text-red-500 mr-3" />
          <span className="text-red-700">{error}</span>
        </div>
      )}

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Connected Accounts</p>
                <p className="text-2xl font-bold text-gray-900">{accounts.length}</p>
              </div>
              <div className="p-3 bg-blue-100 rounded-full">
                <Users className="w-6 h-6 text-blue-600" />
              </div>
            </div>
            <div className="mt-4 flex space-x-1">
              {platformStats.map(({ platform, connected, color }) => (
                <div
                  key={platform}
                  className={`w-2 h-2 rounded-full ${
                    connected ? 'opacity-100' : 'opacity-30'
                  }`}
                  style={{ backgroundColor: color }}
                />
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Scheduled Posts</p>
                <p className="text-2xl font-bold text-gray-900">{totalScheduled}</p>
              </div>
              <div className="p-3 bg-green-100 rounded-full">
                <Calendar className="w-6 h-6 text-green-600" />
              </div>
            </div>
            <p className="text-xs text-gray-500 mt-2">
              Next post in 2 hours
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Publishing Queue</p>
                <p className="text-2xl font-bold text-gray-900">{queuedPosts + activePosts}</p>
              </div>
              <div className="p-3 bg-orange-100 rounded-full">
                <Clock className="w-6 h-6 text-orange-600" />
              </div>
            </div>
            <div className="mt-2 flex items-center space-x-2">
              <Badge variant="secondary" className="text-xs">
                {activePosts} active
              </Badge>
              <Badge variant="outline" className="text-xs">
                {queuedPosts} waiting
              </Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Engagement Rate</p>
                <p className="text-2xl font-bold text-gray-900">3.2%</p>
              </div>
              <div className="p-3 bg-purple-100 rounded-full">
                <TrendingUp className="w-6 h-6 text-purple-600" />
              </div>
            </div>
            <p className="text-xs text-green-600 mt-2 flex items-center">
              <TrendingUp className="w-3 h-3 mr-1" />
              +0.5% from last week
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="accounts">Accounts</TabsTrigger>
          <TabsTrigger value="scheduler">Scheduler</TabsTrigger>
          <TabsTrigger value="queue">Queue</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Platform Status */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Zap className="w-5 h-5 mr-2" />
                  Platform Status
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {platformStats.map(({ platform, connected, color }) => (
                    <div key={platform} className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div
                          className="w-3 h-3 rounded-full"
                          style={{ backgroundColor: color }}
                        />
                        <span className="font-medium">{platform}</span>
                      </div>
                      <Badge variant={connected ? "default" : "secondary"}>
                        {connected ? "Connected" : "Not Connected"}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Recent Activity */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <BarChart3 className="w-5 h-5 mr-2" />
                  Recent Activity
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {scheduledContent.slice(0, 5).map((content) => (
                    <div key={content.id} className="flex items-center justify-between">
                      <div>
                        <p className="font-medium text-sm truncate max-w-48">
                          {content.content}
                        </p>
                        <p className="text-xs text-gray-500">
                          {new Date(content.scheduledAt).toLocaleDateString()}
                        </p>
                      </div>
                      <div className="flex space-x-1">
                        {content.platforms.map((platform) => (
                          <Badge key={platform} variant="outline" className="text-xs">
                            {platform}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  ))}
                  {scheduledContent.length === 0 && (
                    <p className="text-gray-500 text-center py-4">
                      No scheduled content yet
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Optimal Timing Insights */}
          <OptimalTimingInsights />
        </TabsContent>

        <TabsContent value="accounts">
          <ConnectedAccounts />
        </TabsContent>

        <TabsContent value="scheduler">
          <ContentScheduler />
        </TabsContent>

        <TabsContent value="queue">
          <PublishingQueue />
        </TabsContent>

        <TabsContent value="analytics">
          <SocialMediaAnalytics />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default SocialMediaDashboard;
