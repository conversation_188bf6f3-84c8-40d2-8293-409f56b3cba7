export interface User {
  id: string
  email: string
  firstName: string
  lastName: string
  avatar?: string
  role: UserRole
  permissions?: any
  isActive: boolean
  emailVerified: boolean
  lastLoginAt?: string
  createdAt: string
  organizationId: string
  organization: Organization
}

export interface Organization {
  id: string
  name: string
  slug: string
  planType: PlanType
  status: OrganizationStatus
}

export type UserRole = 'SUPER_ADMIN' | 'AGENCY_ADMIN' | 'CLIENT_ADMIN' | 'TEAM_MEMBER' | 'CLIENT_USER';

export type PlanType = 'STARTER' | 'PROFESSIONAL' | 'AGENCY' | 'ENTERPRISE';

export type OrganizationStatus = 'ACTIVE' | 'SUSPENDED' | 'CANCELLED';

export interface LoginCredentials {
  email: string
  password: string
}

export interface RegisterData {
  email: string
  password: string
  firstName: string
  lastName: string
  organizationName?: string
}

export interface AuthResponse {
  user: User
  token: string
  organization?: Organization
}

export interface ApiResponse<T = any> {
  success: boolean
  data: T
  message?: string
  error?: {
    message: string
    details?: any
  }
}
