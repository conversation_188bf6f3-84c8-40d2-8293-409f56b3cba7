import { Router } from 'express';
import { body, validationResult, query } from 'express-validator';
import { prisma } from '../config/database.js';
import { asyncHandler, CustomError } from '../middleware/errorHandler.js';
import { AuthenticatedRequest } from '../middleware/auth.js';

const router = Router();

// Get all content items for the user's organization
router.get('/', [
  query('projectId').optional().isString(),
  query('status').optional().isIn(['DRAFT', 'REVIEW', 'APPROVED', 'SCHEDULED', 'PUBLISHED', 'FAILED']),
  query('type').optional().isIn(['POST', 'STORY', 'REEL', 'VIDEO', 'ARTICLE', 'CAROUSEL']),
  query('platforms').optional().isString(),
], asyncHandler(async (req: AuthenticatedRequest, res) => {
  const user = req.user!;
  const { projectId, status, type, platforms } = req.query;

  // Build where clause
  const where: any = {
    project: {
      organizationId: user.organizationId,
    },
  };

  if (projectId) where.projectId = projectId as string;
  if (status) where.status = status as string;
  if (type) where.type = type as string;
  if (platforms) {
    // Filter by platforms (stored as JSON string)
    where.platforms = {
      contains: platforms as string,
    };
  }

  const contentItems = await prisma.contentItem.findMany({
    where,
    include: {
      project: {
        select: {
          id: true,
          name: true,
        },
      },
      createdBy: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          avatar: true,
        },
      },
    },
    orderBy: {
      updatedAt: 'desc',
    },
  });

  // Parse platforms JSON for each item
  const processedItems = contentItems.map(item => ({
    ...item,
    platforms: JSON.parse(item.platforms),
    contentData: JSON.parse(item.contentData),
  }));

  res.json({
    success: true,
    data: { contentItems: processedItems },
  });
}));

// Get a specific content item
router.get('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const user = req.user!;
  const { id } = req.params;

  const contentItem = await prisma.contentItem.findFirst({
    where: {
      id,
      project: {
        organizationId: user.organizationId,
      },
    },
    include: {
      project: {
        select: {
          id: true,
          name: true,
        },
      },
      createdBy: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          avatar: true,
        },
      },
    },
  });

  if (!contentItem) {
    throw new CustomError('Content item not found', 404);
  }

  // Parse JSON fields
  const processedItem = {
    ...contentItem,
    platforms: JSON.parse(contentItem.platforms),
    contentData: JSON.parse(contentItem.contentData),
  };

  res.json({
    success: true,
    data: { contentItem: processedItem },
  });
}));

// Create a new content item
router.post('/', [
  body('title').trim().isLength({ min: 1 }).withMessage('Title is required'),
  body('description').optional().trim(),
  body('type').isIn(['POST', 'STORY', 'REEL', 'VIDEO', 'ARTICLE', 'CAROUSEL']).withMessage('Invalid content type'),
  body('platforms').isArray({ min: 1 }).withMessage('At least one platform is required'),
  body('platforms.*').isIn(['INSTAGRAM', 'FACEBOOK', 'TWITTER', 'LINKEDIN', 'TIKTOK', 'YOUTUBE', 'PINTEREST']),
  body('contentData').isObject().withMessage('Content data is required'),
  body('projectId').isString().withMessage('Project ID is required'),
  body('scheduledAt').optional().isISO8601().withMessage('Invalid scheduled date'),
], asyncHandler(async (req: AuthenticatedRequest, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    const errorDetails = errors.array().map(err => `${err.path}: ${err.msg}`).join(', ');
    throw new CustomError(`Validation failed: ${errorDetails}`, 400);
  }

  const user = req.user!;
  const { title, description, type, platforms, contentData, projectId, scheduledAt } = req.body;

  // Verify project exists and user has access
  const project = await prisma.project.findFirst({
    where: {
      id: projectId,
      organizationId: user.organizationId,
    },
  });

  if (!project) {
    throw new CustomError('Project not found', 404);
  }

  const contentItem = await prisma.contentItem.create({
    data: {
      title,
      description,
      type,
      platforms: JSON.stringify(platforms),
      contentData: JSON.stringify(contentData),
      scheduledAt: scheduledAt ? new Date(scheduledAt) : null,
      projectId,
      createdById: user.id,
    },
    include: {
      project: {
        select: {
          id: true,
          name: true,
        },
      },
      createdBy: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          avatar: true,
        },
      },
    },
  });

  // Parse JSON fields for response
  const processedItem = {
    ...contentItem,
    platforms: JSON.parse(contentItem.platforms),
    contentData: JSON.parse(contentItem.contentData),
  };

  res.status(201).json({
    success: true,
    data: { contentItem: processedItem },
    message: 'Content item created successfully',
  });
}));

// Update a content item
router.put('/:id', [
  body('title').optional().trim().isLength({ min: 1 }).withMessage('Title cannot be empty'),
  body('description').optional().trim(),
  body('type').optional().isIn(['POST', 'STORY', 'REEL', 'VIDEO', 'ARTICLE', 'CAROUSEL']),
  body('platforms').optional().isArray({ min: 1 }),
  body('platforms.*').optional().isIn(['INSTAGRAM', 'FACEBOOK', 'TWITTER', 'LINKEDIN', 'TIKTOK', 'YOUTUBE', 'PINTEREST']),
  body('contentData').optional().isObject(),
  body('status').optional().isIn(['DRAFT', 'REVIEW', 'APPROVED', 'SCHEDULED', 'PUBLISHED', 'FAILED']),
  body('scheduledAt').optional().isISO8601(),
], asyncHandler(async (req: AuthenticatedRequest, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    const errorDetails = errors.array().map(err => `${err.path}: ${err.msg}`).join(', ');
    throw new CustomError(`Validation failed: ${errorDetails}`, 400);
  }

  const user = req.user!;
  const { id } = req.params;
  const updateData = { ...req.body };

  // Check if content item exists and user has access
  const existingItem = await prisma.contentItem.findFirst({
    where: {
      id,
      project: {
        organizationId: user.organizationId,
      },
    },
  });

  if (!existingItem) {
    throw new CustomError('Content item not found', 404);
  }

  // Process update data
  if (updateData.platforms) {
    updateData.platforms = JSON.stringify(updateData.platforms);
  }
  if (updateData.contentData) {
    updateData.contentData = JSON.stringify(updateData.contentData);
  }
  if (updateData.scheduledAt) {
    updateData.scheduledAt = new Date(updateData.scheduledAt);
  }

  const contentItem = await prisma.contentItem.update({
    where: { id },
    data: updateData,
    include: {
      project: {
        select: {
          id: true,
          name: true,
        },
      },
      createdBy: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          avatar: true,
        },
      },
    },
  });

  // Parse JSON fields for response
  const processedItem = {
    ...contentItem,
    platforms: JSON.parse(contentItem.platforms),
    contentData: JSON.parse(contentItem.contentData),
  };

  res.json({
    success: true,
    data: { contentItem: processedItem },
    message: 'Content item updated successfully',
  });
}));

// Delete a content item
router.delete('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const user = req.user!;
  const { id } = req.params;

  // Check if content item exists and user has access
  const existingItem = await prisma.contentItem.findFirst({
    where: {
      id,
      project: {
        organizationId: user.organizationId,
      },
    },
  });

  if (!existingItem) {
    throw new CustomError('Content item not found', 404);
  }

  await prisma.contentItem.delete({
    where: { id },
  });

  res.json({
    success: true,
    message: 'Content item deleted successfully',
  });
}));

// Bulk update content status (for approval workflows)
router.patch('/bulk-status', [
  body('contentIds').isArray({ min: 1 }).withMessage('Content IDs are required'),
  body('status').isIn(['DRAFT', 'REVIEW', 'APPROVED', 'SCHEDULED', 'PUBLISHED', 'FAILED']).withMessage('Invalid status'),
], asyncHandler(async (req: AuthenticatedRequest, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    const errorDetails = errors.array().map(err => `${err.path}: ${err.msg}`).join(', ');
    throw new CustomError(`Validation failed: ${errorDetails}`, 400);
  }

  const user = req.user!;
  const { contentIds, status } = req.body;

  // Update all content items that belong to user's organization
  const result = await prisma.contentItem.updateMany({
    where: {
      id: { in: contentIds },
      project: {
        organizationId: user.organizationId,
      },
    },
    data: { status },
  });

  res.json({
    success: true,
    data: { updatedCount: result.count },
    message: `${result.count} content items updated successfully`,
  });
}));

export default router;
