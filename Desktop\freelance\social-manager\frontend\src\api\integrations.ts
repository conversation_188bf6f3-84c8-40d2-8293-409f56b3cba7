import { apiClient } from './client'
import { ApiResponse } from '../types/auth'

export interface SocialMediaIntegration {
  id: string
  organizationId: string
  platform: string
  accountName: string
  accountId: string
  isActive: boolean
  settings?: string
  lastTestedAt?: string
  expiresAt?: string
  createdAt: string
  updatedAt: string
  connectedBy: string
}

export interface PlatformCapabilities {
  contentTypes: string[]
  maxTextLength: number
  supportsScheduling: boolean
  supportsHashtags: boolean
  maxHashtags: number
  supportsImages: boolean
  supportsVideos: boolean
  maxVideoSize: number
  supportedImageFormats: string[]
  supportedVideoFormats: string[]
}

export interface ConnectIntegrationData {
  platform: string
  accountName: string
  accountId: string
  accessToken: string
  refreshToken?: string
  expiresAt?: string
}

export interface UpdateIntegrationData {
  isActive?: boolean
  settings?: Record<string, any>
}

export const integrationsAPI = {
  // Get all integrations
  getIntegrations: () => {
    return apiClient.get<ApiResponse<SocialMediaIntegration[]>>('/integrations')
  },

  // Connect a new social media account
  connectIntegration: (data: ConnectIntegrationData) => {
    return apiClient.post<ApiResponse<SocialMediaIntegration>>('/integrations/connect', data)
  },

  // Update integration settings
  updateIntegration: (id: string, data: UpdateIntegrationData) => {
    return apiClient.put<ApiResponse<SocialMediaIntegration>>(`/integrations/${id}`, data)
  },

  // Disconnect integration
  disconnectIntegration: (id: string) => {
    return apiClient.delete<ApiResponse<void>>(`/integrations/${id}`)
  },

  // Refresh access token
  refreshToken: (id: string) => {
    return apiClient.post<ApiResponse<SocialMediaIntegration>>(`/integrations/${id}/refresh`)
  },

  // Test integration connection
  testConnection: (id: string) => {
    return apiClient.post<ApiResponse<{ connected: boolean }>>(`/integrations/${id}/test`)
  },

  // Get platform capabilities
  getPlatformCapabilities: (platform: string) => {
    return apiClient.get<ApiResponse<PlatformCapabilities>>(`/integrations/platforms/${platform}/capabilities`)
  },
}
