import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { useConnectIntegration } from '../../hooks/useIntegrations'
import { XMarkIcon } from '@heroicons/react/24/outline'
import LoadingSpinner from '../ui/LoadingSpinner'

const connectPlatformSchema = z.object({
  platform: z.string().min(1, 'Platform is required'),
  accountName: z.string().min(1, 'Account name is required'),
  accountId: z.string().min(1, 'Account ID is required'),
  accessToken: z.string().min(1, 'Access token is required'),
  refreshToken: z.string().optional(),
  expiresAt: z.string().optional(),
})

type ConnectPlatformFormData = z.infer<typeof connectPlatformSchema>

interface Platform {
  id: string
  name: string
  icon: string
  color: string
  description: string
}

interface ConnectPlatformModalProps {
  availablePlatforms: Platform[]
  onClose: () => void
  onSuccess: () => void
}

const ConnectPlatformModal: React.FC<ConnectPlatformModalProps> = ({
  availablePlatforms,
  onClose,
  onSuccess,
}) => {
  const [selectedPlatform, setSelectedPlatform] = useState<Platform | null>(null)
  const connectIntegration = useConnectIntegration()

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
  } = useForm<ConnectPlatformFormData>({
    resolver: zodResolver(connectPlatformSchema),
  })

  const platform = watch('platform')

  const handlePlatformSelect = (platformData: Platform) => {
    setSelectedPlatform(platformData)
    setValue('platform', platformData.id)
  }

  const onSubmit = async (data: ConnectPlatformFormData) => {
    try {
      await connectIntegration.mutateAsync({
        platform: data.platform,
        accountName: data.accountName,
        accountId: data.accountId,
        accessToken: data.accessToken,
        refreshToken: data.refreshToken,
        expiresAt: data.expiresAt,
      })
      onSuccess()
    } catch (error) {
      // Error handling is done in the hook
    }
  }

  const generateMockCredentials = () => {
    if (!selectedPlatform) return

    // Generate mock credentials for demo purposes
    const mockData = {
      accountName: `Demo ${selectedPlatform.name} Account`,
      accountId: `${selectedPlatform.id.toLowerCase()}_${Math.random().toString(36).substr(2, 9)}`,
      accessToken: `mock_access_token_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      refreshToken: `mock_refresh_token_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      expiresAt: new Date(Date.now() + 60 * 60 * 1000).toISOString(), // 1 hour from now
    }

    setValue('accountName', mockData.accountName)
    setValue('accountId', mockData.accountId)
    setValue('accessToken', mockData.accessToken)
    setValue('refreshToken', mockData.refreshToken)
    setValue('expiresAt', mockData.expiresAt)
  }

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={onClose} />

        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <form onSubmit={handleSubmit(onSubmit)}>
            <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-medium text-gray-900">Connect Platform</h3>
                <button
                  type="button"
                  onClick={onClose}
                  className="text-gray-400 hover:text-gray-500"
                >
                  <XMarkIcon className="h-6 w-6" />
                </button>
              </div>

              <div className="space-y-6">
                {/* Platform Selection */}
                {!selectedPlatform ? (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-3">
                      Select Platform
                    </label>
                    <div className="grid grid-cols-1 gap-3">
                      {availablePlatforms.map((platformOption) => (
                        <button
                          key={platformOption.id}
                          type="button"
                          onClick={() => handlePlatformSelect(platformOption)}
                          className="flex items-center p-3 border border-gray-300 rounded-lg hover:bg-gray-50 text-left"
                        >
                          <div className={`p-2 rounded-lg ${platformOption.color} text-white mr-3`}>
                            <span className="text-lg">{platformOption.icon}</span>
                          </div>
                          <div>
                            <div className="font-medium text-gray-900">{platformOption.name}</div>
                            <div className="text-sm text-gray-600">{platformOption.description}</div>
                          </div>
                        </button>
                      ))}
                    </div>
                  </div>
                ) : (
                  <>
                    {/* Selected Platform */}
                    <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className={`p-2 rounded-lg ${selectedPlatform.color} text-white`}>
                          <span className="text-lg">{selectedPlatform.icon}</span>
                        </div>
                        <div>
                          <div className="font-medium text-gray-900">{selectedPlatform.name}</div>
                          <div className="text-sm text-gray-600">Selected platform</div>
                        </div>
                      </div>
                      <button
                        type="button"
                        onClick={() => {
                          setSelectedPlatform(null)
                          setValue('platform', '')
                        }}
                        className="text-sm text-gray-500 hover:text-gray-700"
                      >
                        Change
                      </button>
                    </div>

                    {/* Demo Notice */}
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <div className="flex items-start space-x-3">
                        <div className="text-blue-600 text-lg">ℹ️</div>
                        <div>
                          <h4 className="text-sm font-medium text-blue-900">Demo Mode</h4>
                          <p className="text-sm text-blue-700 mt-1">
                            This is a demo. In a real application, you would authenticate with {selectedPlatform.name} 
                            using OAuth. Click "Generate Demo Credentials" to simulate the connection.
                          </p>
                          <button
                            type="button"
                            onClick={generateMockCredentials}
                            className="mt-2 text-sm text-blue-600 hover:text-blue-800 font-medium"
                          >
                            Generate Demo Credentials
                          </button>
                        </div>
                      </div>
                    </div>

                    {/* Account Name */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Account Name *
                      </label>
                      <input
                        {...register('accountName')}
                        type="text"
                        className="input"
                        placeholder="Enter account name"
                      />
                      {errors.accountName && (
                        <p className="mt-1 text-sm text-red-600">{errors.accountName.message}</p>
                      )}
                    </div>

                    {/* Account ID */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Account ID *
                      </label>
                      <input
                        {...register('accountId')}
                        type="text"
                        className="input"
                        placeholder="Enter account ID"
                      />
                      {errors.accountId && (
                        <p className="mt-1 text-sm text-red-600">{errors.accountId.message}</p>
                      )}
                    </div>

                    {/* Access Token */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Access Token *
                      </label>
                      <textarea
                        {...register('accessToken')}
                        rows={3}
                        className="input"
                        placeholder="Enter access token"
                      />
                      {errors.accessToken && (
                        <p className="mt-1 text-sm text-red-600">{errors.accessToken.message}</p>
                      )}
                    </div>

                    {/* Refresh Token */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Refresh Token (optional)
                      </label>
                      <textarea
                        {...register('refreshToken')}
                        rows={2}
                        className="input"
                        placeholder="Enter refresh token"
                      />
                    </div>

                    {/* Expires At */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Token Expires At (optional)
                      </label>
                      <input
                        {...register('expiresAt')}
                        type="datetime-local"
                        className="input"
                      />
                    </div>
                  </>
                )}
              </div>
            </div>

            <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
              {selectedPlatform && (
                <button
                  type="submit"
                  disabled={connectIntegration.isPending}
                  className="btn-primary w-full sm:w-auto sm:ml-3"
                >
                  {connectIntegration.isPending ? (
                    <div className="flex items-center justify-center">
                      <LoadingSpinner size="sm" className="mr-2" />
                      Connecting...
                    </div>
                  ) : (
                    'Connect Account'
                  )}
                </button>
              )}
              <button
                type="button"
                onClick={onClose}
                className="btn-outline w-full sm:w-auto mt-3 sm:mt-0"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}

export default ConnectPlatformModal
