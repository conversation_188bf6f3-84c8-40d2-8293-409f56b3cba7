import { Request, Response, NextFunction } from 'express';
import { CacheService } from '../config/redis.js';
import { logger } from '../utils/logger.js';

interface RateLimitOptions {
  windowMs: number; // Time window in milliseconds
  maxRequests: number; // Maximum requests per window
  keyGenerator?: (req: Request) => string;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
  onLimitReached?: (req: Request, res: Response) => void;
  message?: string;
}

interface RateLimitInfo {
  count: number;
  resetTime: number;
  remaining: number;
}

/**
 * Advanced rate limiting middleware using Redis
 */
export function rateLimiter(options: RateLimitOptions) {
  const {
    windowMs,
    maxRequests,
    keyGenerator = defaultKeyGenerator,
    skipSuccessfulRequests = false,
    skipFailedRequests = false,
    onLimitReached,
    message = 'Too many requests, please try again later.'
  } = options;

  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      const key = keyGenerator(req);
      const now = Date.now();
      const windowStart = now - windowMs;

      // Get current count for this key
      const rateLimitInfo = await getRateLimitInfo(key, windowStart, now);

      // Set rate limit headers
      res.set({
        'X-RateLimit-Limit': maxRequests.toString(),
        'X-RateLimit-Remaining': Math.max(0, maxRequests - rateLimitInfo.count).toString(),
        'X-RateLimit-Reset': new Date(rateLimitInfo.resetTime).toISOString(),
        'X-RateLimit-Window': windowMs.toString()
      });

      // Check if limit exceeded
      if (rateLimitInfo.count >= maxRequests) {
        logger.warn(`Rate limit exceeded for key: ${key}`, {
          count: rateLimitInfo.count,
          limit: maxRequests,
          ip: req.ip,
          userAgent: req.get('User-Agent')
        });

        if (onLimitReached) {
          onLimitReached(req, res);
        }

        return res.status(429).json({
          error: 'Rate limit exceeded',
          message,
          retryAfter: Math.ceil((rateLimitInfo.resetTime - now) / 1000)
        });
      }

      // Store original res.end to track response status
      const originalEnd = res.end;
      res.end = function(...args: any[]) {
        const shouldCount = !skipSuccessfulRequests && !skipFailedRequests ||
                           (!skipSuccessfulRequests && res.statusCode < 400) ||
                           (!skipFailedRequests && res.statusCode >= 400);

        if (shouldCount) {
          incrementRateLimit(key, now, windowMs)
            .catch(error => logger.error('Error incrementing rate limit:', error));
        }

        return originalEnd.apply(this, args);
      };

      next();
    } catch (error) {
      logger.error('Rate limiter error:', error);
      next(); // Continue without rate limiting on error
    }
  };
}

/**
 * Get current rate limit information
 */
async function getRateLimitInfo(key: string, windowStart: number, now: number): Promise<RateLimitInfo> {
  try {
    const countKey = `ratelimit:${key}:count`;
    const timestampKey = `ratelimit:${key}:timestamp`;

    const [countStr, timestampStr] = await Promise.all([
      CacheService.get(countKey),
      CacheService.get(timestampKey)
    ]);

    const count = countStr ? parseInt(countStr, 10) : 0;
    const timestamp = timestampStr ? parseInt(timestampStr, 10) : now;

    // If the stored timestamp is outside the current window, reset
    if (timestamp < windowStart) {
      return {
        count: 0,
        resetTime: now + (windowStart - timestamp),
        remaining: 0
      };
    }

    return {
      count,
      resetTime: timestamp + (now - windowStart),
      remaining: Math.max(0, count)
    };
  } catch (error) {
    logger.error('Error getting rate limit info:', error);
    return { count: 0, resetTime: now, remaining: 0 };
  }
}

/**
 * Increment rate limit counter
 */
async function incrementRateLimit(key: string, now: number, windowMs: number): Promise<void> {
  try {
    const countKey = `ratelimit:${key}:count`;
    const timestampKey = `ratelimit:${key}:timestamp`;
    const ttl = Math.ceil(windowMs / 1000);

    // Use Redis pipeline for atomic operations
    const pipeline = [
      CacheService.set(countKey, '1', ttl),
      CacheService.set(timestampKey, now.toString(), ttl)
    ];

    // Check if keys exist to increment or set
    const exists = await CacheService.exists(countKey);
    if (exists) {
      // Increment existing counter
      await Promise.all([
        incrementCounter(countKey),
        CacheService.set(timestampKey, now.toString(), ttl)
      ]);
    } else {
      // Set new counter
      await Promise.all(pipeline);
    }
  } catch (error) {
    logger.error('Error incrementing rate limit:', error);
  }
}

/**
 * Increment counter (Redis INCR equivalent)
 */
async function incrementCounter(key: string): Promise<void> {
  try {
    const current = await CacheService.get(key);
    const newValue = current ? (parseInt(current, 10) + 1).toString() : '1';
    await CacheService.set(key, newValue);
  } catch (error) {
    logger.error('Error incrementing counter:', error);
  }
}

/**
 * Default key generator
 */
function defaultKeyGenerator(req: Request): string {
  return req.ip || 'unknown';
}

/**
 * Key generators for different rate limiting strategies
 */
export const rateLimitKeyGenerators = {
  // IP-based rate limiting
  byIP: (req: Request) => req.ip || 'unknown',

  // User-based rate limiting
  byUser: (req: Request) => {
    const userId = req.user?.id;
    return userId ? `user:${userId}` : req.ip || 'unknown';
  },

  // Organization-based rate limiting
  byOrganization: (req: Request) => {
    const orgId = req.user?.organizationId;
    return orgId ? `org:${orgId}` : req.ip || 'unknown';
  },

  // API key-based rate limiting
  byApiKey: (req: Request) => {
    const apiKey = req.headers['x-api-key'] as string;
    return apiKey ? `apikey:${apiKey}` : req.ip || 'unknown';
  },

  // Combined user and IP
  byUserAndIP: (req: Request) => {
    const userId = req.user?.id;
    const ip = req.ip || 'unknown';
    return userId ? `user:${userId}:${ip}` : ip;
  }
};

/**
 * Predefined rate limit configurations
 */
export const rateLimitConfigs = {
  // Strict rate limiting for authentication endpoints
  auth: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 5, // 5 attempts per 15 minutes
    keyGenerator: rateLimitKeyGenerators.byIP,
    message: 'Too many authentication attempts, please try again later.'
  },

  // General API rate limiting
  api: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 100, // 100 requests per minute
    keyGenerator: rateLimitKeyGenerators.byUser,
    skipSuccessfulRequests: false,
    skipFailedRequests: true
  },

  // Lenient rate limiting for dashboard/UI endpoints
  dashboard: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 200, // 200 requests per minute
    keyGenerator: rateLimitKeyGenerators.byUser
  },

  // Strict rate limiting for content publishing
  publishing: {
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 50, // 50 publishes per hour
    keyGenerator: rateLimitKeyGenerators.byOrganization,
    message: 'Publishing rate limit exceeded. Please upgrade your plan for higher limits.'
  },

  // Analytics endpoints (can be more lenient)
  analytics: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 50, // 50 requests per minute
    keyGenerator: rateLimitKeyGenerators.byUser
  },

  // File upload rate limiting
  upload: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 10, // 10 uploads per minute
    keyGenerator: rateLimitKeyGenerators.byUser,
    message: 'Upload rate limit exceeded. Please wait before uploading more files.'
  }
};

/**
 * Rate limit bypass for premium users
 */
export function createPremiumRateLimiter(baseConfig: RateLimitOptions, premiumMultiplier: number = 5) {
  return {
    ...baseConfig,
    maxRequests: baseConfig.maxRequests * premiumMultiplier,
    keyGenerator: (req: Request) => {
      const baseKey = baseConfig.keyGenerator ? baseConfig.keyGenerator(req) : defaultKeyGenerator(req);
      const userPlan = req.user?.subscription?.plan || 'free';
      return `${baseKey}:${userPlan}`;
    }
  };
}
