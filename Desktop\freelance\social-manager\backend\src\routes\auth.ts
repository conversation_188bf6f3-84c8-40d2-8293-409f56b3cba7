import { Router } from 'express';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { body, validationResult } from 'express-validator';
import { prisma } from '../config/database.js';
import { CustomError, asyncHandler } from '../middleware/errorHandler.js';

const router = Router();

// Register new user
router.post(
  '/register',
  [
    body('email').isEmail().normalizeEmail(),
    body('password').isLength({ min: 6 }).withMessage('Password must be at least 6 characters'),
    body('name').optional().trim(),
    body('firstName').optional().trim(),
    body('lastName').optional().trim(),
    body('organizationName').optional().trim(),
  ],
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new CustomError('Validation failed', 400);
    }

    const { email, password, name, firstName, lastName, organizationName } = req.body;

    // Handle both name formats
    const userFirstName = firstName || (name ? name.split(' ')[0] : 'User');
    const userLastName = lastName || (name ? name.split(' ').slice(1).join(' ') || 'Name');

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email },
    });

    if (existingUser) {
      throw new CustomError('User already exists with this email', 409);
    }

    // Hash password
    const saltRounds = 12;
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    // Create organization and user in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Create organization if provided
      let organization;
      if (organizationName) {
        organization = await tx.organization.create({
          data: {
            name: organizationName,
            slug: organizationName.toLowerCase().replace(/\s+/g, '-'),
          },
        });
      } else {
        // For now, create a default organization
        organization = await tx.organization.create({
          data: {
            name: `${firstName} ${lastName}'s Organization`,
            slug: `${firstName}-${lastName}`.toLowerCase().replace(/\s+/g, '-'),
          },
        });
      }

      // Create user
      const user = await tx.user.create({
        data: {
          email,
          password: hashedPassword,
          firstName,
          lastName,
          role: 'AGENCY_ADMIN', // First user becomes admin
          organizationId: organization.id,
        },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          role: true,
          organizationId: true,
          createdAt: true,
        },
      });

      return { user, organization };
    });

    // Generate JWT token
    const jwtSecret = process.env.JWT_SECRET;
    if (!jwtSecret) {
      throw new CustomError('JWT secret not configured', 500);
    }

    const token = jwt.sign(
      { userId: result.user.id },
      jwtSecret,
      { expiresIn: '7d' }
    );

    res.status(201).json({
      success: true,
      data: {
        user: result.user,
        organization: result.organization,
        token,
      },
      message: 'User registered successfully',
    });
  })
);

// Login user
router.post(
  '/login',
  [
    body('email').isEmail().normalizeEmail(),
    body('password').notEmpty().withMessage('Password is required'),
  ],
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new CustomError('Validation failed', 400);
    }

    const { email, password } = req.body;

    // Find user with organization
    const user = await prisma.user.findUnique({
      where: { email },
      include: {
        organization: {
          select: {
            id: true,
            name: true,
            slug: true,
            planType: true,
            status: true,
          },
        },
      },
    });

    if (!user || !user.password) {
      throw new CustomError('Invalid email or password', 401);
    }

    if (!user.isActive) {
      throw new CustomError('Account is deactivated', 401);
    }

    // Check password
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      throw new CustomError('Invalid email or password', 401);
    }

    // Update last login
    await prisma.user.update({
      where: { id: user.id },
      data: { lastLoginAt: new Date() },
    });

    // Generate JWT token
    const jwtSecret = process.env.JWT_SECRET;
    if (!jwtSecret) {
      throw new CustomError('JWT secret not configured', 500);
    }

    const token = jwt.sign(
      { userId: user.id },
      jwtSecret,
      { expiresIn: '7d' }
    );

    // Remove password from response
    const { password: _, ...userWithoutPassword } = user;

    res.json({
      success: true,
      data: {
        user: userWithoutPassword,
        token,
      },
      message: 'Login successful',
    });
  })
);

// Get current user
router.get('/me', asyncHandler(async (req, res) => {
  const token = req.header('Authorization')?.replace('Bearer ', '');
  
  if (!token) {
    throw new CustomError('Access denied. No token provided.', 401);
  }

  const jwtSecret = process.env.JWT_SECRET;
  if (!jwtSecret) {
    throw new CustomError('JWT secret not configured', 500);
  }

  const decoded = jwt.verify(token, jwtSecret) as any;
  
  const user = await prisma.user.findUnique({
    where: { id: decoded.userId },
    select: {
      id: true,
      email: true,
      firstName: true,
      lastName: true,
      avatar: true,
      role: true,
      permissions: true,
      isActive: true,
      emailVerified: true,
      lastLoginAt: true,
      createdAt: true,
      organizationId: true,
      organization: {
        select: {
          id: true,
          name: true,
          slug: true,
          planType: true,
          status: true,
        },
      },
    },
  });

  if (!user || !user.isActive) {
    throw new CustomError('User not found or inactive', 401);
  }

  res.json({
    success: true,
    data: { user },
  });
}));

export default router;
