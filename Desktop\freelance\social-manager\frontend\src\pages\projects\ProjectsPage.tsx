import { useState } from 'react'
import { useProjects, useDeleteProject } from '../../hooks/useProjects'
import { Project } from '../../types/project'
import LoadingSpinner from '../../components/ui/LoadingSpinner'
import {
  FolderIcon,
  CalendarIcon,
  CurrencyDollarIcon,
  UsersIcon,
  DocumentTextIcon,
  EllipsisVerticalIcon,
  PlusIcon
} from '@heroicons/react/24/outline'
import { Menu, Transition } from '@headlessui/react'
import { Fragment } from 'react'
import { format } from 'date-fns'

const ProjectsPage: React.FC = () => {
  const { data: projects, isLoading, error } = useProjects()
  const deleteProject = useDeleteProject()
  const [selectedProject, setSelectedProject] = useState<Project | null>(null)

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PLANNING':
        return 'bg-yellow-100 text-yellow-800'
      case 'IN_PROGRESS':
        return 'bg-blue-100 text-blue-800'
      case 'REVIEW':
        return 'bg-purple-100 text-purple-800'
      case 'COMPLETED':
        return 'bg-green-100 text-green-800'
      case 'CANCELLED':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'CAMPAIGN':
        return '🎯'
      case 'ONGOING':
        return '🔄'
      case 'ONE_TIME':
        return '⚡'
      default:
        return '📁'
    }
  }

  const handleDeleteProject = async (project: Project) => {
    if (window.confirm(`Are you sure you want to delete "${project.name}"?`)) {
      await deleteProject.mutateAsync(project.id)
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-600">Failed to load projects. Please try again.</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Projects</h1>
          <p className="text-gray-600">Manage your social media campaigns and projects</p>
        </div>
        <button className="btn-primary flex items-center space-x-2">
          <PlusIcon className="h-5 w-5" />
          <span>New Project</span>
        </button>
      </div>

      {projects && projects.length === 0 ? (
        <div className="card">
          <div className="card-content text-center py-12">
            <FolderIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No projects yet</h3>
            <p className="text-gray-500 mb-6">
              Get started by creating your first social media project
            </p>
            <button className="btn-primary">Create Your First Project</button>
          </div>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {projects?.map((project) => (
            <div key={project.id} className="card hover:shadow-lg transition-shadow">
              <div className="card-content">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="text-2xl">{getTypeIcon(project.type)}</div>
                    <div>
                      <h3 className="font-semibold text-gray-900 truncate">{project.name}</h3>
                      <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(project.status)}`}>
                        {project.status.replace('_', ' ')}
                      </span>
                    </div>
                  </div>

                  <Menu as="div" className="relative">
                    <Menu.Button className="p-1 rounded-md hover:bg-gray-100">
                      <EllipsisVerticalIcon className="h-5 w-5 text-gray-400" />
                    </Menu.Button>
                    <Transition
                      as={Fragment}
                      enter="transition ease-out duration-100"
                      enterFrom="transform opacity-0 scale-95"
                      enterTo="transform opacity-100 scale-100"
                      leave="transition ease-in duration-75"
                      leaveFrom="transform opacity-100 scale-100"
                      leaveTo="transform opacity-0 scale-95"
                    >
                      <Menu.Items className="absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                        <Menu.Item>
                          {({ active }) => (
                            <button
                              className={`block w-full text-left px-4 py-2 text-sm ${
                                active ? 'bg-gray-100 text-gray-900' : 'text-gray-700'
                              }`}
                            >
                              Edit Project
                            </button>
                          )}
                        </Menu.Item>
                        <Menu.Item>
                          {({ active }) => (
                            <button
                              onClick={() => handleDeleteProject(project)}
                              className={`block w-full text-left px-4 py-2 text-sm ${
                                active ? 'bg-gray-100 text-red-900' : 'text-red-700'
                              }`}
                            >
                              Delete Project
                            </button>
                          )}
                        </Menu.Item>
                      </Menu.Items>
                    </Transition>
                  </Menu>
                </div>

                {project.description && (
                  <p className="text-gray-600 text-sm mb-4 line-clamp-2">{project.description}</p>
                )}

                <div className="space-y-3">
                  {project.deadline && (
                    <div className="flex items-center text-sm text-gray-500">
                      <CalendarIcon className="h-4 w-4 mr-2" />
                      <span>Due {format(new Date(project.deadline), 'MMM d, yyyy')}</span>
                    </div>
                  )}

                  {project.budget && (
                    <div className="flex items-center text-sm text-gray-500">
                      <CurrencyDollarIcon className="h-4 w-4 mr-2" />
                      <span>${project.budget.toLocaleString()}</span>
                    </div>
                  )}

                  <div className="flex items-center justify-between text-sm text-gray-500">
                    <div className="flex items-center">
                      <UsersIcon className="h-4 w-4 mr-1" />
                      <span>{project.members?.length || 0} members</span>
                    </div>
                    <div className="flex items-center">
                      <DocumentTextIcon className="h-4 w-4 mr-1" />
                      <span>{project.contentItems?.length || 0} content</span>
                    </div>
                  </div>
                </div>

                <div className="mt-4 pt-4 border-t border-gray-200">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div className="h-6 w-6 bg-gray-300 rounded-full flex items-center justify-center">
                        <span className="text-xs font-medium text-gray-700">
                          {project.createdBy.firstName[0]}{project.createdBy.lastName[0]}
                        </span>
                      </div>
                      <span className="text-sm text-gray-600">
                        {project.createdBy.firstName} {project.createdBy.lastName}
                      </span>
                    </div>
                    <span className="text-xs text-gray-500">
                      {format(new Date(project.createdAt), 'MMM d')}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}

export default ProjectsPage
