import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTit<PERSON> } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '../ui/dialog';
import { 
  Plus, 
  Settings, 
  Trash2, 
  ExternalLink,
  CheckCircle,
  AlertCircle,
  RefreshCw
} from 'lucide-react';
import useSocialMedia from '../../hooks/useSocialMedia';

const PLATFORM_CONFIG = {
  INSTAGRAM: { name: 'Instagram', icon: '📷', color: '#E4405F' },
  FACEBOOK: { name: 'Facebook', icon: '👥', color: '#1877F2' },
  TWITTER: { name: 'Twitter', icon: '🐦', color: '#1DA1F2' },
  LINKEDIN: { name: 'LinkedIn', icon: '💼', color: '#0A66C2' },
  TIKTOK: { name: 'TikTok', icon: '🎵', color: '#000000' },
  YOUTUBE: { name: 'YouTube', icon: '📺', color: '#FF0000' },
  PINTEREST: { name: 'Pinterest', icon: '📌', color: '#BD081C' }
};

export const ConnectedAccounts: React.FC = () => {
  const {
    accounts,
    loading,
    error,
    disconnectAccount,
    getOAuthUrl,
    loadAccounts
  } = useSocialMedia();

  const [disconnecting, setDisconnecting] = useState<string | null>(null);
  const [showConnectDialog, setShowConnectDialog] = useState(false);

  const handleDisconnect = async (accountId: string) => {
    setDisconnecting(accountId);
    try {
      await disconnectAccount(accountId);
    } finally {
      setDisconnecting(null);
    }
  };

  const handleConnect = (platform: string) => {
    const oauthUrl = getOAuthUrl(platform);
    window.open(oauthUrl, '_blank', 'width=600,height=600');
    setShowConnectDialog(false);
  };

  const availablePlatforms = Object.keys(PLATFORM_CONFIG).filter(
    platform => !accounts.some(account => account.platform === platform && account.isActive)
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Connected Accounts</h2>
          <p className="text-gray-600 mt-1">
            Manage your social media account connections
          </p>
        </div>
        <div className="flex space-x-3">
          <Button variant="outline" onClick={loadAccounts} disabled={loading}>
            <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Dialog open={showConnectDialog} onOpenChange={setShowConnectDialog}>
            <DialogTrigger asChild>
              <Button className="bg-blue-600 hover:bg-blue-700">
                <Plus className="w-4 h-4 mr-2" />
                Connect Account
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-md">
              <DialogHeader>
                <DialogTitle>Connect Social Media Account</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <p className="text-sm text-gray-600">
                  Choose a platform to connect to your account:
                </p>
                <div className="grid grid-cols-2 gap-3">
                  {availablePlatforms.map((platform) => {
                    const config = PLATFORM_CONFIG[platform as keyof typeof PLATFORM_CONFIG];
                    return (
                      <Button
                        key={platform}
                        variant="outline"
                        className="h-16 flex flex-col items-center justify-center space-y-1"
                        onClick={() => handleConnect(platform)}
                      >
                        <span className="text-2xl">{config.icon}</span>
                        <span className="text-sm">{config.name}</span>
                      </Button>
                    );
                  })}
                </div>
                {availablePlatforms.length === 0 && (
                  <p className="text-center text-gray-500 py-4">
                    All supported platforms are already connected
                  </p>
                )}
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Error Alert */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 flex items-center">
          <AlertCircle className="w-5 h-5 text-red-500 mr-3" />
          <span className="text-red-700">{error}</span>
        </div>
      )}

      {/* Connected Accounts Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {accounts.map((account) => {
          const config = PLATFORM_CONFIG[account.platform as keyof typeof PLATFORM_CONFIG];
          const isDisconnecting = disconnecting === account.id;

          return (
            <Card key={account.id} className="relative">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div
                      className="w-10 h-10 rounded-full flex items-center justify-center text-white text-lg"
                      style={{ backgroundColor: config.color }}
                    >
                      {config.icon}
                    </div>
                    <div>
                      <CardTitle className="text-lg">{config.name}</CardTitle>
                      <p className="text-sm text-gray-600">@{account.accountName}</p>
                    </div>
                  </div>
                  <Badge variant={account.isActive ? "default" : "secondary"}>
                    {account.isActive ? (
                      <CheckCircle className="w-3 h-3 mr-1" />
                    ) : (
                      <AlertCircle className="w-3 h-3 mr-1" />
                    )}
                    {account.isActive ? "Active" : "Inactive"}
                  </Badge>
                </div>
              </CardHeader>
              
              <CardContent className="pt-0">
                <div className="space-y-4">
                  {/* Account Info */}
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Account ID:</span>
                      <span className="font-mono text-xs">{account.accountId.slice(0, 12)}...</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Connected:</span>
                      <span className="text-green-600">✓ Authorized</span>
                    </div>
                  </div>

                  {/* Account Settings */}
                  {account.settings && Object.keys(account.settings).length > 0 && (
                    <div className="border-t pt-3">
                      <p className="text-sm font-medium text-gray-700 mb-2">Settings:</p>
                      <div className="space-y-1">
                        {Object.entries(account.settings).slice(0, 3).map(([key, value]) => (
                          <div key={key} className="flex justify-between text-xs">
                            <span className="text-gray-600 capitalize">{key}:</span>
                            <span className="text-gray-800 truncate max-w-24">
                              {typeof value === 'boolean' ? (value ? 'Yes' : 'No') : String(value)}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Actions */}
                  <div className="flex space-x-2 pt-2">
                    <Button variant="outline" size="sm" className="flex-1">
                      <Settings className="w-3 h-3 mr-1" />
                      Settings
                    </Button>
                    <Button variant="outline" size="sm">
                      <ExternalLink className="w-3 h-3" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                      onClick={() => handleDisconnect(account.id)}
                      disabled={isDisconnecting}
                    >
                      {isDisconnecting ? (
                        <RefreshCw className="w-3 h-3 animate-spin" />
                      ) : (
                        <Trash2 className="w-3 h-3" />
                      )}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Empty State */}
      {accounts.length === 0 && !loading && (
        <Card className="text-center py-12">
          <CardContent>
            <div className="space-y-4">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto">
                <Plus className="w-8 h-8 text-gray-400" />
              </div>
              <div>
                <h3 className="text-lg font-medium text-gray-900">No accounts connected</h3>
                <p className="text-gray-600 mt-1">
                  Connect your social media accounts to start managing your content
                </p>
              </div>
              <Button 
                className="bg-blue-600 hover:bg-blue-700"
                onClick={() => setShowConnectDialog(true)}
              >
                <Plus className="w-4 h-4 mr-2" />
                Connect Your First Account
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Platform Statistics */}
      {accounts.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Platform Overview</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4">
              {Object.entries(PLATFORM_CONFIG).map(([platform, config]) => {
                const isConnected = accounts.some(
                  account => account.platform === platform && account.isActive
                );
                
                return (
                  <div
                    key={platform}
                    className={`text-center p-4 rounded-lg border-2 transition-colors ${
                      isConnected 
                        ? 'border-green-200 bg-green-50' 
                        : 'border-gray-200 bg-gray-50'
                    }`}
                  >
                    <div className="text-2xl mb-2">{config.icon}</div>
                    <p className="text-sm font-medium">{config.name}</p>
                    <Badge 
                      variant={isConnected ? "default" : "secondary"}
                      className="mt-2 text-xs"
                    >
                      {isConnected ? "Connected" : "Available"}
                    </Badge>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default ConnectedAccounts;
