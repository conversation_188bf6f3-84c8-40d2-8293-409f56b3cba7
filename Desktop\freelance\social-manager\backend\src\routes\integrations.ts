import { Router } from 'express';
import { body, query } from 'express-validator';
import { prisma } from '../config/database.js';
import { asyncHandler } from '../middleware/errorHandler.js';
import { AuthenticatedRequest } from '../middleware/auth.js';
import { validateRequest } from '../middleware/validation.js';

const router = Router();

// Get all integrations for the organization
router.get('/', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const user = req.user!;

  const integrations = await prisma.socialMediaIntegration.findMany({
    where: {
      organizationId: user.organizationId,
    },
    orderBy: {
      createdAt: 'desc',
    },
  });

  res.json({
    success: true,
    data: integrations,
  });
}));

// Connect a new social media account
router.post('/connect', [
  body('platform').isIn(['INSTAGRAM', 'FACEBOOK', 'TWITTER', 'LINKEDIN', 'TIKTOK', 'YOUTUBE', 'PINTEREST']),
  body('accountName').isString().notEmpty(),
  body('accountId').isString().notEmpty(),
  body('accessToken').isString().notEmpty(),
  body('refreshToken').optional().isString(),
  body('expiresAt').optional().isISO8601(),
], validateRequest, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const user = req.user!;
  const { platform, accountName, accountId, accessToken, refreshToken, expiresAt } = req.body;

  // Check if integration already exists
  const existingIntegration = await prisma.socialMediaIntegration.findFirst({
    where: {
      organizationId: user.organizationId,
      platform,
      accountId,
    },
  });

  if (existingIntegration) {
    return res.status(400).json({
      success: false,
      error: { message: 'This account is already connected' },
    });
  }

  // Create new integration
  const integration = await prisma.socialMediaIntegration.create({
    data: {
      organizationId: user.organizationId,
      platform,
      accountName,
      accountId,
      accessToken,
      refreshToken,
      expiresAt: expiresAt ? new Date(expiresAt) : null,
      isActive: true,
      connectedBy: user.id,
    },
  });

  res.status(201).json({
    success: true,
    data: integration,
    message: 'Social media account connected successfully',
  });
}));

// Update integration settings
router.put('/:id', [
  body('isActive').optional().isBoolean(),
  body('settings').optional().isObject(),
], validateRequest, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const user = req.user!;
  const { id } = req.params;
  const { isActive, settings } = req.body;

  // Verify integration belongs to user's organization
  const integration = await prisma.socialMediaIntegration.findFirst({
    where: {
      id,
      organizationId: user.organizationId,
    },
  });

  if (!integration) {
    return res.status(404).json({
      success: false,
      error: { message: 'Integration not found' },
    });
  }

  // Update integration
  const updatedIntegration = await prisma.socialMediaIntegration.update({
    where: { id },
    data: {
      ...(isActive !== undefined && { isActive }),
      ...(settings && { settings: JSON.stringify(settings) }),
      updatedAt: new Date(),
    },
  });

  res.json({
    success: true,
    data: updatedIntegration,
    message: 'Integration updated successfully',
  });
}));

// Disconnect integration
router.delete('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const user = req.user!;
  const { id } = req.params;

  // Verify integration belongs to user's organization
  const integration = await prisma.socialMediaIntegration.findFirst({
    where: {
      id,
      organizationId: user.organizationId,
    },
  });

  if (!integration) {
    return res.status(404).json({
      success: false,
      error: { message: 'Integration not found' },
    });
  }

  // Delete integration
  await prisma.socialMediaIntegration.delete({
    where: { id },
  });

  res.json({
    success: true,
    message: 'Integration disconnected successfully',
  });
}));

// Refresh access token
router.post('/:id/refresh', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const user = req.user!;
  const { id } = req.params;

  // Verify integration belongs to user's organization
  const integration = await prisma.socialMediaIntegration.findFirst({
    where: {
      id,
      organizationId: user.organizationId,
    },
  });

  if (!integration) {
    return res.status(404).json({
      success: false,
      error: { message: 'Integration not found' },
    });
  }

  // In a real app, this would call the platform's API to refresh the token
  // For now, we'll simulate a successful refresh
  const newAccessToken = `refreshed_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  const newExpiresAt = new Date(Date.now() + 60 * 60 * 1000); // 1 hour from now

  const updatedIntegration = await prisma.socialMediaIntegration.update({
    where: { id },
    data: {
      accessToken: newAccessToken,
      expiresAt: newExpiresAt,
      updatedAt: new Date(),
    },
  });

  res.json({
    success: true,
    data: updatedIntegration,
    message: 'Access token refreshed successfully',
  });
}));

// Test integration connection
router.post('/:id/test', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const user = req.user!;
  const { id } = req.params;

  // Verify integration belongs to user's organization
  const integration = await prisma.socialMediaIntegration.findFirst({
    where: {
      id,
      organizationId: user.organizationId,
    },
  });

  if (!integration) {
    return res.status(404).json({
      success: false,
      error: { message: 'Integration not found' },
    });
  }

  // In a real app, this would test the connection to the platform's API
  // For now, we'll simulate a test
  const isConnected = Math.random() > 0.1; // 90% success rate

  if (isConnected) {
    // Update last tested timestamp
    await prisma.socialMediaIntegration.update({
      where: { id },
      data: {
        lastTestedAt: new Date(),
        updatedAt: new Date(),
      },
    });

    res.json({
      success: true,
      data: { connected: true },
      message: 'Connection test successful',
    });
  } else {
    res.status(400).json({
      success: false,
      error: { message: 'Connection test failed. Please check your credentials.' },
    });
  }
}));

// Get platform-specific publishing capabilities
router.get('/platforms/:platform/capabilities', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { platform } = req.params;

  // Validate platform
  const validPlatforms = ['INSTAGRAM', 'FACEBOOK', 'TWITTER', 'LINKEDIN', 'TIKTOK', 'YOUTUBE', 'PINTEREST'];
  if (!validPlatforms.includes(platform.toUpperCase())) {
    return res.status(400).json({
      success: false,
      error: { message: 'Invalid platform. Must be one of: ' + validPlatforms.join(', ') }
    });
  }

  // Platform capabilities (in a real app, this might be dynamic)
  const capabilities = {
    INSTAGRAM: {
      contentTypes: ['POST', 'STORY', 'REEL'],
      maxTextLength: 2200,
      supportsScheduling: true,
      supportsHashtags: true,
      maxHashtags: 30,
      supportsImages: true,
      supportsVideos: true,
      maxVideoSize: 100 * 1024 * 1024, // 100MB
      supportedImageFormats: ['jpg', 'jpeg', 'png'],
      supportedVideoFormats: ['mp4', 'mov'],
    },
    FACEBOOK: {
      contentTypes: ['POST'],
      maxTextLength: 63206,
      supportsScheduling: true,
      supportsHashtags: true,
      maxHashtags: 50,
      supportsImages: true,
      supportsVideos: true,
      maxVideoSize: 4 * 1024 * 1024 * 1024, // 4GB
      supportedImageFormats: ['jpg', 'jpeg', 'png', 'gif'],
      supportedVideoFormats: ['mp4', 'mov', 'avi'],
    },
    TWITTER: {
      contentTypes: ['POST'],
      maxTextLength: 280,
      supportsScheduling: true,
      supportsHashtags: true,
      maxHashtags: 10,
      supportsImages: true,
      supportsVideos: true,
      maxVideoSize: 512 * 1024 * 1024, // 512MB
      supportedImageFormats: ['jpg', 'jpeg', 'png', 'gif'],
      supportedVideoFormats: ['mp4', 'mov'],
    },
    LINKEDIN: {
      contentTypes: ['POST', 'ARTICLE'],
      maxTextLength: 3000,
      supportsScheduling: true,
      supportsHashtags: true,
      maxHashtags: 20,
      supportsImages: true,
      supportsVideos: true,
      maxVideoSize: 5 * 1024 * 1024 * 1024, // 5GB
      supportedImageFormats: ['jpg', 'jpeg', 'png'],
      supportedVideoFormats: ['mp4', 'mov', 'avi'],
    },
    TIKTOK: {
      contentTypes: ['VIDEO'],
      maxTextLength: 150,
      supportsScheduling: false,
      supportsHashtags: true,
      maxHashtags: 20,
      supportsImages: false,
      supportsVideos: true,
      maxVideoSize: 287 * 1024 * 1024, // 287MB
      supportedImageFormats: [],
      supportedVideoFormats: ['mp4', 'mov'],
    },
    YOUTUBE: {
      contentTypes: ['VIDEO'],
      maxTextLength: 5000,
      supportsScheduling: true,
      supportsHashtags: true,
      maxHashtags: 15,
      supportsImages: false,
      supportsVideos: true,
      maxVideoSize: 256 * 1024 * 1024 * 1024, // 256GB
      supportedImageFormats: [],
      supportedVideoFormats: ['mp4', 'mov', 'avi', 'wmv', 'flv'],
    },
    PINTEREST: {
      contentTypes: ['POST'],
      maxTextLength: 500,
      supportsScheduling: true,
      supportsHashtags: true,
      maxHashtags: 20,
      supportsImages: true,
      supportsVideos: true,
      maxVideoSize: 2 * 1024 * 1024 * 1024, // 2GB
      supportedImageFormats: ['jpg', 'jpeg', 'png'],
      supportedVideoFormats: ['mp4', 'mov'],
    },
  };

  res.json({
    success: true,
    data: capabilities[platform.toUpperCase() as keyof typeof capabilities] || null,
  });
}));

export default router;
