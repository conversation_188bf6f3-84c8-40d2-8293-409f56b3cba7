// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

// Core Models
model Organization {
  id          String   @id @default(cuid())
  name        String
  slug        String   @unique
  planType    String   @default("STARTER") // STARTER, PROFESSIONAL, AGENCY, ENTERPRISE
  status      String   @default("ACTIVE")  // ACTIVE, SUSPENDED, CANCELLED
  settings    String?  // JSON string
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  users       User[]
  projects    Project[]
  socialAccounts SocialAccount[]
  subscriptions Subscription[]
  invitations Invitation[]

  @@map("organizations")
}

model User {
  id             String   @id @default(cuid())
  email          String   @unique
  password       String?
  firstName      String
  lastName       String
  avatar         String?
  role           UserRole @default(TEAM_MEMBER)
  permissions    Json?
  isActive       Boolean  @default(true)
  emailVerified  <PERSON>olean  @default(false)
  lastLoginAt    DateTime?
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // Foreign Keys
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  // Relations
  projects       ProjectMember[]
  contentItems   ContentItem[]
  comments       Comment[]
  notifications  Notification[]
  auditLogs      AuditLog[]
  createdProjects Project[] @relation("ProjectCreator")

  @@map("users")
}

model Project {
  id          String        @id @default(cuid())
  name        String
  description String?
  type        ProjectType   @default(CAMPAIGN)
  status      ProjectStatus @default(PLANNING)
  budget      Float?
  deadline    DateTime?
  startDate   DateTime?
  endDate     DateTime?
  metadata    Json?
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt

  // Foreign Keys
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  createdById    String
  createdBy      User @relation("ProjectCreator", fields: [createdById], references: [id])

  // Relations
  members      ProjectMember[]
  contentItems ContentItem[]
  milestones   Milestone[]
  comments     Comment[]

  @@map("projects")
}

model ProjectMember {
  id        String      @id @default(cuid())
  role      ProjectRole @default(MEMBER)
  joinedAt  DateTime    @default(now())

  // Foreign Keys
  projectId String
  project   Project @relation(fields: [projectId], references: [id], onDelete: Cascade)
  userId    String
  user      User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([projectId, userId])
  @@map("project_members")
}

model ContentItem {
  id          String        @id @default(cuid())
  title       String
  description String?
  type        ContentType
  platforms   String // JSON array of platforms
  contentData Json
  status      ContentStatus @default(DRAFT)
  scheduledAt DateTime?
  publishedAt DateTime?
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt

  // Foreign Keys
  projectId String
  project   Project @relation(fields: [projectId], references: [id], onDelete: Cascade)
  createdById String
  createdBy   User @relation(fields: [createdById], references: [id])

  // Relations
  assets       Asset[]
  comments     Comment[]
  analytics    AnalyticsEvent[]
  publications Publication[]

  @@map("content_items")
}

model Asset {
  id          String    @id @default(cuid())
  filename    String
  originalName String
  mimeType    String
  size        Int
  url         String
  thumbnailUrl String?
  metadata    Json?
  createdAt   DateTime  @default(now())

  // Foreign Keys
  contentItemId String?
  contentItem   ContentItem? @relation(fields: [contentItemId], references: [id], onDelete: SetNull)

  @@map("assets")
}

model SocialAccount {
  id          String   @id @default(cuid())
  platform    Platform
  accountId   String
  username    String
  displayName String?
  avatar      String?
  accessToken String
  refreshToken String?
  expiresAt   DateTime?
  isActive    Boolean  @default(true)
  metadata    Json?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Foreign Keys
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  // Relations
  publications Publication[]

  @@unique([organizationId, platform, accountId])
  @@map("social_accounts")
}

model Publication {
  id            String           @id @default(cuid())
  platform      Platform
  platformPostId String?
  status        PublicationStatus @default(SCHEDULED)
  scheduledAt   DateTime
  publishedAt   DateTime?
  error         String?
  metadata      Json?
  createdAt     DateTime         @default(now())
  updatedAt     DateTime         @updatedAt

  // Foreign Keys
  contentItemId   String
  contentItem     ContentItem @relation(fields: [contentItemId], references: [id], onDelete: Cascade)
  socialAccountId String
  socialAccount   SocialAccount @relation(fields: [socialAccountId], references: [id], onDelete: Cascade)

  @@map("publications")
}

model AnalyticsEvent {
  id        String    @id @default(cuid())
  platform  Platform
  eventType EventType
  value     Int
  metadata  Json?
  timestamp DateTime  @default(now())

  // Foreign Keys
  contentItemId String
  contentItem   ContentItem @relation(fields: [contentItemId], references: [id], onDelete: Cascade)

  @@map("analytics_events")
}

model Comment {
  id        String   @id @default(cuid())
  content   String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Foreign Keys
  userId    String
  user      User @relation(fields: [userId], references: [id], onDelete: Cascade)
  projectId String?
  project   Project? @relation(fields: [projectId], references: [id], onDelete: Cascade)
  contentItemId String?
  contentItem   ContentItem? @relation(fields: [contentItemId], references: [id], onDelete: Cascade)

  @@map("comments")
}

model Milestone {
  id          String          @id @default(cuid())
  title       String
  description String?
  dueDate     DateTime
  status      MilestoneStatus @default(PENDING)
  createdAt   DateTime        @default(now())
  updatedAt   DateTime        @updatedAt

  // Foreign Keys
  projectId String
  project   Project @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@map("milestones")
}

model Subscription {
  id              String           @id @default(cuid())
  stripeCustomerId String?         @unique
  stripePriceId   String
  status          SubscriptionStatus @default(ACTIVE)
  currentPeriodStart DateTime
  currentPeriodEnd   DateTime
  cancelAtPeriodEnd  Boolean       @default(false)
  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @updatedAt

  // Foreign Keys
  organizationId String @unique
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@map("subscriptions")
}

model Invitation {
  id        String           @id @default(cuid())
  email     String
  role      UserRole
  token     String           @unique
  status    InvitationStatus @default(PENDING)
  expiresAt DateTime
  createdAt DateTime         @default(now())

  // Foreign Keys
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@map("invitations")
}

model Notification {
  id        String           @id @default(cuid())
  title     String
  message   String
  type      NotificationType
  isRead    Boolean          @default(false)
  metadata  Json?
  createdAt DateTime         @default(now())

  // Foreign Keys
  userId String
  user   User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("notifications")
}

model AuditLog {
  id        String   @id @default(cuid())
  action    String
  resource  String
  resourceId String?
  metadata  Json?
  ipAddress String?
  userAgent String?
  createdAt DateTime @default(now())

  // Foreign Keys
  userId String
  user   User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("audit_logs")
}

// Enums
enum PlanType {
  STARTER
  PROFESSIONAL
  AGENCY
  ENTERPRISE
}

enum OrganizationStatus {
  ACTIVE
  SUSPENDED
  CANCELLED
}

enum UserRole {
  SUPER_ADMIN
  AGENCY_ADMIN
  CLIENT_ADMIN
  TEAM_MEMBER
  CLIENT_USER
}

enum ProjectType {
  CAMPAIGN
  ONGOING
  ONE_TIME
}

enum ProjectStatus {
  PLANNING
  IN_PROGRESS
  REVIEW
  COMPLETED
  CANCELLED
}

enum ProjectRole {
  OWNER
  MANAGER
  MEMBER
  VIEWER
}

enum ContentType {
  POST
  STORY
  REEL
  VIDEO
  ARTICLE
  CAROUSEL
}

enum ContentStatus {
  DRAFT
  REVIEW
  APPROVED
  SCHEDULED
  PUBLISHED
  FAILED
}

enum Platform {
  INSTAGRAM
  FACEBOOK
  TWITTER
  LINKEDIN
  TIKTOK
  YOUTUBE
  PINTEREST
}

enum PublicationStatus {
  SCHEDULED
  PUBLISHING
  PUBLISHED
  FAILED
  CANCELLED
}

enum EventType {
  IMPRESSION
  REACH
  ENGAGEMENT
  LIKE
  COMMENT
  SHARE
  CLICK
  SAVE
  FOLLOW
}

enum MilestoneStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  OVERDUE
}

enum SubscriptionStatus {
  ACTIVE
  PAST_DUE
  CANCELLED
  INCOMPLETE
}

enum InvitationStatus {
  PENDING
  ACCEPTED
  EXPIRED
  CANCELLED
}

enum NotificationType {
  INFO
  SUCCESS
  WARNING
  ERROR
  MENTION
  ASSIGNMENT
}
