import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '../ui/card';
import { But<PERSON> } from '../ui/button';
import { Badge } from '../ui/badge';
import { 
  Download, 
  Eye, 
  Calendar,
  DollarSign,
  FileText,
  CheckCircle,
  Clock,
  AlertCircle
} from 'lucide-react';

// Mock invoice data
const MOCK_INVOICES = [
  {
    id: 'inv_001',
    number: 'INV-2024-001',
    amount: 79.00,
    currency: 'usd',
    status: 'paid',
    dueDate: '2024-01-15',
    paidAt: '2024-01-10',
    description: 'Professional Plan - January 2024',
    downloadUrl: '#'
  },
  {
    id: 'inv_002',
    number: 'INV-2024-002',
    amount: 79.00,
    currency: 'usd',
    status: 'paid',
    dueDate: '2024-02-15',
    paidAt: '2024-02-12',
    description: 'Professional Plan - February 2024',
    downloadUrl: '#'
  },
  {
    id: 'inv_003',
    number: 'INV-2024-003',
    amount: 79.00,
    currency: 'usd',
    status: 'open',
    dueDate: '2024-03-15',
    paidAt: null,
    description: 'Professional Plan - March 2024',
    downloadUrl: '#'
  }
];

export const InvoiceHistory: React.FC = () => {
  const formatCurrency = (amount: number, currency: string = 'usd') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency.toUpperCase(),
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      paid: {
        variant: 'default' as const,
        icon: <CheckCircle className="w-3 h-3 mr-1" />,
        label: 'Paid',
        color: 'text-green-600 bg-green-100'
      },
      open: {
        variant: 'secondary' as const,
        icon: <Clock className="w-3 h-3 mr-1" />,
        label: 'Pending',
        color: 'text-yellow-600 bg-yellow-100'
      },
      overdue: {
        variant: 'destructive' as const,
        icon: <AlertCircle className="w-3 h-3 mr-1" />,
        label: 'Overdue',
        color: 'text-red-600 bg-red-100'
      }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.open;

    return (
      <Badge variant={config.variant} className={`${config.color} flex items-center`}>
        {config.icon}
        {config.label}
      </Badge>
    );
  };

  const handleDownload = (invoice: typeof MOCK_INVOICES[0]) => {
    console.log('Downloading invoice:', invoice.number);
    // In a real app, this would trigger the download
  };

  const handleView = (invoice: typeof MOCK_INVOICES[0]) => {
    console.log('Viewing invoice:', invoice.number);
    // In a real app, this would open the invoice in a modal or new tab
  };

  const totalPaid = MOCK_INVOICES
    .filter(inv => inv.status === 'paid')
    .reduce((sum, inv) => sum + inv.amount, 0);

  const pendingAmount = MOCK_INVOICES
    .filter(inv => inv.status === 'open')
    .reduce((sum, inv) => sum + inv.amount, 0);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Invoice History</h2>
          <p className="text-gray-600 mt-1">
            View and download your billing history
          </p>
        </div>
        <Button variant="outline">
          <Download className="w-4 h-4 mr-2" />
          Download All
        </Button>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Paid</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatCurrency(totalPaid)}
                </p>
              </div>
              <div className="p-3 bg-green-100 rounded-full">
                <DollarSign className="w-6 h-6 text-green-600" />
              </div>
            </div>
            <p className="text-xs text-gray-500 mt-2">
              Lifetime payments
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Pending</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatCurrency(pendingAmount)}
                </p>
              </div>
              <div className="p-3 bg-yellow-100 rounded-full">
                <Clock className="w-6 h-6 text-yellow-600" />
              </div>
            </div>
            <p className="text-xs text-gray-500 mt-2">
              Outstanding balance
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Invoices</p>
                <p className="text-2xl font-bold text-gray-900">
                  {MOCK_INVOICES.length}
                </p>
              </div>
              <div className="p-3 bg-blue-100 rounded-full">
                <FileText className="w-6 h-6 text-blue-600" />
              </div>
            </div>
            <p className="text-xs text-gray-500 mt-2">
              All time
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Invoices Table */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Invoices</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-3 px-2 font-medium text-gray-700">Invoice</th>
                  <th className="text-left py-3 px-2 font-medium text-gray-700">Description</th>
                  <th className="text-left py-3 px-2 font-medium text-gray-700">Amount</th>
                  <th className="text-left py-3 px-2 font-medium text-gray-700">Due Date</th>
                  <th className="text-left py-3 px-2 font-medium text-gray-700">Status</th>
                  <th className="text-center py-3 px-2 font-medium text-gray-700">Actions</th>
                </tr>
              </thead>
              <tbody>
                {MOCK_INVOICES.map((invoice) => (
                  <tr key={invoice.id} className="border-b hover:bg-gray-50">
                    <td className="py-4 px-2">
                      <div>
                        <p className="font-medium text-gray-900">{invoice.number}</p>
                        {invoice.paidAt && (
                          <p className="text-sm text-gray-500">
                            Paid on {formatDate(invoice.paidAt)}
                          </p>
                        )}
                      </div>
                    </td>
                    <td className="py-4 px-2">
                      <p className="text-sm text-gray-900">{invoice.description}</p>
                    </td>
                    <td className="py-4 px-2">
                      <p className="font-medium text-gray-900">
                        {formatCurrency(invoice.amount, invoice.currency)}
                      </p>
                    </td>
                    <td className="py-4 px-2">
                      <div className="flex items-center space-x-1">
                        <Calendar className="w-4 h-4 text-gray-400" />
                        <span className="text-sm text-gray-600">
                          {formatDate(invoice.dueDate)}
                        </span>
                      </div>
                    </td>
                    <td className="py-4 px-2">
                      {getStatusBadge(invoice.status)}
                    </td>
                    <td className="py-4 px-2">
                      <div className="flex items-center justify-center space-x-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleView(invoice)}
                        >
                          <Eye className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDownload(invoice)}
                        >
                          <Download className="w-4 h-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Payment Information */}
      <Card>
        <CardHeader>
          <CardTitle>Payment Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium text-gray-900 mb-3">Billing Cycle</h4>
              <p className="text-sm text-gray-600 mb-2">
                Your subscription renews monthly on the 15th of each month.
              </p>
              <p className="text-sm text-gray-600">
                Next billing date: <span className="font-medium">March 15, 2024</span>
              </p>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-3">Payment Method</h4>
              <div className="flex items-center space-x-2">
                <div className="w-8 h-6 bg-blue-500 rounded flex items-center justify-center text-white text-xs">
                  💳
                </div>
                <span className="text-sm text-gray-900">Visa •••• 4242</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tax Information */}
      <Card>
        <CardHeader>
          <CardTitle>Tax Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Tax ID</h4>
              <p className="text-sm text-gray-600">
                No tax ID provided. <Button variant="link" className="p-0 h-auto text-blue-600">Add tax ID</Button>
              </p>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Tax Exemption</h4>
              <p className="text-sm text-gray-600">
                Not tax exempt. <Button variant="link" className="p-0 h-auto text-blue-600">Apply for exemption</Button>
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Empty State (if no invoices) */}
      {MOCK_INVOICES.length === 0 && (
        <Card>
          <CardContent className="text-center py-12">
            <div className="space-y-4">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto">
                <FileText className="w-8 h-8 text-gray-400" />
              </div>
              <div>
                <h3 className="text-lg font-medium text-gray-900">No invoices yet</h3>
                <p className="text-gray-600 mt-1">
                  Your invoices will appear here once you have an active subscription
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default InvoiceHistory;
