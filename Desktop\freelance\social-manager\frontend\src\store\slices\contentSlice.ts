import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import { ContentItem } from '../../types/content'

interface ContentState {
  items: ContentItem[]
  currentItem: ContentItem | null
  isLoading: boolean
  error: string | null
  filters: {
    status: string[]
    platforms: string[]
    dateRange: {
      start: string | null
      end: string | null
    }
  }
}

const initialState: ContentState = {
  items: [],
  currentItem: null,
  isLoading: false,
  error: null,
  filters: {
    status: [],
    platforms: [],
    dateRange: {
      start: null,
      end: null,
    },
  },
}

const contentSlice = createSlice({
  name: 'content',
  initialState,
  reducers: {
    setContentItems: (state, action: PayloadAction<ContentItem[]>) => {
      state.items = action.payload
    },
    addContentItem: (state, action: PayloadAction<ContentItem>) => {
      state.items.push(action.payload)
    },
    updateContentItem: (state, action: PayloadAction<ContentItem>) => {
      const index = state.items.findIndex(item => item.id === action.payload.id)
      if (index !== -1) {
        state.items[index] = action.payload
      }
    },
    removeContentItem: (state, action: PayloadAction<string>) => {
      state.items = state.items.filter(item => item.id !== action.payload)
    },
    setCurrentItem: (state, action: PayloadAction<ContentItem | null>) => {
      state.currentItem = action.payload
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload
    },
    setFilters: (state, action: PayloadAction<Partial<ContentState['filters']>>) => {
      state.filters = { ...state.filters, ...action.payload }
    },
    clearFilters: (state) => {
      state.filters = initialState.filters
    },
  },
})

export const {
  setContentItems,
  addContentItem,
  updateContentItem,
  removeContentItem,
  setCurrentItem,
  setLoading,
  setError,
  setFilters,
  clearFilters,
} = contentSlice.actions

export default contentSlice.reducer
