import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { contentAPI } from '../api/content'
import { CreateContentData, UpdateContentData } from '../types/content'
import toast from 'react-hot-toast'

interface ContentFilters {
  projectId?: string
  status?: string
  type?: string
  platforms?: string
}

export const useContentItems = (filters?: ContentFilters) => {
  return useQuery({
    queryKey: ['content', filters],
    queryFn: async () => {
      const response = await contentAPI.getContentItems(filters)
      return response.data.data.contentItems
    },
  })
}

export const useContentItem = (id: string) => {
  return useQuery({
    queryKey: ['content', id],
    queryFn: async () => {
      const response = await contentAPI.getContentItem(id)
      return response.data.data.contentItem
    },
    enabled: !!id,
  })
}

export const useCreateContentItem = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: CreateContentData) => contentAPI.createContentItem(data),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ['content'] })
      queryClient.invalidateQueries({ queryKey: ['projects'] }) // Update project content counts
      toast.success('Content created successfully!')
      return response.data.data.contentItem
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error?.message || 'Failed to create content')
    },
  })
}

export const useUpdateContentItem = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateContentData }) =>
      contentAPI.updateContentItem(id, data),
    onSuccess: (response, variables) => {
      queryClient.invalidateQueries({ queryKey: ['content'] })
      queryClient.invalidateQueries({ queryKey: ['content', variables.id] })
      queryClient.invalidateQueries({ queryKey: ['projects'] })
      toast.success('Content updated successfully!')
      return response.data.data.contentItem
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error?.message || 'Failed to update content')
    },
  })
}

export const useDeleteContentItem = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => contentAPI.deleteContentItem(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['content'] })
      queryClient.invalidateQueries({ queryKey: ['projects'] })
      toast.success('Content deleted successfully!')
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error?.message || 'Failed to delete content')
    },
  })
}

export const useBulkUpdateStatus = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ contentIds, status }: { contentIds: string[]; status: string }) =>
      contentAPI.bulkUpdateStatus(contentIds, status),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ['content'] })
      queryClient.invalidateQueries({ queryKey: ['projects'] })
      toast.success(`${response.data.data.updatedCount} items updated successfully!`)
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error?.message || 'Failed to update content status')
    },
  })
}
