import request from 'supertest';
import express from 'express';
import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';
import projectRoutes from '../src/routes/projects';
import authRoutes from '../src/routes/auth';
import { prisma } from './setup';

const app = express();
app.use(express.json());
app.use('/api/auth', authRoutes);
app.use('/api/projects', projectRoutes);

describe('Projects API E2E Tests', () => {
  let testUser: any;
  let testOrganization: any;
  let authToken: string;

  beforeEach(async () => {
    // Create test organization
    testOrganization = await prisma.organization.create({
      data: {
        name: 'Test Organization',
        slug: 'test-org',
        planType: 'PROFESSIONAL',
        status: 'ACTIVE',
        settings: {}
      }
    });

    // Create test user
    const hashedPassword = await bcrypt.hash('password123', 12);
    testUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: hashedPassword,
        firstName: 'Test',
        lastName: 'User',
        role: 'CLIENT_ADMIN',
        isActive: true,
        emailVerified: true,
        organizationId: testOrganization.id,
        permissions: {}
      }
    });

    // Get auth token
    const loginResponse = await request(app)
      .post('/api/auth/login')
      .send({
        email: '<EMAIL>',
        password: 'password123'
      });
    
    authToken = loginResponse.body.data.token;
  });

  describe('POST /api/projects', () => {
    it('should create a new project', async () => {
      const projectData = {
        name: 'Test Project',
        description: 'A test project for E2E testing',
        type: 'CAMPAIGN',
        budget: 5000,
        deadline: '2024-12-31',
        startDate: '2024-01-01'
      };

      const response = await request(app)
        .post('/api/projects')
        .set('Authorization', `Bearer ${authToken}`)
        .send(projectData);

      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(response.body.data.project.name).toBe(projectData.name);
      expect(response.body.data.project.type).toBe(projectData.type);
      expect(response.body.data.project.budget).toBe(projectData.budget);
      expect(response.body.data.project.organizationId).toBe(testOrganization.id);
      expect(response.body.data.project.createdById).toBe(testUser.id);
    });

    it('should validate required fields', async () => {
      const response = await request(app)
        .post('/api/projects')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          description: 'Missing name field'
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });

    it('should validate project type', async () => {
      const response = await request(app)
        .post('/api/projects')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          name: 'Invalid Type Project',
          type: 'INVALID_TYPE'
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });

    it('should require authentication', async () => {
      const response = await request(app)
        .post('/api/projects')
        .send({
          name: 'Unauthorized Project',
          type: 'CAMPAIGN'
        });

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
    });
  });

  describe('GET /api/projects', () => {
    let testProject: any;

    beforeEach(async () => {
      testProject = await prisma.project.create({
        data: {
          name: 'Test Project',
          description: 'A test project',
          type: 'CAMPAIGN',
          status: 'PLANNING',
          budget: 5000,
          organizationId: testOrganization.id,
          createdById: testUser.id,
          metadata: {}
        }
      });
    });

    it('should list user projects', async () => {
      const response = await request(app)
        .get('/api/projects')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.projects).toHaveLength(1);
      expect(response.body.data.projects[0].name).toBe('Test Project');
      expect(response.body.data.projects[0].organizationId).toBe(testOrganization.id);
    });

    it('should support pagination', async () => {
      // Create multiple projects
      for (let i = 1; i <= 5; i++) {
        await prisma.project.create({
          data: {
            name: `Project ${i}`,
            type: 'CAMPAIGN',
            status: 'PLANNING',
            organizationId: testOrganization.id,
            createdById: testUser.id,
            metadata: {}
          }
        });
      }

      const response = await request(app)
        .get('/api/projects?page=1&limit=3')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.projects).toHaveLength(3);
      expect(response.body.data.pagination.total).toBe(6); // 1 from beforeEach + 5 new
      expect(response.body.data.pagination.page).toBe(1);
      expect(response.body.data.pagination.limit).toBe(3);
    });

    it('should filter by status', async () => {
      await prisma.project.create({
        data: {
          name: 'Completed Project',
          type: 'CAMPAIGN',
          status: 'COMPLETED',
          organizationId: testOrganization.id,
          createdById: testUser.id,
          metadata: {}
        }
      });

      const response = await request(app)
        .get('/api/projects?status=COMPLETED')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.projects).toHaveLength(1);
      expect(response.body.data.projects[0].status).toBe('COMPLETED');
    });

    it('should require authentication', async () => {
      const response = await request(app)
        .get('/api/projects');

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
    });
  });

  describe('GET /api/projects/:id', () => {
    let testProject: any;

    beforeEach(async () => {
      testProject = await prisma.project.create({
        data: {
          name: 'Test Project',
          description: 'A test project',
          type: 'CAMPAIGN',
          status: 'PLANNING',
          budget: 5000,
          organizationId: testOrganization.id,
          createdById: testUser.id,
          metadata: {}
        }
      });
    });

    it('should get project by id', async () => {
      const response = await request(app)
        .get(`/api/projects/${testProject.id}`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.project.id).toBe(testProject.id);
      expect(response.body.data.project.name).toBe('Test Project');
    });

    it('should return 404 for non-existent project', async () => {
      const response = await request(app)
        .get('/api/projects/non-existent-id')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
    });

    it('should require authentication', async () => {
      const response = await request(app)
        .get(`/api/projects/${testProject.id}`);

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
    });
  });

  describe('PUT /api/projects/:id', () => {
    let testProject: any;

    beforeEach(async () => {
      testProject = await prisma.project.create({
        data: {
          name: 'Test Project',
          description: 'A test project',
          type: 'CAMPAIGN',
          status: 'PLANNING',
          budget: 5000,
          organizationId: testOrganization.id,
          createdById: testUser.id,
          metadata: {}
        }
      });
    });

    it('should update project', async () => {
      const updateData = {
        name: 'Updated Project Name',
        description: 'Updated description',
        status: 'IN_PROGRESS',
        budget: 7500
      };

      const response = await request(app)
        .put(`/api/projects/${testProject.id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.project.name).toBe(updateData.name);
      expect(response.body.data.project.description).toBe(updateData.description);
      expect(response.body.data.project.status).toBe(updateData.status);
      expect(response.body.data.project.budget).toBe(updateData.budget);
    });

    it('should return 404 for non-existent project', async () => {
      const response = await request(app)
        .put('/api/projects/non-existent-id')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ name: 'Updated Name' });

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
    });

    it('should require authentication', async () => {
      const response = await request(app)
        .put(`/api/projects/${testProject.id}`)
        .send({ name: 'Updated Name' });

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
    });
  });

  describe('DELETE /api/projects/:id', () => {
    let testProject: any;

    beforeEach(async () => {
      testProject = await prisma.project.create({
        data: {
          name: 'Test Project',
          description: 'A test project',
          type: 'CAMPAIGN',
          status: 'PLANNING',
          budget: 5000,
          organizationId: testOrganization.id,
          createdById: testUser.id,
          metadata: {}
        }
      });
    });

    it('should delete project', async () => {
      const response = await request(app)
        .delete(`/api/projects/${testProject.id}`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('deleted successfully');

      // Verify project is deleted
      const getResponse = await request(app)
        .get(`/api/projects/${testProject.id}`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(getResponse.status).toBe(404);
    });

    it('should return 404 for non-existent project', async () => {
      const response = await request(app)
        .delete('/api/projects/non-existent-id')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
    });

    it('should require authentication', async () => {
      const response = await request(app)
        .delete(`/api/projects/${testProject.id}`);

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
    });
  });
});
