import { useState } from 'react'
import { SocialMediaIntegration } from '../../api/integrations'
import { 
  useUpdateIntegration, 
  useDisconnectIntegration, 
  useRefreshToken, 
  useTestConnection 
} from '../../hooks/useIntegrations'
import { 
  EllipsisVerticalIcon,
  CheckCircleIcon,
  XCircleIcon,
  ArrowPathIcon,
  LinkIcon,
  TrashIcon,
  WifiIcon,
} from '@heroicons/react/24/outline'
import { Menu, Transition } from '@headlessui/react'
import { Fragment } from 'react'
import { format } from 'date-fns'

interface IntegrationCardProps {
  integration: SocialMediaIntegration
}

const IntegrationCard: React.FC<IntegrationCardProps> = ({ integration }) => {
  const [isToggling, setIsToggling] = useState(false)
  
  const updateIntegration = useUpdateIntegration()
  const disconnectIntegration = useDisconnectIntegration()
  const refreshToken = useRefreshToken()
  const testConnection = useTestConnection()

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case 'INSTAGRAM': return '📷'
      case 'FACEBOOK': return '👥'
      case 'TWITTER': return '🐦'
      case 'LINKEDIN': return '💼'
      case 'TIKTOK': return '🎵'
      case 'YOUTUBE': return '📺'
      case 'PINTEREST': return '📌'
      default: return '📱'
    }
  }

  const getPlatformColor = (platform: string) => {
    switch (platform) {
      case 'INSTAGRAM': return 'bg-pink-500'
      case 'FACEBOOK': return 'bg-blue-600'
      case 'TWITTER': return 'bg-sky-500'
      case 'LINKEDIN': return 'bg-blue-700'
      case 'TIKTOK': return 'bg-black'
      case 'YOUTUBE': return 'bg-red-600'
      case 'PINTEREST': return 'bg-red-700'
      default: return 'bg-gray-500'
    }
  }

  const handleToggleActive = async () => {
    setIsToggling(true)
    try {
      await updateIntegration.mutateAsync({
        id: integration.id,
        data: { isActive: !integration.isActive }
      })
    } finally {
      setIsToggling(false)
    }
  }

  const handleDisconnect = async () => {
    if (window.confirm(`Are you sure you want to disconnect ${integration.accountName}?`)) {
      await disconnectIntegration.mutateAsync(integration.id)
    }
  }

  const handleRefreshToken = async () => {
    await refreshToken.mutateAsync(integration.id)
  }

  const handleTestConnection = async () => {
    await testConnection.mutateAsync(integration.id)
  }

  const isExpiringSoon = integration.expiresAt && 
    new Date(integration.expiresAt).getTime() - Date.now() < 7 * 24 * 60 * 60 * 1000 // 7 days

  return (
    <div className={`card ${integration.isActive ? 'ring-2 ring-green-200' : 'opacity-75'}`}>
      <div className="card-content">
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className={`p-2 rounded-lg ${getPlatformColor(integration.platform)} text-white`}>
              <span className="text-lg">{getPlatformIcon(integration.platform)}</span>
            </div>
            <div>
              <h3 className="font-medium text-gray-900">{integration.accountName}</h3>
              <p className="text-sm text-gray-600">{integration.platform}</p>
            </div>
          </div>
          
          <Menu as="div" className="relative">
            <Menu.Button className="p-1 rounded-md hover:bg-gray-100">
              <EllipsisVerticalIcon className="h-5 w-5 text-gray-400" />
            </Menu.Button>
            <Transition
              as={Fragment}
              enter="transition ease-out duration-100"
              enterFrom="transform opacity-0 scale-95"
              enterTo="transform opacity-100 scale-100"
              leave="transition ease-in duration-75"
              leaveFrom="transform opacity-100 scale-100"
              leaveTo="transform opacity-0 scale-95"
            >
              <Menu.Items className="absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                <Menu.Item>
                  {({ active }) => (
                    <button
                      onClick={handleTestConnection}
                      disabled={testConnection.isPending}
                      className={`flex items-center w-full text-left px-4 py-2 text-sm ${
                        active ? 'bg-gray-100 text-gray-900' : 'text-gray-700'
                      }`}
                    >
                      <WifiIcon className="h-4 w-4 mr-2" />
                      Test Connection
                    </button>
                  )}
                </Menu.Item>
                <Menu.Item>
                  {({ active }) => (
                    <button
                      onClick={handleRefreshToken}
                      disabled={refreshToken.isPending}
                      className={`flex items-center w-full text-left px-4 py-2 text-sm ${
                        active ? 'bg-gray-100 text-gray-900' : 'text-gray-700'
                      }`}
                    >
                      <ArrowPathIcon className="h-4 w-4 mr-2" />
                      Refresh Token
                    </button>
                  )}
                </Menu.Item>
                <Menu.Item>
                  {({ active }) => (
                    <button
                      onClick={handleDisconnect}
                      disabled={disconnectIntegration.isPending}
                      className={`flex items-center w-full text-left px-4 py-2 text-sm ${
                        active ? 'bg-gray-100 text-red-900' : 'text-red-700'
                      }`}
                    >
                      <TrashIcon className="h-4 w-4 mr-2" />
                      Disconnect
                    </button>
                  )}
                </Menu.Item>
              </Menu.Items>
            </Transition>
          </Menu>
        </div>

        {/* Status */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            {integration.isActive ? (
              <CheckCircleIcon className="h-5 w-5 text-green-500" />
            ) : (
              <XCircleIcon className="h-5 w-5 text-red-500" />
            )}
            <span className={`text-sm font-medium ${
              integration.isActive ? 'text-green-700' : 'text-red-700'
            }`}>
              {integration.isActive ? 'Active' : 'Inactive'}
            </span>
          </div>
          
          <button
            onClick={handleToggleActive}
            disabled={isToggling || updateIntegration.isPending}
            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
              integration.isActive ? 'bg-green-600' : 'bg-gray-200'
            }`}
          >
            <span
              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                integration.isActive ? 'translate-x-6' : 'translate-x-1'
              }`}
            />
          </button>
        </div>

        {/* Expiration Warning */}
        {isExpiringSoon && (
          <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-center space-x-2">
              <ArrowPathIcon className="h-4 w-4 text-yellow-600" />
              <span className="text-sm text-yellow-800">
                Token expires {format(new Date(integration.expiresAt!), 'MMM d, yyyy')}
              </span>
            </div>
          </div>
        )}

        {/* Connection Info */}
        <div className="space-y-2 text-sm text-gray-600">
          <div className="flex items-center justify-between">
            <span>Connected:</span>
            <span>{format(new Date(integration.createdAt), 'MMM d, yyyy')}</span>
          </div>
          {integration.lastTestedAt && (
            <div className="flex items-center justify-between">
              <span>Last tested:</span>
              <span>{format(new Date(integration.lastTestedAt), 'MMM d, yyyy')}</span>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default IntegrationCard
