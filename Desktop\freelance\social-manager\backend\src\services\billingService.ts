import Stripe from 'stripe';
import { PrismaClient } from '@prisma/client';
import { logger } from '../utils/logger.js';

export interface SubscriptionPlan {
  id: string;
  name: string;
  description: string;
  price: number;
  currency: string;
  interval: 'month' | 'year';
  features: PlanFeature[];
  limits: PlanLimits;
  stripePriceId: string;
  isActive: boolean;
}

export interface PlanFeature {
  name: string;
  description: string;
  included: boolean;
  limit?: number;
}

export interface PlanLimits {
  users: number;
  socialAccounts: number;
  postsPerMonth: number;
  aiGenerations: number;
  analyticsRetention: number; // days
  customBranding: boolean;
  apiAccess: boolean;
  prioritySupport: boolean;
}

export interface UsageMetrics {
  organizationId: string;
  period: string; // YYYY-MM format
  users: number;
  socialAccounts: number;
  postsPublished: number;
  aiGenerations: number;
  apiCalls: number;
  storageUsed: number; // bytes
}

export interface Invoice {
  id: string;
  organizationId: string;
  stripeInvoiceId: string;
  amount: number;
  currency: string;
  status: 'draft' | 'open' | 'paid' | 'void' | 'uncollectible';
  dueDate: Date;
  paidAt?: Date;
  items: InvoiceItem[];
  downloadUrl?: string;
}

export interface InvoiceItem {
  description: string;
  quantity: number;
  unitPrice: number;
  amount: number;
}

export interface PaymentMethod {
  id: string;
  type: 'card' | 'bank_account';
  last4: string;
  brand?: string;
  expiryMonth?: number;
  expiryYear?: number;
  isDefault: boolean;
}

export class BillingService {
  private stripe: Stripe;
  private prisma: PrismaClient;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
    this.stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
      apiVersion: '2023-10-16'
    });
  }

  /**
   * Get all available subscription plans
   */
  async getSubscriptionPlans(): Promise<SubscriptionPlan[]> {
    try {
      const plans = await this.prisma.subscriptionPlan.findMany({
        where: { isActive: true },
        orderBy: { price: 'asc' }
      });

      return plans.map(plan => ({
        id: plan.id,
        name: plan.name,
        description: plan.description,
        price: plan.price,
        currency: plan.currency,
        interval: plan.interval as 'month' | 'year',
        features: JSON.parse(plan.features || '[]'),
        limits: JSON.parse(plan.limits || '{}'),
        stripePriceId: plan.stripePriceId,
        isActive: plan.isActive
      }));
    } catch (error) {
      logger.error('Error getting subscription plans:', error);
      throw new Error('Failed to get subscription plans');
    }
  }

  /**
   * Create subscription for organization
   */
  async createSubscription(
    organizationId: string,
    planId: string,
    paymentMethodId: string
  ): Promise<{ subscriptionId: string; clientSecret?: string }> {
    try {
      // Get organization and plan details
      const organization = await this.prisma.organization.findUnique({
        where: { id: organizationId }
      });

      const plan = await this.prisma.subscriptionPlan.findUnique({
        where: { id: planId }
      });

      if (!organization || !plan) {
        throw new Error('Organization or plan not found');
      }

      // Create or get Stripe customer
      let stripeCustomerId = organization.stripeCustomerId;
      if (!stripeCustomerId) {
        const customer = await this.stripe.customers.create({
          email: organization.contactEmail,
          name: organization.name,
          metadata: {
            organizationId: organization.id
          }
        });
        stripeCustomerId = customer.id;

        // Update organization with Stripe customer ID
        await this.prisma.organization.update({
          where: { id: organizationId },
          data: { stripeCustomerId }
        });
      }

      // Attach payment method to customer
      await this.stripe.paymentMethods.attach(paymentMethodId, {
        customer: stripeCustomerId
      });

      // Set as default payment method
      await this.stripe.customers.update(stripeCustomerId, {
        invoice_settings: {
          default_payment_method: paymentMethodId
        }
      });

      // Create subscription
      const subscription = await this.stripe.subscriptions.create({
        customer: stripeCustomerId,
        items: [{
          price: plan.stripePriceId
        }],
        payment_behavior: 'default_incomplete',
        payment_settings: {
          save_default_payment_method: 'on_subscription'
        },
        expand: ['latest_invoice.payment_intent']
      });

      // Save subscription to database
      await this.prisma.subscription.create({
        data: {
          organizationId,
          planId,
          stripeSubscriptionId: subscription.id,
          status: subscription.status,
          currentPeriodStart: new Date(subscription.current_period_start * 1000),
          currentPeriodEnd: new Date(subscription.current_period_end * 1000),
          cancelAtPeriodEnd: subscription.cancel_at_period_end
        }
      });

      const invoice = subscription.latest_invoice as Stripe.Invoice;
      const paymentIntent = invoice.payment_intent as Stripe.PaymentIntent;

      logger.info(`Created subscription ${subscription.id} for organization ${organizationId}`);

      return {
        subscriptionId: subscription.id,
        clientSecret: paymentIntent?.client_secret
      };
    } catch (error) {
      logger.error('Error creating subscription:', error);
      throw new Error('Failed to create subscription');
    }
  }

  /**
   * Update subscription plan
   */
  async updateSubscription(organizationId: string, newPlanId: string): Promise<void> {
    try {
      const subscription = await this.prisma.subscription.findFirst({
        where: {
          organizationId,
          status: { in: ['active', 'trialing'] }
        }
      });

      if (!subscription) {
        throw new Error('No active subscription found');
      }

      const newPlan = await this.prisma.subscriptionPlan.findUnique({
        where: { id: newPlanId }
      });

      if (!newPlan) {
        throw new Error('Plan not found');
      }

      // Update Stripe subscription
      const stripeSubscription = await this.stripe.subscriptions.retrieve(
        subscription.stripeSubscriptionId
      );

      await this.stripe.subscriptions.update(subscription.stripeSubscriptionId, {
        items: [{
          id: stripeSubscription.items.data[0].id,
          price: newPlan.stripePriceId
        }],
        proration_behavior: 'create_prorations'
      });

      // Update database
      await this.prisma.subscription.update({
        where: { id: subscription.id },
        data: { planId: newPlanId }
      });

      logger.info(`Updated subscription ${subscription.id} to plan ${newPlanId}`);
    } catch (error) {
      logger.error('Error updating subscription:', error);
      throw new Error('Failed to update subscription');
    }
  }

  /**
   * Cancel subscription
   */
  async cancelSubscription(organizationId: string, immediate: boolean = false): Promise<void> {
    try {
      const subscription = await this.prisma.subscription.findFirst({
        where: {
          organizationId,
          status: { in: ['active', 'trialing'] }
        }
      });

      if (!subscription) {
        throw new Error('No active subscription found');
      }

      if (immediate) {
        // Cancel immediately
        await this.stripe.subscriptions.cancel(subscription.stripeSubscriptionId);
        
        await this.prisma.subscription.update({
          where: { id: subscription.id },
          data: { status: 'canceled' }
        });
      } else {
        // Cancel at period end
        await this.stripe.subscriptions.update(subscription.stripeSubscriptionId, {
          cancel_at_period_end: true
        });

        await this.prisma.subscription.update({
          where: { id: subscription.id },
          data: { cancelAtPeriodEnd: true }
        });
      }

      logger.info(`Cancelled subscription ${subscription.id} for organization ${organizationId}`);
    } catch (error) {
      logger.error('Error cancelling subscription:', error);
      throw new Error('Failed to cancel subscription');
    }
  }

  /**
   * Track usage for billing
   */
  async trackUsage(organizationId: string, metric: string, quantity: number = 1): Promise<void> {
    try {
      const currentPeriod = new Date().toISOString().slice(0, 7); // YYYY-MM

      await this.prisma.usageRecord.upsert({
        where: {
          organizationId_period_metric: {
            organizationId,
            period: currentPeriod,
            metric
          }
        },
        update: {
          quantity: { increment: quantity }
        },
        create: {
          organizationId,
          period: currentPeriod,
          metric,
          quantity
        }
      });

      // Check usage limits
      await this.checkUsageLimits(organizationId);
    } catch (error) {
      logger.error('Error tracking usage:', error);
    }
  }

  /**
   * Get usage metrics for organization
   */
  async getUsageMetrics(organizationId: string, period?: string): Promise<UsageMetrics> {
    try {
      const targetPeriod = period || new Date().toISOString().slice(0, 7);

      const usageRecords = await this.prisma.usageRecord.findMany({
        where: {
          organizationId,
          period: targetPeriod
        }
      });

      const metrics: UsageMetrics = {
        organizationId,
        period: targetPeriod,
        users: 0,
        socialAccounts: 0,
        postsPublished: 0,
        aiGenerations: 0,
        apiCalls: 0,
        storageUsed: 0
      };

      for (const record of usageRecords) {
        switch (record.metric) {
          case 'users':
            metrics.users = record.quantity;
            break;
          case 'social_accounts':
            metrics.socialAccounts = record.quantity;
            break;
          case 'posts_published':
            metrics.postsPublished = record.quantity;
            break;
          case 'ai_generations':
            metrics.aiGenerations = record.quantity;
            break;
          case 'api_calls':
            metrics.apiCalls = record.quantity;
            break;
          case 'storage_used':
            metrics.storageUsed = record.quantity;
            break;
        }
      }

      return metrics;
    } catch (error) {
      logger.error('Error getting usage metrics:', error);
      throw new Error('Failed to get usage metrics');
    }
  }

  /**
   * Get invoices for organization
   */
  async getInvoices(organizationId: string, limit: number = 10): Promise<Invoice[]> {
    try {
      const organization = await this.prisma.organization.findUnique({
        where: { id: organizationId }
      });

      if (!organization?.stripeCustomerId) {
        return [];
      }

      const stripeInvoices = await this.stripe.invoices.list({
        customer: organization.stripeCustomerId,
        limit
      });

      return stripeInvoices.data.map(invoice => ({
        id: invoice.id,
        organizationId,
        stripeInvoiceId: invoice.id,
        amount: invoice.amount_paid / 100, // Convert from cents
        currency: invoice.currency,
        status: invoice.status as any,
        dueDate: new Date(invoice.due_date! * 1000),
        paidAt: invoice.status_transitions.paid_at ?
          new Date(invoice.status_transitions.paid_at * 1000) : undefined,
        items: invoice.lines.data.map(line => ({
          description: line.description || '',
          quantity: line.quantity || 1,
          unitPrice: (line.price?.unit_amount || 0) / 100,
          amount: (line.amount || 0) / 100
        })),
        downloadUrl: invoice.invoice_pdf
      }));
    } catch (error) {
      logger.error('Error getting invoices:', error);
      throw new Error('Failed to get invoices');
    }
  }

  /**
   * Get payment methods for organization
   */
  async getPaymentMethods(organizationId: string): Promise<PaymentMethod[]> {
    try {
      const organization = await this.prisma.organization.findUnique({
        where: { id: organizationId }
      });

      if (!organization?.stripeCustomerId) {
        return [];
      }

      const paymentMethods = await this.stripe.paymentMethods.list({
        customer: organization.stripeCustomerId,
        type: 'card'
      });

      const customer = await this.stripe.customers.retrieve(organization.stripeCustomerId);
      const defaultPaymentMethodId = (customer as Stripe.Customer).invoice_settings.default_payment_method;

      return paymentMethods.data.map(pm => ({
        id: pm.id,
        type: pm.type as 'card' | 'bank_account',
        last4: pm.card?.last4 || '',
        brand: pm.card?.brand,
        expiryMonth: pm.card?.exp_month,
        expiryYear: pm.card?.exp_year,
        isDefault: pm.id === defaultPaymentMethodId
      }));
    } catch (error) {
      logger.error('Error getting payment methods:', error);
      throw new Error('Failed to get payment methods');
    }
  }

  /**
   * Add payment method
   */
  async addPaymentMethod(organizationId: string, paymentMethodId: string): Promise<void> {
    try {
      const organization = await this.prisma.organization.findUnique({
        where: { id: organizationId }
      });

      if (!organization?.stripeCustomerId) {
        throw new Error('No Stripe customer found');
      }

      await this.stripe.paymentMethods.attach(paymentMethodId, {
        customer: organization.stripeCustomerId
      });

      logger.info(`Added payment method ${paymentMethodId} for organization ${organizationId}`);
    } catch (error) {
      logger.error('Error adding payment method:', error);
      throw new Error('Failed to add payment method');
    }
  }

  /**
   * Set default payment method
   */
  async setDefaultPaymentMethod(organizationId: string, paymentMethodId: string): Promise<void> {
    try {
      const organization = await this.prisma.organization.findUnique({
        where: { id: organizationId }
      });

      if (!organization?.stripeCustomerId) {
        throw new Error('No Stripe customer found');
      }

      await this.stripe.customers.update(organization.stripeCustomerId, {
        invoice_settings: {
          default_payment_method: paymentMethodId
        }
      });

      logger.info(`Set default payment method ${paymentMethodId} for organization ${organizationId}`);
    } catch (error) {
      logger.error('Error setting default payment method:', error);
      throw new Error('Failed to set default payment method');
    }
  }

  /**
   * Remove payment method
   */
  async removePaymentMethod(paymentMethodId: string): Promise<void> {
    try {
      await this.stripe.paymentMethods.detach(paymentMethodId);
      logger.info(`Removed payment method ${paymentMethodId}`);
    } catch (error) {
      logger.error('Error removing payment method:', error);
      throw new Error('Failed to remove payment method');
    }
  }

  /**
   * Handle Stripe webhooks
   */
  async handleWebhook(event: Stripe.Event): Promise<void> {
    try {
      switch (event.type) {
        case 'invoice.payment_succeeded':
          await this.handleInvoicePaymentSucceeded(event.data.object as Stripe.Invoice);
          break;
        case 'invoice.payment_failed':
          await this.handleInvoicePaymentFailed(event.data.object as Stripe.Invoice);
          break;
        case 'customer.subscription.updated':
          await this.handleSubscriptionUpdated(event.data.object as Stripe.Subscription);
          break;
        case 'customer.subscription.deleted':
          await this.handleSubscriptionDeleted(event.data.object as Stripe.Subscription);
          break;
        default:
          logger.info(`Unhandled webhook event type: ${event.type}`);
      }
    } catch (error) {
      logger.error('Error handling webhook:', error);
      throw error;
    }
  }

  /**
   * Check usage limits for organization
   */
  private async checkUsageLimits(organizationId: string): Promise<void> {
    try {
      const subscription = await this.prisma.subscription.findFirst({
        where: {
          organizationId,
          status: { in: ['active', 'trialing'] }
        },
        include: { plan: true }
      });

      if (!subscription) {
        return;
      }

      const limits = JSON.parse(subscription.plan.limits || '{}') as PlanLimits;
      const usage = await this.getUsageMetrics(organizationId);

      // Check limits and send notifications if exceeded
      const warnings: string[] = [];

      if (usage.postsPublished >= limits.postsPerMonth * 0.9) {
        warnings.push(`Approaching post limit: ${usage.postsPublished}/${limits.postsPerMonth}`);
      }

      if (usage.aiGenerations >= limits.aiGenerations * 0.9) {
        warnings.push(`Approaching AI generation limit: ${usage.aiGenerations}/${limits.aiGenerations}`);
      }

      if (warnings.length > 0) {
        // Send usage warning notification
        logger.warn(`Usage warnings for organization ${organizationId}:`, warnings);
        // TODO: Send email notification
      }
    } catch (error) {
      logger.error('Error checking usage limits:', error);
    }
  }

  /**
   * Webhook handlers
   */
  private async handleInvoicePaymentSucceeded(invoice: Stripe.Invoice): Promise<void> {
    const organizationId = await this.getOrganizationIdFromCustomer(invoice.customer as string);
    if (organizationId) {
      logger.info(`Payment succeeded for organization ${organizationId}, invoice ${invoice.id}`);
      // Update subscription status, send confirmation email, etc.
    }
  }

  private async handleInvoicePaymentFailed(invoice: Stripe.Invoice): Promise<void> {
    const organizationId = await this.getOrganizationIdFromCustomer(invoice.customer as string);
    if (organizationId) {
      logger.warn(`Payment failed for organization ${organizationId}, invoice ${invoice.id}`);
      // Send payment failure notification, retry logic, etc.
    }
  }

  private async handleSubscriptionUpdated(subscription: Stripe.Subscription): Promise<void> {
    const organizationId = await this.getOrganizationIdFromCustomer(subscription.customer as string);
    if (organizationId) {
      await this.prisma.subscription.updateMany({
        where: {
          organizationId,
          stripeSubscriptionId: subscription.id
        },
        data: {
          status: subscription.status,
          currentPeriodStart: new Date(subscription.current_period_start * 1000),
          currentPeriodEnd: new Date(subscription.current_period_end * 1000),
          cancelAtPeriodEnd: subscription.cancel_at_period_end
        }
      });
      logger.info(`Updated subscription ${subscription.id} for organization ${organizationId}`);
    }
  }

  private async handleSubscriptionDeleted(subscription: Stripe.Subscription): Promise<void> {
    const organizationId = await this.getOrganizationIdFromCustomer(subscription.customer as string);
    if (organizationId) {
      await this.prisma.subscription.updateMany({
        where: {
          organizationId,
          stripeSubscriptionId: subscription.id
        },
        data: { status: 'canceled' }
      });
      logger.info(`Deleted subscription ${subscription.id} for organization ${organizationId}`);
    }
  }

  private async getOrganizationIdFromCustomer(customerId: string): Promise<string | null> {
    const organization = await this.prisma.organization.findFirst({
      where: { stripeCustomerId: customerId }
    });
    return organization?.id || null;
  }

  /**
   * Get current subscription for organization
   */
  async getCurrentSubscription(organizationId: string): Promise<{
    subscription: any;
    plan: SubscriptionPlan;
    usage: UsageMetrics;
  } | null> {
    try {
      const subscription = await this.prisma.subscription.findFirst({
        where: {
          organizationId,
          status: { in: ['active', 'trialing'] }
        },
        include: { plan: true }
      });

      if (!subscription) {
        return null;
      }

      const plan: SubscriptionPlan = {
        id: subscription.plan.id,
        name: subscription.plan.name,
        description: subscription.plan.description,
        price: subscription.plan.price,
        currency: subscription.plan.currency,
        interval: subscription.plan.interval as 'month' | 'year',
        features: JSON.parse(subscription.plan.features || '[]'),
        limits: JSON.parse(subscription.plan.limits || '{}'),
        stripePriceId: subscription.plan.stripePriceId,
        isActive: subscription.plan.isActive
      };

      const usage = await this.getUsageMetrics(organizationId);

      return {
        subscription: {
          id: subscription.id,
          status: subscription.status,
          currentPeriodStart: subscription.currentPeriodStart,
          currentPeriodEnd: subscription.currentPeriodEnd,
          cancelAtPeriodEnd: subscription.cancelAtPeriodEnd
        },
        plan,
        usage
      };
    } catch (error) {
      logger.error('Error getting current subscription:', error);
      throw new Error('Failed to get current subscription');
    }
  }
}
