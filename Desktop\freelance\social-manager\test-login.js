const axios = require('axios');

async function testLogin() {
  try {
    console.log('Testing login endpoint...');
    
    const response = await axios.post('http://localhost:5000/api/auth/login', {
      email: '<EMAIL>',
      password: 'password123'
    });
    
    console.log('Login successful!');
    console.log('Response:', response.data);
    
    if (response.data.success && response.data.data.token) {
      console.log('✅ Token received:', response.data.data.token.substring(0, 20) + '...');
      console.log('✅ User:', response.data.data.user.firstName, response.data.data.user.lastName);
    }
    
  } catch (error) {
    console.error('❌ Login failed:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    } else {
      console.error('Error:', error.message);
    }
  }
}

testLogin();
