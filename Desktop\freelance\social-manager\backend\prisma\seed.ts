import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Starting database seed...')

  // Create organizations
  const organization1 = await prisma.organization.create({
    data: {
      name: 'Digital Marketing Agency',
      slug: 'digital-marketing-agency',
      planType: 'AGENCY',
      status: 'ACTIVE',
    },
  })

  const organization2 = await prisma.organization.create({
    data: {
      name: 'Startup Inc',
      slug: 'startup-inc',
      planType: 'PROFESSIONAL',
      status: 'ACTIVE',
    },
  })

  // Create users
  const hashedPassword = await bcrypt.hash('password123', 12)

  const superAdmin = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password: hashedPassword,
      firstName: 'Super',
      lastName: 'Admin',
      role: 'SUPER_ADMIN',
      emailVerified: true,
      organizationId: organization1.id,
    },
  })

  const agencyAdmin = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password: hashedPassword,
      firstName: 'Agency',
      lastName: 'Admin',
      role: 'AGENCY_ADMIN',
      emailVerified: true,
      organizationId: organization1.id,
    },
  })

  const clientAdmin = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password: hashedPassword,
      firstName: 'Client',
      lastName: 'Admin',
      role: 'CLIENT_ADMIN',
      emailVerified: true,
      organizationId: organization2.id,
    },
  })

  const teamMember = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password: hashedPassword,
      firstName: 'Team',
      lastName: 'Member',
      role: 'TEAM_MEMBER',
      emailVerified: true,
      organizationId: organization1.id,
    },
  })

  // Create projects
  const project1 = await prisma.project.create({
    data: {
      name: 'Summer Campaign 2024',
      description: 'A comprehensive summer marketing campaign across all social platforms',
      type: 'CAMPAIGN',
      status: 'IN_PROGRESS',
      budget: 15000,
      deadline: new Date('2024-08-31'),
      startDate: new Date('2024-06-01'),
      organizationId: organization1.id,
      createdById: agencyAdmin.id,
    },
  })

  const project2 = await prisma.project.create({
    data: {
      name: 'Product Launch',
      description: 'Launch campaign for our new product line',
      type: 'CAMPAIGN',
      status: 'PLANNING',
      budget: 25000,
      deadline: new Date('2024-09-15'),
      startDate: new Date('2024-07-01'),
      organizationId: organization1.id,
      createdById: agencyAdmin.id,
    },
  })

  const project3 = await prisma.project.create({
    data: {
      name: 'Brand Awareness',
      description: 'Ongoing brand awareness and engagement campaign',
      type: 'ONGOING',
      status: 'IN_PROGRESS',
      organizationId: organization2.id,
      createdById: clientAdmin.id,
    },
  })

  // Create project members
  await prisma.projectMember.create({
    data: {
      projectId: project1.id,
      userId: agencyAdmin.id,
      role: 'OWNER',
    },
  })

  await prisma.projectMember.create({
    data: {
      projectId: project1.id,
      userId: teamMember.id,
      role: 'MEMBER',
    },
  })

  await prisma.projectMember.create({
    data: {
      projectId: project2.id,
      userId: agencyAdmin.id,
      role: 'OWNER',
    },
  })

  await prisma.projectMember.create({
    data: {
      projectId: project3.id,
      userId: clientAdmin.id,
      role: 'OWNER',
    },
  })

  // Create content items
  await prisma.contentItem.create({
    data: {
      title: 'Summer Vibes Instagram Post',
      description: 'Bright and colorful summer-themed post for Instagram',
      type: 'POST',
      platforms: JSON.stringify(['INSTAGRAM', 'FACEBOOK']),
      contentData: JSON.stringify({
        text: 'Summer is here! ☀️ Check out our amazing summer collection. #Summer2024 #Fashion',
        imageUrl: '/assets/summer-post.jpg',
        hashtags: ['#Summer2024', '#Fashion', '#Style'],
      }),
      status: 'APPROVED',
      scheduledAt: new Date('2024-06-15T10:00:00Z'),
      projectId: project1.id,
      createdById: teamMember.id,
    },
  })

  await prisma.contentItem.create({
    data: {
      title: 'Product Teaser Video',
      description: 'Short teaser video for the upcoming product launch',
      type: 'VIDEO',
      platforms: JSON.stringify(['INSTAGRAM', 'TIKTOK', 'YOUTUBE']),
      contentData: JSON.stringify({
        text: 'Something amazing is coming... 🚀 Stay tuned! #ComingSoon #Innovation',
        videoUrl: '/assets/teaser-video.mp4',
        duration: 30,
        hashtags: ['#ComingSoon', '#Innovation', '#TechLaunch'],
      }),
      status: 'REVIEW',
      projectId: project2.id,
      createdById: teamMember.id,
    },
  })

  await prisma.contentItem.create({
    data: {
      title: 'Behind the Scenes Story',
      description: 'Instagram story showing behind the scenes content',
      type: 'STORY',
      platforms: JSON.stringify(['INSTAGRAM']),
      contentData: JSON.stringify({
        text: 'Behind the scenes at our studio today! 📸',
        imageUrl: '/assets/bts-story.jpg',
        stickers: ['location', 'music'],
      }),
      status: 'PUBLISHED',
      publishedAt: new Date('2024-06-01T14:30:00Z'),
      projectId: project3.id,
      createdById: clientAdmin.id,
    },
  })

  console.log('✅ Database seeded successfully!')
  console.log('\n📊 Created:')
  console.log(`- ${await prisma.organization.count()} organizations`)
  console.log(`- ${await prisma.user.count()} users`)
  console.log(`- ${await prisma.project.count()} projects`)
  console.log(`- ${await prisma.projectMember.count()} project members`)
  console.log(`- ${await prisma.contentItem.count()} content items`)
  
  console.log('\n👤 Test Users:')
  console.log('- <EMAIL> (Super Admin) - password: password123')
  console.log('- <EMAIL> (Agency Admin) - password: password123')
  console.log('- <EMAIL> (Client Admin) - password: password123')
  console.log('- <EMAIL> (Team Member) - password: password123')
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
