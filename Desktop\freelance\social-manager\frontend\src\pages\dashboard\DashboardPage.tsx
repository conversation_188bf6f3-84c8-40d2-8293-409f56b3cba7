import { useSelector } from 'react-redux'
import { RootState } from '../../store/store'
import {
  ChartBarIcon,
  FolderIcon,
  DocumentTextIcon,
  UsersIcon,
} from '@heroicons/react/24/outline'

const stats = [
  { name: 'Total Projects', value: '12', icon: FolderIcon, change: '+4.75%', changeType: 'positive' },
  { name: 'Content Items', value: '156', icon: DocumentTextIcon, change: '+12.5%', changeType: 'positive' },
  { name: 'Team Members', value: '8', icon: UsersIcon, change: '+2', changeType: 'positive' },
  { name: 'Engagement Rate', value: '4.2%', icon: ChartBarIcon, change: '-0.3%', changeType: 'negative' },
]

const recentProjects = [
  { id: 1, name: 'Summer Campaign 2024', status: 'In Progress', progress: 75 },
  { id: 2, name: 'Product Launch', status: 'Review', progress: 90 },
  { id: 3, name: 'Brand Awareness', status: 'Planning', progress: 25 },
]

const DashboardPage: React.FC = () => {
  const { user } = useSelector((state: RootState) => state.auth)

  return (
    <div className="space-y-6">
      {/* Welcome header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">
          Welcome back, {user?.firstName}!
        </h1>
        <p className="mt-1 text-sm text-gray-600">
          Here's what's happening with your social media campaigns today.
        </p>
      </div>

      {/* Stats grid */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        {stats.map((item) => (
          <div key={item.name} className="card">
            <div className="card-content">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <item.icon className="h-6 w-6 text-gray-400" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">{item.name}</dt>
                    <dd className="flex items-baseline">
                      <div className="text-2xl font-semibold text-gray-900">{item.value}</div>
                      <div
                        className={`ml-2 flex items-baseline text-sm font-semibold ${
                          item.changeType === 'positive' ? 'text-green-600' : 'text-red-600'
                        }`}
                      >
                        {item.change}
                      </div>
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        {/* Recent Projects */}
        <div className="card">
          <div className="card-header">
            <h3 className="card-title">Recent Projects</h3>
            <p className="card-description">Your latest social media campaigns</p>
          </div>
          <div className="card-content">
            <div className="space-y-4">
              {recentProjects.map((project) => (
                <div key={project.id} className="flex items-center justify-between">
                  <div className="flex-1">
                    <h4 className="text-sm font-medium text-gray-900">{project.name}</h4>
                    <p className="text-sm text-gray-500">{project.status}</p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-16 bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-primary-600 h-2 rounded-full"
                        style={{ width: `${project.progress}%` }}
                      />
                    </div>
                    <span className="text-sm text-gray-500">{project.progress}%</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="card">
          <div className="card-header">
            <h3 className="card-title">Quick Actions</h3>
            <p className="card-description">Get started with common tasks</p>
          </div>
          <div className="card-content">
            <div className="grid grid-cols-2 gap-4">
              <button className="btn-outline p-4 h-auto flex flex-col items-center space-y-2">
                <FolderIcon className="h-8 w-8 text-primary-600" />
                <span className="text-sm font-medium">New Project</span>
              </button>
              <button className="btn-outline p-4 h-auto flex flex-col items-center space-y-2">
                <DocumentTextIcon className="h-8 w-8 text-primary-600" />
                <span className="text-sm font-medium">Create Content</span>
              </button>
              <button className="btn-outline p-4 h-auto flex flex-col items-center space-y-2">
                <ChartBarIcon className="h-8 w-8 text-primary-600" />
                <span className="text-sm font-medium">View Analytics</span>
              </button>
              <button className="btn-outline p-4 h-auto flex flex-col items-center space-y-2">
                <UsersIcon className="h-8 w-8 text-primary-600" />
                <span className="text-sm font-medium">Invite Team</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Activity Feed */}
      <div className="card">
        <div className="card-header">
          <h3 className="card-title">Recent Activity</h3>
          <p className="card-description">Latest updates from your team</p>
        </div>
        <div className="card-content">
          <div className="space-y-4">
            <div className="flex items-start space-x-3">
              <div className="h-8 w-8 bg-green-100 rounded-full flex items-center justify-center">
                <DocumentTextIcon className="h-4 w-4 text-green-600" />
              </div>
              <div className="flex-1">
                <p className="text-sm text-gray-900">
                  <span className="font-medium">Sarah Johnson</span> published a new post to Instagram
                </p>
                <p className="text-xs text-gray-500">2 hours ago</p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <div className="h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center">
                <FolderIcon className="h-4 w-4 text-blue-600" />
              </div>
              <div className="flex-1">
                <p className="text-sm text-gray-900">
                  <span className="font-medium">Mike Chen</span> created a new project "Holiday Campaign"
                </p>
                <p className="text-xs text-gray-500">4 hours ago</p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <div className="h-8 w-8 bg-purple-100 rounded-full flex items-center justify-center">
                <UsersIcon className="h-4 w-4 text-purple-600" />
              </div>
              <div className="flex-1">
                <p className="text-sm text-gray-900">
                  <span className="font-medium">Alex Rivera</span> joined the team
                </p>
                <p className="text-xs text-gray-500">1 day ago</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default DashboardPage
