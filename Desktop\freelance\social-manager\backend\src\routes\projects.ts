import { Router } from 'express';
import { body, validationResult } from 'express-validator';
import { prisma } from '../config/database.js';
import { asyncHandler, CustomError } from '../middleware/errorHandler.js';
import { AuthenticatedRequest } from '../middleware/auth.js';

const router = Router();

// Get all projects for the user's organization
router.get('/', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const user = req.user!;

  const projects = await prisma.project.findMany({
    where: {
      organizationId: user.organizationId,
    },
    include: {
      createdBy: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          avatar: true,
        },
      },
      members: {
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              avatar: true,
            },
          },
        },
      },
      contentItems: {
        select: {
          id: true,
          title: true,
          status: true,
        },
      },
      _count: {
        select: {
          contentItems: true,
          members: true,
        },
      },
    },
    orderBy: {
      updatedAt: 'desc',
    },
  });

  res.json({
    success: true,
    data: { projects },
  });
}));

// Get a specific project
router.get('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const user = req.user!;
  const { id } = req.params;

  const project = await prisma.project.findFirst({
    where: {
      id,
      organizationId: user.organizationId,
    },
    include: {
      createdBy: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          avatar: true,
        },
      },
      members: {
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              avatar: true,
            },
          },
        },
      },
      contentItems: {
        include: {
          createdBy: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              avatar: true,
            },
          },
        },
        orderBy: {
          updatedAt: 'desc',
        },
      },
    },
  });

  if (!project) {
    throw new CustomError('Project not found', 404);
  }

  res.json({
    success: true,
    data: { project },
  });
}));

// Create a new project
router.post('/', [
  body('name').trim().isLength({ min: 1 }).withMessage('Project name is required'),
  body('description').optional().trim(),
  body('type').isIn(['CAMPAIGN', 'ONGOING', 'ONE_TIME']).withMessage('Invalid project type'),
  body('budget').optional().isFloat({ min: 0 }).withMessage('Budget must be a positive number'),
  body('deadline').optional().isISO8601().withMessage('Invalid deadline date'),
  body('startDate').optional().isISO8601().withMessage('Invalid start date'),
  body('endDate').optional().isISO8601().withMessage('Invalid end date'),
], asyncHandler(async (req: AuthenticatedRequest, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new CustomError('Validation failed', 400);
  }

  const user = req.user!;
  const { name, description, type, budget, deadline, startDate, endDate } = req.body;

  const project = await prisma.project.create({
    data: {
      name,
      description,
      type,
      budget: budget ? parseFloat(budget) : null,
      deadline: deadline ? new Date(deadline) : null,
      startDate: startDate ? new Date(startDate) : null,
      endDate: endDate ? new Date(endDate) : null,
      organizationId: user.organizationId,
      createdById: user.id,
    },
    include: {
      createdBy: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          avatar: true,
        },
      },
    },
  });

  // Add the creator as the project owner
  await prisma.projectMember.create({
    data: {
      projectId: project.id,
      userId: user.id,
      role: 'OWNER',
    },
  });

  res.status(201).json({
    success: true,
    data: { project },
    message: 'Project created successfully',
  });
}));

// Update a project
router.put('/:id', [
  body('name').optional().trim().isLength({ min: 1 }).withMessage('Project name cannot be empty'),
  body('description').optional().trim(),
  body('type').optional().isIn(['CAMPAIGN', 'ONGOING', 'ONE_TIME']).withMessage('Invalid project type'),
  body('status').optional().isIn(['PLANNING', 'IN_PROGRESS', 'REVIEW', 'COMPLETED', 'CANCELLED']).withMessage('Invalid project status'),
  body('budget').optional().isFloat({ min: 0 }).withMessage('Budget must be a positive number'),
  body('deadline').optional().isISO8601().withMessage('Invalid deadline date'),
  body('startDate').optional().isISO8601().withMessage('Invalid start date'),
  body('endDate').optional().isISO8601().withMessage('Invalid end date'),
], asyncHandler(async (req: AuthenticatedRequest, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new CustomError('Validation failed', 400);
  }

  const user = req.user!;
  const { id } = req.params;
  const updateData = req.body;

  // Check if project exists and user has access
  const existingProject = await prisma.project.findFirst({
    where: {
      id,
      organizationId: user.organizationId,
    },
  });

  if (!existingProject) {
    throw new CustomError('Project not found', 404);
  }

  // Convert date strings to Date objects
  if (updateData.deadline) updateData.deadline = new Date(updateData.deadline);
  if (updateData.startDate) updateData.startDate = new Date(updateData.startDate);
  if (updateData.endDate) updateData.endDate = new Date(updateData.endDate);
  if (updateData.budget) updateData.budget = parseFloat(updateData.budget);

  const project = await prisma.project.update({
    where: { id },
    data: updateData,
    include: {
      createdBy: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          avatar: true,
        },
      },
      members: {
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              avatar: true,
            },
          },
        },
      },
    },
  });

  res.json({
    success: true,
    data: { project },
    message: 'Project updated successfully',
  });
}));

// Delete a project
router.delete('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const user = req.user!;
  const { id } = req.params;

  // Check if project exists and user has access
  const existingProject = await prisma.project.findFirst({
    where: {
      id,
      organizationId: user.organizationId,
    },
  });

  if (!existingProject) {
    throw new CustomError('Project not found', 404);
  }

  await prisma.project.delete({
    where: { id },
  });

  res.json({
    success: true,
    message: 'Project deleted successfully',
  });
}));

export default router;
