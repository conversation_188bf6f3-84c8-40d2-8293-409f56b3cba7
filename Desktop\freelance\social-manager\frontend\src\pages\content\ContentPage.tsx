import { useState, useMemo } from 'react'
import { useContentItems, useDeleteContentItem, useBulkUpdateStatus } from '../../hooks/useContent'
import { useProjects } from '../../hooks/useProjects'
import { ContentItem, Platform, ContentStatus, ContentType } from '../../types/content'
import LoadingSpinner from '../../components/ui/LoadingSpinner'
import CreateContentModal from '../../components/content/CreateContentModal'
import ContentCard from '../../components/content/ContentCard'
import ContentFilters from '../../components/content/ContentFilters'
import {
  PlusIcon,
  FunnelIcon,
  Squares2X2Icon,
  ListBulletIcon,
  CheckIcon,
} from '@heroicons/react/24/outline'

const ContentPage: React.FC = () => {
  const [filters, setFilters] = useState({
    projectId: '',
    status: '',
    type: '',
    platforms: '',
  })
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [selectedItems, setSelectedItems] = useState<string[]>([])
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [showFilters, setShowFilters] = useState(false)

  const { data: contentItems, isLoading, error } = useContentItems(filters)
  const { data: projects } = useProjects()
  const deleteContentItem = useDeleteContentItem()
  const bulkUpdateStatus = useBulkUpdateStatus()

  // Filter out empty filter values
  const activeFilters = useMemo(() => {
    const active: any = {}
    Object.entries(filters).forEach(([key, value]) => {
      if (value) active[key] = value
    })
    return active
  }, [filters])

  const handleFilterChange = (newFilters: typeof filters) => {
    setFilters(newFilters)
    setSelectedItems([]) // Clear selection when filters change
  }

  const handleSelectItem = (itemId: string) => {
    setSelectedItems(prev =>
      prev.includes(itemId)
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    )
  }

  const handleSelectAll = () => {
    if (selectedItems.length === contentItems?.length) {
      setSelectedItems([])
    } else {
      setSelectedItems(contentItems?.map(item => item.id) || [])
    }
  }

  const handleBulkStatusUpdate = async (status: ContentStatus) => {
    if (selectedItems.length === 0) return

    await bulkUpdateStatus.mutateAsync({
      contentIds: selectedItems,
      status,
    })
    setSelectedItems([])
  }

  const handleDeleteItem = async (item: ContentItem) => {
    if (window.confirm(`Are you sure you want to delete "${item.title}"?`)) {
      await deleteContentItem.mutateAsync(item.id)
    }
  }

  const getStatusColor = (status: ContentStatus) => {
    switch (status) {
      case 'DRAFT':
        return 'bg-gray-100 text-gray-800'
      case 'REVIEW':
        return 'bg-yellow-100 text-yellow-800'
      case 'APPROVED':
        return 'bg-green-100 text-green-800'
      case 'SCHEDULED':
        return 'bg-blue-100 text-blue-800'
      case 'PUBLISHED':
        return 'bg-purple-100 text-purple-800'
      case 'FAILED':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getPlatformIcon = (platform: Platform) => {
    switch (platform) {
      case 'INSTAGRAM':
        return '📷'
      case 'FACEBOOK':
        return '👥'
      case 'TWITTER':
        return '🐦'
      case 'LINKEDIN':
        return '💼'
      case 'TIKTOK':
        return '🎵'
      case 'YOUTUBE':
        return '📺'
      case 'PINTEREST':
        return '📌'
      default:
        return '📱'
    }
  }

  const getTypeIcon = (type: ContentType) => {
    switch (type) {
      case 'POST':
        return '📝'
      case 'STORY':
        return '📖'
      case 'REEL':
        return '🎬'
      case 'VIDEO':
        return '🎥'
      case 'ARTICLE':
        return '📄'
      case 'CAROUSEL':
        return '🎠'
      default:
        return '📄'
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-600">Failed to load content. Please try again.</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Content</h1>
          <p className="text-gray-600">Create and manage your social media content</p>
        </div>

        <div className="flex items-center space-x-3">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className={`btn-outline flex items-center space-x-2 ${showFilters ? 'bg-gray-100' : ''}`}
          >
            <FunnelIcon className="h-5 w-5" />
            <span>Filters</span>
            {Object.keys(activeFilters).length > 0 && (
              <span className="bg-primary-600 text-white text-xs rounded-full px-2 py-1">
                {Object.keys(activeFilters).length}
              </span>
            )}
          </button>

          <div className="flex items-center border border-gray-300 rounded-md">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 ${viewMode === 'grid' ? 'bg-gray-100 text-gray-900' : 'text-gray-500'}`}
            >
              <Squares2X2Icon className="h-5 w-5" />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 ${viewMode === 'list' ? 'bg-gray-100 text-gray-900' : 'text-gray-500'}`}
            >
              <ListBulletIcon className="h-5 w-5" />
            </button>
          </div>

          <button
            onClick={() => setShowCreateModal(true)}
            className="btn-primary flex items-center space-x-2"
          >
            <PlusIcon className="h-5 w-5" />
            <span>Create Content</span>
          </button>
        </div>
      </div>

      {/* Filters */}
      {showFilters && (
        <ContentFilters
          filters={filters}
          projects={projects || []}
          onFiltersChange={handleFilterChange}
        />
      )}

      {/* Bulk Actions */}
      {selectedItems.length > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <CheckIcon className="h-5 w-5 text-blue-600" />
              <span className="text-blue-900 font-medium">
                {selectedItems.length} item{selectedItems.length > 1 ? 's' : ''} selected
              </span>
            </div>

            <div className="flex items-center space-x-2">
              <button
                onClick={() => handleBulkStatusUpdate('REVIEW')}
                className="btn-sm btn-outline"
              >
                Send to Review
              </button>
              <button
                onClick={() => handleBulkStatusUpdate('APPROVED')}
                className="btn-sm btn-outline"
              >
                Approve
              </button>
              <button
                onClick={() => handleBulkStatusUpdate('SCHEDULED')}
                className="btn-sm btn-outline"
              >
                Schedule
              </button>
              <button
                onClick={() => setSelectedItems([])}
                className="btn-sm btn-ghost"
              >
                Clear Selection
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Content Grid/List */}
      {contentItems && contentItems.length === 0 ? (
        <div className="card">
          <div className="card-content text-center py-12">
            <div className="text-6xl mb-4">📝</div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No content yet</h3>
            <p className="text-gray-500 mb-6">
              Start creating engaging content for your social media campaigns
            </p>
            <button
              onClick={() => setShowCreateModal(true)}
              className="btn-primary"
            >
              Create Your First Content
            </button>
          </div>
        </div>
      ) : (
        <div className={viewMode === 'grid'
          ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
          : 'space-y-4'
        }>
          {contentItems?.map((item) => (
            <ContentCard
              key={item.id}
              item={item}
              viewMode={viewMode}
              isSelected={selectedItems.includes(item.id)}
              onSelect={() => handleSelectItem(item.id)}
              onDelete={() => handleDeleteItem(item)}
              getStatusColor={getStatusColor}
              getPlatformIcon={getPlatformIcon}
              getTypeIcon={getTypeIcon}
            />
          ))}
        </div>
      )}

      {/* Create Content Modal */}
      {showCreateModal && (
        <CreateContentModal
          projects={projects || []}
          onClose={() => setShowCreateModal(false)}
          onSuccess={() => {
            setShowCreateModal(false)
          }}
        />
      )}
    </div>
  )
}

export default ContentPage
