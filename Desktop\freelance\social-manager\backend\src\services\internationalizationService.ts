import { PrismaClient } from '@prisma/client';
import { logger } from '../utils/logger.js';
import { CacheService } from '../config/redis.js';

export interface LocalizationConfig {
  organizationId: string;
  defaultLocale: string;
  supportedLocales: string[];
  fallbackLocale: string;
  autoDetectLocale: boolean;
  rtlSupport: boolean;
  dateFormat: string;
  timeFormat: string;
  numberFormat: string;
  currencyFormat: string;
  timezone: string;
  contentTranslation: {
    enabled: boolean;
    autoTranslate: boolean;
    provider: 'google' | 'azure' | 'aws' | 'deepl';
    apiKey: string;
  };
  regionalSettings: {
    [locale: string]: {
      currency: string;
      dateFormat: string;
      timeFormat: string;
      numberFormat: string;
      firstDayOfWeek: number;
      workingDays: number[];
      holidays: string[];
      businessHours: {
        start: string;
        end: string;
        timezone: string;
      };
    };
  };
}

export interface Translation {
  id: string;
  key: string;
  locale: string;
  value: string;
  context?: string;
  namespace: string;
  organizationId?: string;
  isPlural: boolean;
  pluralForms?: { [key: string]: string };
  metadata: {
    translator?: string;
    reviewedBy?: string;
    lastModified: Date;
    version: number;
    status: 'draft' | 'approved' | 'needs_review';
  };
}

export interface LocaleData {
  locale: string;
  name: string;
  nativeName: string;
  direction: 'ltr' | 'rtl';
  region: string;
  currency: string;
  dateFormats: {
    short: string;
    medium: string;
    long: string;
    full: string;
  };
  timeFormats: {
    short: string;
    medium: string;
    long: string;
    full: string;
  };
  numberFormats: {
    decimal: string;
    currency: string;
    percent: string;
  };
  pluralRules: {
    zero?: string;
    one: string;
    two?: string;
    few?: string;
    many?: string;
    other: string;
  };
  calendar: {
    firstDayOfWeek: number;
    weekendDays: number[];
    monthNames: string[];
    dayNames: string[];
  };
}

export interface ContentTranslationRequest {
  content: string;
  fromLocale: string;
  toLocale: string;
  contentType: 'text' | 'html' | 'markdown';
  context?: string;
  preserveFormatting: boolean;
}

export interface TranslationResult {
  translatedContent: string;
  confidence: number;
  detectedLanguage?: string;
  alternatives?: string[];
  metadata: {
    provider: string;
    model: string;
    processingTime: number;
    characterCount: number;
    cost: number;
  };
}

export interface RegionalCompliance {
  region: string;
  gdprCompliant: boolean;
  ccpaCompliant: boolean;
  dataResidency: string[];
  cookieConsent: {
    required: boolean;
    categories: string[];
    defaultSettings: { [category: string]: boolean };
  };
  accessibilityStandards: string[];
  contentRestrictions: {
    prohibitedContent: string[];
    ageRestrictions: boolean;
    contentRating: string;
  };
  businessRequirements: {
    taxRegistration: boolean;
    localRepresentative: boolean;
    businessLicense: boolean;
    dataProtectionOfficer: boolean;
  };
}

export class InternationalizationService {
  private prisma: PrismaClient;
  private translations: Map<string, Map<string, Translation>>;
  private localeData: Map<string, LocaleData>;
  private translationProviders: Map<string, any>;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
    this.translations = new Map();
    this.localeData = new Map();
    this.translationProviders = new Map();
    this.initializeI18nService();
  }

  /**
   * Set up localization for organization
   */
  async setupLocalization(
    organizationId: string,
    config: LocalizationConfig
  ): Promise<void> {
    try {
      // Validate locales
      await this.validateLocales(config.supportedLocales);

      // Store configuration
      await this.storeLocalizationConfig(organizationId, config);

      // Initialize translations for supported locales
      await this.initializeTranslations(organizationId, config.supportedLocales);

      // Set up regional compliance
      await this.setupRegionalCompliance(organizationId, config);

      logger.info(`Localization setup completed for organization: ${organizationId}`);
    } catch (error) {
      logger.error('Error setting up localization:', error);
      throw error;
    }
  }

  /**
   * Translate content
   */
  async translateContent(
    request: ContentTranslationRequest,
    organizationId?: string
  ): Promise<TranslationResult> {
    try {
      const cacheKey = `translation:${this.hashContent(request.content)}:${request.fromLocale}:${request.toLocale}`;
      const cached = await CacheService.get(cacheKey);
      if (cached) {
        return JSON.parse(cached);
      }

      // Get translation provider
      const config = organizationId ? await this.getLocalizationConfig(organizationId) : null;
      const provider = config?.contentTranslation.provider || 'google';

      // Translate content
      const result = await this.performTranslation(request, provider);

      // Cache result for 24 hours
      await CacheService.set(cacheKey, JSON.stringify(result), 86400);

      // Store translation for future reference
      if (organizationId) {
        await this.storeTranslation(organizationId, request, result);
      }

      return result;
    } catch (error) {
      logger.error('Error translating content:', error);
      throw error;
    }
  }

  /**
   * Get localized content
   */
  async getLocalizedContent(
    key: string,
    locale: string,
    namespace: string = 'default',
    organizationId?: string,
    interpolations?: { [key: string]: any }
  ): Promise<string> {
    try {
      // Get translation
      const translation = await this.getTranslation(key, locale, namespace, organizationId);

      if (!translation) {
        // Fallback to default locale
        const config = organizationId ? await this.getLocalizationConfig(organizationId) : null;
        const fallbackLocale = config?.fallbackLocale || 'en';
        
        if (locale !== fallbackLocale) {
          return this.getLocalizedContent(key, fallbackLocale, namespace, organizationId, interpolations);
        }

        // Return key if no translation found
        return key;
      }

      // Apply interpolations
      let content = translation.value;
      if (interpolations) {
        Object.entries(interpolations).forEach(([placeholder, value]) => {
          content = content.replace(new RegExp(`{{${placeholder}}}`, 'g'), String(value));
        });
      }

      return content;
    } catch (error) {
      logger.error('Error getting localized content:', error);
      return key; // Fallback to key
    }
  }

  /**
   * Format date for locale
   */
  formatDate(
    date: Date,
    locale: string,
    format: 'short' | 'medium' | 'long' | 'full' = 'medium',
    timezone?: string
  ): string {
    try {
      const localeData = this.localeData.get(locale);
      if (!localeData) {
        return date.toLocaleDateString();
      }

      const options: Intl.DateTimeFormatOptions = {
        timeZone: timezone
      };

      switch (format) {
        case 'short':
          options.dateStyle = 'short';
          break;
        case 'medium':
          options.dateStyle = 'medium';
          break;
        case 'long':
          options.dateStyle = 'long';
          break;
        case 'full':
          options.dateStyle = 'full';
          break;
      }

      return new Intl.DateTimeFormat(locale, options).format(date);
    } catch (error) {
      logger.error('Error formatting date:', error);
      return date.toLocaleDateString();
    }
  }

  /**
   * Format number for locale
   */
  formatNumber(
    number: number,
    locale: string,
    type: 'decimal' | 'currency' | 'percent' = 'decimal',
    currency?: string
  ): string {
    try {
      const options: Intl.NumberFormatOptions = {};

      switch (type) {
        case 'currency':
          options.style = 'currency';
          options.currency = currency || 'USD';
          break;
        case 'percent':
          options.style = 'percent';
          break;
        default:
          options.style = 'decimal';
      }

      return new Intl.NumberFormat(locale, options).format(number);
    } catch (error) {
      logger.error('Error formatting number:', error);
      return number.toString();
    }
  }

  /**
   * Detect content language
   */
  async detectLanguage(content: string): Promise<{
    language: string;
    confidence: number;
    alternatives: { language: string; confidence: number }[];
  }> {
    try {
      // Use Google Translate API for language detection
      const response = await fetch('https://translation.googleapis.com/language/translate/v2/detect', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${process.env.GOOGLE_TRANSLATE_API_KEY}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          q: content
        })
      });

      const data = await response.json();
      const detection = data.data.detections[0][0];

      return {
        language: detection.language,
        confidence: detection.confidence,
        alternatives: data.data.detections[0].slice(1).map((alt: any) => ({
          language: alt.language,
          confidence: alt.confidence
        }))
      };
    } catch (error) {
      logger.error('Error detecting language:', error);
      return {
        language: 'en',
        confidence: 0.5,
        alternatives: []
      };
    }
  }

  /**
   * Get regional compliance requirements
   */
  async getRegionalCompliance(region: string): Promise<RegionalCompliance> {
    try {
      const cacheKey = `compliance:${region}`;
      const cached = await CacheService.get(cacheKey);
      if (cached) {
        return JSON.parse(cached);
      }

      const compliance = this.getComplianceRequirements(region);

      // Cache for 24 hours
      await CacheService.set(cacheKey, JSON.stringify(compliance), 86400);

      return compliance;
    } catch (error) {
      logger.error('Error getting regional compliance:', error);
      throw error;
    }
  }

  /**
   * Validate content for regional restrictions
   */
  async validateContentForRegion(
    content: string,
    region: string
  ): Promise<{
    isValid: boolean;
    violations: string[];
    recommendations: string[];
  }> {
    try {
      const compliance = await this.getRegionalCompliance(region);
      const violations: string[] = [];
      const recommendations: string[] = [];

      // Check for prohibited content
      compliance.contentRestrictions.prohibitedContent.forEach(restriction => {
        if (content.toLowerCase().includes(restriction.toLowerCase())) {
          violations.push(`Contains prohibited content: ${restriction}`);
          recommendations.push(`Remove or modify content related to: ${restriction}`);
        }
      });

      // Check for age-restricted content
      if (compliance.contentRestrictions.ageRestrictions) {
        const ageRestrictedKeywords = ['alcohol', 'gambling', 'adult', 'mature'];
        ageRestrictedKeywords.forEach(keyword => {
          if (content.toLowerCase().includes(keyword)) {
            recommendations.push('Consider age verification or content warnings');
          }
        });
      }

      return {
        isValid: violations.length === 0,
        violations,
        recommendations
      };
    } catch (error) {
      logger.error('Error validating content for region:', error);
      throw error;
    }
  }

  /**
   * Initialize i18n service
   */
  private initializeI18nService(): void {
    // Load locale data
    this.loadLocaleData();

    // Initialize translation providers
    this.initializeTranslationProviders();

    // Load default translations
    this.loadDefaultTranslations();
  }

  private loadLocaleData(): void {
    // Load common locales
    const locales = [
      {
        locale: 'en-US',
        name: 'English (United States)',
        nativeName: 'English (United States)',
        direction: 'ltr' as const,
        region: 'US',
        currency: 'USD',
        dateFormats: {
          short: 'M/d/yy',
          medium: 'MMM d, y',
          long: 'MMMM d, y',
          full: 'EEEE, MMMM d, y'
        },
        timeFormats: {
          short: 'h:mm a',
          medium: 'h:mm:ss a',
          long: 'h:mm:ss a z',
          full: 'h:mm:ss a zzzz'
        },
        numberFormats: {
          decimal: '#,##0.###',
          currency: '¤#,##0.00',
          percent: '#,##0%'
        },
        pluralRules: {
          one: 'n = 1',
          other: 'n != 1'
        },
        calendar: {
          firstDayOfWeek: 0,
          weekendDays: [0, 6],
          monthNames: ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'],
          dayNames: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
        }
      },
      {
        locale: 'es-ES',
        name: 'Spanish (Spain)',
        nativeName: 'Español (España)',
        direction: 'ltr' as const,
        region: 'ES',
        currency: 'EUR',
        dateFormats: {
          short: 'd/M/yy',
          medium: 'd MMM y',
          long: 'd \'de\' MMMM \'de\' y',
          full: 'EEEE, d \'de\' MMMM \'de\' y'
        },
        timeFormats: {
          short: 'H:mm',
          medium: 'H:mm:ss',
          long: 'H:mm:ss z',
          full: 'H:mm:ss zzzz'
        },
        numberFormats: {
          decimal: '#.##0,###',
          currency: '#.##0,00 ¤',
          percent: '#.##0 %'
        },
        pluralRules: {
          one: 'n = 1',
          other: 'n != 1'
        },
        calendar: {
          firstDayOfWeek: 1,
          weekendDays: [0, 6],
          monthNames: ['enero', 'febrero', 'marzo', 'abril', 'mayo', 'junio', 'julio', 'agosto', 'septiembre', 'octubre', 'noviembre', 'diciembre'],
          dayNames: ['domingo', 'lunes', 'martes', 'miércoles', 'jueves', 'viernes', 'sábado']
        }
      }
      // Add more locales...
    ];

    locales.forEach(locale => {
      this.localeData.set(locale.locale, locale);
    });
  }

  private initializeTranslationProviders(): void {
    // Initialize Google Translate
    this.translationProviders.set('google', {
      translate: async (request: ContentTranslationRequest) => {
        // Google Translate API implementation
        return this.googleTranslate(request);
      }
    });

    // Initialize other providers...
  }

  private async googleTranslate(request: ContentTranslationRequest): Promise<TranslationResult> {
    try {
      const response = await fetch('https://translation.googleapis.com/language/translate/v2', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${process.env.GOOGLE_TRANSLATE_API_KEY}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          q: request.content,
          source: request.fromLocale,
          target: request.toLocale,
          format: request.contentType === 'html' ? 'html' : 'text'
        })
      });

      const data = await response.json();
      const translation = data.data.translations[0];

      return {
        translatedContent: translation.translatedText,
        confidence: 0.9, // Google doesn't provide confidence scores
        detectedLanguage: translation.detectedSourceLanguage,
        alternatives: [],
        metadata: {
          provider: 'google',
          model: 'translate-v2',
          processingTime: 0,
          characterCount: request.content.length,
          cost: request.content.length * 0.00002 // $20 per 1M characters
        }
      };
    } catch (error) {
      logger.error('Error with Google Translate:', error);
      throw error;
    }
  }

  private loadDefaultTranslations(): void {
    // Load default UI translations
    const defaultTranslations = {
      'en-US': {
        'common.save': 'Save',
        'common.cancel': 'Cancel',
        'common.delete': 'Delete',
        'common.edit': 'Edit',
        'common.loading': 'Loading...',
        'dashboard.title': 'Dashboard',
        'content.create': 'Create Content',
        'analytics.title': 'Analytics'
      },
      'es-ES': {
        'common.save': 'Guardar',
        'common.cancel': 'Cancelar',
        'common.delete': 'Eliminar',
        'common.edit': 'Editar',
        'common.loading': 'Cargando...',
        'dashboard.title': 'Panel de Control',
        'content.create': 'Crear Contenido',
        'analytics.title': 'Analíticas'
      }
    };

    Object.entries(defaultTranslations).forEach(([locale, translations]) => {
      const localeMap = new Map<string, Translation>();
      Object.entries(translations).forEach(([key, value]) => {
        localeMap.set(key, {
          id: `${locale}_${key}`,
          key,
          locale,
          value,
          namespace: 'default',
          isPlural: false,
          metadata: {
            lastModified: new Date(),
            version: 1,
            status: 'approved'
          }
        });
      });
      this.translations.set(locale, localeMap);
    });
  }

  // Helper methods
  private async validateLocales(locales: string[]): Promise<void> {
    locales.forEach(locale => {
      if (!this.localeData.has(locale)) {
        throw new Error(`Unsupported locale: ${locale}`);
      }
    });
  }

  private async storeLocalizationConfig(organizationId: string, config: LocalizationConfig): Promise<void> {
    // Store in database
    logger.debug(`Storing localization config for organization: ${organizationId}`);
  }

  private async initializeTranslations(organizationId: string, locales: string[]): Promise<void> {
    // Initialize translations for organization
    logger.debug(`Initializing translations for organization: ${organizationId}`);
  }

  private async setupRegionalCompliance(organizationId: string, config: LocalizationConfig): Promise<void> {
    // Set up compliance based on supported regions
    logger.debug(`Setting up regional compliance for organization: ${organizationId}`);
  }

  private hashContent(content: string): string {
    // Simple hash function for caching
    let hash = 0;
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    return hash.toString();
  }

  private async getLocalizationConfig(organizationId: string): Promise<LocalizationConfig | null> {
    // Get from database
    return null;
  }

  private async performTranslation(request: ContentTranslationRequest, provider: string): Promise<TranslationResult> {
    const translationProvider = this.translationProviders.get(provider);
    if (!translationProvider) {
      throw new Error(`Translation provider not found: ${provider}`);
    }

    return await translationProvider.translate(request);
  }

  private async storeTranslation(organizationId: string, request: ContentTranslationRequest, result: TranslationResult): Promise<void> {
    // Store translation in database
    logger.debug(`Storing translation for organization: ${organizationId}`);
  }

  private async getTranslation(key: string, locale: string, namespace: string, organizationId?: string): Promise<Translation | null> {
    const localeMap = this.translations.get(locale);
    return localeMap?.get(key) || null;
  }

  private getComplianceRequirements(region: string): RegionalCompliance {
    const complianceMap: { [region: string]: RegionalCompliance } = {
      'EU': {
        region: 'EU',
        gdprCompliant: true,
        ccpaCompliant: false,
        dataResidency: ['EU'],
        cookieConsent: {
          required: true,
          categories: ['necessary', 'analytics', 'marketing', 'preferences'],
          defaultSettings: { necessary: true, analytics: false, marketing: false, preferences: false }
        },
        accessibilityStandards: ['WCAG 2.1 AA'],
        contentRestrictions: {
          prohibitedContent: ['hate speech', 'discrimination'],
          ageRestrictions: true,
          contentRating: 'EU'
        },
        businessRequirements: {
          taxRegistration: true,
          localRepresentative: true,
          businessLicense: false,
          dataProtectionOfficer: true
        }
      },
      'US': {
        region: 'US',
        gdprCompliant: false,
        ccpaCompliant: true,
        dataResidency: ['US'],
        cookieConsent: {
          required: false,
          categories: ['necessary', 'analytics', 'marketing'],
          defaultSettings: { necessary: true, analytics: true, marketing: true }
        },
        accessibilityStandards: ['ADA', 'Section 508'],
        contentRestrictions: {
          prohibitedContent: ['illegal content'],
          ageRestrictions: true,
          contentRating: 'ESRB'
        },
        businessRequirements: {
          taxRegistration: true,
          localRepresentative: false,
          businessLicense: true,
          dataProtectionOfficer: false
        }
      }
    };

    return complianceMap[region] || complianceMap['US'];
  }
}
