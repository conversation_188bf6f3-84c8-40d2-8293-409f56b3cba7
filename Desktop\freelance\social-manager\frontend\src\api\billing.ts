import { apiClient } from './client';

export interface SubscriptionPlan {
  id: string;
  name: string;
  description: string;
  price: number;
  currency: string;
  interval: 'month' | 'year';
  features: PlanFeature[];
  limits: PlanLimits;
  stripePriceId: string;
  isActive: boolean;
}

export interface PlanFeature {
  name: string;
  description: string;
  included: boolean;
  limit?: number;
}

export interface PlanLimits {
  users: number;
  socialAccounts: number;
  postsPerMonth: number;
  aiGenerations: number;
  analyticsRetention: number;
  customBranding: boolean;
  apiAccess: boolean;
  prioritySupport: boolean;
}

export interface UsageMetrics {
  organizationId: string;
  period: string;
  users: number;
  socialAccounts: number;
  postsPublished: number;
  aiGenerations: number;
  apiCalls: number;
  storageUsed: number;
}

export interface Invoice {
  id: string;
  organizationId: string;
  stripeInvoiceId: string;
  amount: number;
  currency: string;
  status: 'draft' | 'open' | 'paid' | 'void' | 'uncollectible';
  dueDate: string;
  paidAt?: string;
  items: InvoiceItem[];
  downloadUrl?: string;
}

export interface InvoiceItem {
  description: string;
  quantity: number;
  unitPrice: number;
  amount: number;
}

export interface PaymentMethod {
  id: string;
  type: 'card' | 'bank_account';
  last4: string;
  brand?: string;
  expiryMonth?: number;
  expiryYear?: number;
  isDefault: boolean;
}

export interface CurrentSubscription {
  subscription: {
    id: string;
    status: string;
    currentPeriodStart: string;
    currentPeriodEnd: string;
    cancelAtPeriodEnd: boolean;
  };
  plan: SubscriptionPlan;
  usage: UsageMetrics;
}

export interface PaymentIntent {
  clientSecret: string;
  amount: number;
  currency: string;
}

export const billingApi = {
  // Subscription Plans
  async getSubscriptionPlans(): Promise<SubscriptionPlan[]> {
    const response = await apiClient.get('/billing/plans');
    return response.data.data;
  },

  // Payment Intents
  async createPaymentIntent(planId: string): Promise<PaymentIntent> {
    const response = await apiClient.post('/billing/create-payment-intent', {
      planId
    });
    return response.data.data;
  },

  // Subscription Management
  async createSubscription(planId: string, paymentMethodId: string) {
    const response = await apiClient.post('/billing/subscribe', {
      planId,
      paymentMethodId
    });
    return response.data;
  },

  async updateSubscription(planId: string) {
    const response = await apiClient.put('/billing/subscription', {
      planId
    });
    return response.data;
  },

  async cancelSubscription(immediate: boolean = false) {
    const response = await apiClient.delete(`/billing/subscription?immediate=${immediate}`);
    return response.data;
  },

  async getCurrentSubscription(): Promise<CurrentSubscription | null> {
    const response = await apiClient.get('/billing/subscription');
    return response.data.data;
  },

  // Usage Tracking
  async getUsageMetrics(period?: string): Promise<UsageMetrics> {
    const params = period ? `?period=${period}` : '';
    const response = await apiClient.get(`/billing/usage${params}`);
    return response.data.data;
  },

  // Invoices
  async getInvoices(limit: number = 10): Promise<Invoice[]> {
    const response = await apiClient.get(`/billing/invoices?limit=${limit}`);
    return response.data.data;
  },

  // Payment Methods
  async getPaymentMethods(): Promise<PaymentMethod[]> {
    const response = await apiClient.get('/billing/payment-methods');
    return response.data.data;
  },

  async addPaymentMethod(paymentMethodId: string) {
    const response = await apiClient.post('/billing/payment-methods', {
      paymentMethodId
    });
    return response.data;
  },

  async setDefaultPaymentMethod(paymentMethodId: string) {
    const response = await apiClient.put(`/billing/payment-methods/${paymentMethodId}/default`);
    return response.data;
  },

  async removePaymentMethod(paymentMethodId: string) {
    const response = await apiClient.delete(`/billing/payment-methods/${paymentMethodId}`);
    return response.data;
  },

  // Utility Functions
  formatPrice(amount: number, currency: string = 'usd'): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency.toUpperCase(),
    }).format(amount);
  },

  formatUsagePercentage(used: number, limit: number): number {
    if (limit === 0) return 0;
    return Math.min((used / limit) * 100, 100);
  },

  getUsageStatus(used: number, limit: number): 'low' | 'medium' | 'high' | 'exceeded' {
    const percentage = this.formatUsagePercentage(used, limit);
    
    if (percentage >= 100) return 'exceeded';
    if (percentage >= 80) return 'high';
    if (percentage >= 50) return 'medium';
    return 'low';
  },

  getUsageColor(status: 'low' | 'medium' | 'high' | 'exceeded'): string {
    const colors = {
      low: '#10B981',      // green
      medium: '#F59E0B',   // yellow
      high: '#EF4444',     // red
      exceeded: '#DC2626'  // dark red
    };
    return colors[status];
  },

  getPlanBadgeColor(planType: string): string {
    const colors: Record<string, string> = {
      STARTER: '#6B7280',     // gray
      PROFESSIONAL: '#3B82F6', // blue
      AGENCY: '#8B5CF6',      // purple
      ENTERPRISE: '#F59E0B'   // gold
    };
    return colors[planType] || '#6B7280';
  },

  isFeatureIncluded(plan: SubscriptionPlan, featureName: string): boolean {
    return plan.features.some(feature => 
      feature.name === featureName && feature.included
    );
  },

  getFeatureLimit(plan: SubscriptionPlan, featureName: string): number | null {
    const feature = plan.features.find(f => f.name === featureName);
    return feature?.limit || null;
  },

  calculateAnnualSavings(monthlyPrice: number, annualPrice: number): number {
    const monthlyTotal = monthlyPrice * 12;
    return monthlyTotal - annualPrice;
  },

  calculateAnnualSavingsPercentage(monthlyPrice: number, annualPrice: number): number {
    const monthlyTotal = monthlyPrice * 12;
    return ((monthlyTotal - annualPrice) / monthlyTotal) * 100;
  },

  // Plan comparison helpers
  comparePlans(plan1: SubscriptionPlan, plan2: SubscriptionPlan): {
    priceDifference: number;
    featureDifferences: string[];
    limitDifferences: string[];
  } {
    const priceDifference = plan2.price - plan1.price;
    
    const featureDifferences: string[] = [];
    const limitDifferences: string[] = [];

    // Compare features
    plan2.features.forEach(feature2 => {
      const feature1 = plan1.features.find(f => f.name === feature2.name);
      if (!feature1 && feature2.included) {
        featureDifferences.push(`+ ${feature2.name}`);
      } else if (feature1 && !feature1.included && feature2.included) {
        featureDifferences.push(`+ ${feature2.name}`);
      }
    });

    // Compare limits
    const limits1 = plan1.limits;
    const limits2 = plan2.limits;

    Object.keys(limits2).forEach(key => {
      const limit1 = (limits1 as any)[key];
      const limit2 = (limits2 as any)[key];
      
      if (typeof limit1 === 'number' && typeof limit2 === 'number' && limit2 > limit1) {
        limitDifferences.push(`${key}: ${limit1} → ${limit2}`);
      } else if (typeof limit1 === 'boolean' && typeof limit2 === 'boolean' && !limit1 && limit2) {
        limitDifferences.push(`+ ${key}`);
      }
    });

    return {
      priceDifference,
      featureDifferences,
      limitDifferences
    };
  },

  // Billing period helpers
  getNextBillingDate(subscription: CurrentSubscription): Date {
    return new Date(subscription.subscription.currentPeriodEnd);
  },

  getDaysUntilBilling(subscription: CurrentSubscription): number {
    const nextBilling = this.getNextBillingDate(subscription);
    const now = new Date();
    const diffTime = nextBilling.getTime() - now.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  },

  // Invoice helpers
  getInvoiceStatusColor(status: Invoice['status']): string {
    const colors = {
      draft: '#6B7280',
      open: '#F59E0B',
      paid: '#10B981',
      void: '#6B7280',
      uncollectible: '#EF4444'
    };
    return colors[status];
  },

  getInvoiceStatusText(status: Invoice['status']): string {
    const texts = {
      draft: 'Draft',
      open: 'Pending',
      paid: 'Paid',
      void: 'Void',
      uncollectible: 'Uncollectible'
    };
    return texts[status];
  }
};
