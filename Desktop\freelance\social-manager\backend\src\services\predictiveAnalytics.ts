import { PrismaClient } from '@prisma/client';
import { logger } from '../utils/logger.js';
import { CacheService } from '../config/redis.js';

export interface PredictionRequest {
  contentId?: string;
  content?: string;
  platform: string;
  scheduledTime?: Date;
  hashtags?: string[];
  contentType: string;
  targetAudience?: string;
}

export interface PerformancePrediction {
  expectedEngagement: {
    likes: number;
    comments: number;
    shares: number;
    clicks: number;
    reach: number;
    impressions: number;
  };
  confidence: number;
  factors: {
    timing: number;
    content: number;
    hashtags: number;
    platform: number;
    audience: number;
  };
  recommendations: string[];
  riskFactors: string[];
  optimizationSuggestions: string[];
}

export interface TrendPrediction {
  topic: string;
  platform: string;
  trendScore: number;
  peakTime: Date;
  duration: number; // in days
  confidence: number;
  relatedTopics: string[];
  suggestedContent: string[];
}

export interface AudienceInsight {
  segment: string;
  size: number;
  engagement: number;
  bestTimes: string[];
  preferredContent: string[];
  demographics: {
    ageGroups: { [key: string]: number };
    locations: { [key: string]: number };
    interests: string[];
  };
  growthTrend: number;
}

export class PredictiveAnalyticsService {
  private prisma: PrismaClient;
  private models: Map<string, any>;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
    this.models = new Map();
    this.initializeModels();
  }

  /**
   * Predict content performance before publishing
   */
  async predictContentPerformance(
    request: PredictionRequest,
    organizationId: string
  ): Promise<PerformancePrediction> {
    try {
      logger.info(`Predicting performance for ${request.platform} content`);

      // Get historical data for similar content
      const historicalData = await this.getHistoricalData(request, organizationId);

      // Calculate base predictions using multiple factors
      const timingScore = await this.calculateTimingScore(request.scheduledTime, request.platform);
      const contentScore = await this.calculateContentScore(request.content, request.contentType);
      const hashtagScore = await this.calculateHashtagScore(request.hashtags, request.platform);
      const platformScore = await this.calculatePlatformScore(request.platform, organizationId);
      const audienceScore = await this.calculateAudienceScore(request.targetAudience, organizationId);

      // Weighted prediction model
      const weights = {
        timing: 0.25,
        content: 0.30,
        hashtags: 0.15,
        platform: 0.20,
        audience: 0.10
      };

      const overallScore = (
        timingScore * weights.timing +
        contentScore * weights.content +
        hashtagScore * weights.hashtags +
        platformScore * weights.platform +
        audienceScore * weights.audience
      );

      // Calculate expected engagement based on historical averages
      const baseMetrics = await this.getBaselineMetrics(request.platform, organizationId);
      const multiplier = Math.max(0.1, Math.min(3.0, overallScore));

      const expectedEngagement = {
        likes: Math.round(baseMetrics.avgLikes * multiplier),
        comments: Math.round(baseMetrics.avgComments * multiplier),
        shares: Math.round(baseMetrics.avgShares * multiplier),
        clicks: Math.round(baseMetrics.avgClicks * multiplier),
        reach: Math.round(baseMetrics.avgReach * multiplier),
        impressions: Math.round(baseMetrics.avgImpressions * multiplier)
      };

      // Generate recommendations and risk factors
      const recommendations = this.generateRecommendations(
        { timing: timingScore, content: contentScore, hashtags: hashtagScore, platform: platformScore, audience: audienceScore }
      );

      const riskFactors = this.identifyRiskFactors(
        { timing: timingScore, content: contentScore, hashtags: hashtagScore, platform: platformScore, audience: audienceScore }
      );

      const optimizationSuggestions = this.generateOptimizationSuggestions(request, {
        timing: timingScore,
        content: contentScore,
        hashtags: hashtagScore,
        platform: platformScore,
        audience: audienceScore
      });

      const confidence = this.calculateConfidence(historicalData.length, overallScore);

      return {
        expectedEngagement,
        confidence,
        factors: {
          timing: timingScore,
          content: contentScore,
          hashtags: hashtagScore,
          platform: platformScore,
          audience: audienceScore
        },
        recommendations,
        riskFactors,
        optimizationSuggestions
      };
    } catch (error) {
      logger.error('Error predicting content performance:', error);
      throw new Error('Failed to predict content performance');
    }
  }

  /**
   * Predict trending topics and content opportunities
   */
  async predictTrends(
    platform: string,
    organizationId: string,
    timeframe: number = 7 // days
  ): Promise<TrendPrediction[]> {
    try {
      const cacheKey = `trends:${platform}:${organizationId}:${timeframe}`;
      const cached = await CacheService.get(cacheKey);
      if (cached) {
        return JSON.parse(cached);
      }

      // Analyze recent content performance and engagement patterns
      const recentContent = await this.prisma.contentItem.findMany({
        where: {
          project: { organizationId },
          platforms: { has: platform },
          createdAt: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
          }
        },
        include: { analytics: true },
        orderBy: { createdAt: 'desc' }
      });

      // Extract trending topics using engagement velocity
      const topicEngagement = new Map<string, { total: number; count: number; recent: number }>();
      const now = Date.now();

      recentContent.forEach(content => {
        const keywords = this.extractKeywords(content.content as any);
        const contentAge = now - content.createdAt.getTime();
        const isRecent = contentAge < 7 * 24 * 60 * 60 * 1000; // Last 7 days

        const engagement = this.calculateTotalEngagement(content.analytics);

        keywords.forEach(keyword => {
          if (!topicEngagement.has(keyword)) {
            topicEngagement.set(keyword, { total: 0, count: 0, recent: 0 });
          }
          const data = topicEngagement.get(keyword)!;
          data.total += engagement;
          data.count += 1;
          if (isRecent) {
            data.recent += engagement;
          }
        });
      });

      // Calculate trend scores
      const trends: TrendPrediction[] = [];
      topicEngagement.forEach((data, topic) => {
        if (data.count < 3) return; // Need minimum data points

        const avgEngagement = data.total / data.count;
        const recentBoost = data.recent > 0 ? data.recent / (data.total - data.recent + 1) : 0;
        const trendScore = avgEngagement * (1 + recentBoost);

        if (trendScore > 10) { // Threshold for trending
          trends.push({
            topic,
            platform,
            trendScore,
            peakTime: new Date(Date.now() + Math.random() * timeframe * 24 * 60 * 60 * 1000),
            duration: Math.round(3 + Math.random() * 10), // 3-13 days
            confidence: Math.min(0.9, data.count / 10),
            relatedTopics: this.findRelatedTopics(topic, Array.from(topicEngagement.keys())),
            suggestedContent: this.generateContentSuggestions(topic, platform)
          });
        }
      });

      // Sort by trend score
      trends.sort((a, b) => b.trendScore - a.trendScore);
      const topTrends = trends.slice(0, 10);

      // Cache for 4 hours
      await CacheService.set(cacheKey, JSON.stringify(topTrends), 14400);

      return topTrends;
    } catch (error) {
      logger.error('Error predicting trends:', error);
      return [];
    }
  }

  /**
   * Analyze audience segments and predict behavior
   */
  async analyzeAudienceSegments(organizationId: string): Promise<AudienceInsight[]> {
    try {
      const cacheKey = `audience_insights:${organizationId}`;
      const cached = await CacheService.get(cacheKey);
      if (cached) {
        return JSON.parse(cached);
      }

      // Get audience data from analytics
      const audienceData = await this.getAudienceData(organizationId);

      // Segment audience based on engagement patterns
      const segments = this.segmentAudience(audienceData);

      // Analyze each segment
      const insights: AudienceInsight[] = [];
      for (const segment of segments) {
        const insight = await this.analyzeSegment(segment, organizationId);
        insights.push(insight);
      }

      // Cache for 6 hours
      await CacheService.set(cacheKey, JSON.stringify(insights), 21600);

      return insights;
    } catch (error) {
      logger.error('Error analyzing audience segments:', error);
      return [];
    }
  }

  /**
   * Predict optimal content calendar
   */
  async predictOptimalCalendar(
    organizationId: string,
    platform: string,
    days: number = 30
  ): Promise<{
    calendar: { date: Date; contentType: string; topic: string; confidence: number }[];
    insights: string[];
  }> {
    try {
      // Analyze historical posting patterns and performance
      const historicalData = await this.getHistoricalPostingData(organizationId, platform);
      
      // Predict optimal posting schedule
      const calendar = [];
      const startDate = new Date();

      for (let i = 0; i < days; i++) {
        const date = new Date(startDate.getTime() + i * 24 * 60 * 60 * 1000);
        const dayOfWeek = date.getDay();
        const hour = this.predictOptimalHour(dayOfWeek, platform, historicalData);
        
        date.setHours(hour, 0, 0, 0);

        const contentType = this.predictOptimalContentType(date, platform, historicalData);
        const topic = await this.predictOptimalTopic(date, platform, organizationId);
        const confidence = this.calculateCalendarConfidence(date, contentType, topic);

        calendar.push({
          date,
          contentType,
          topic,
          confidence
        });
      }

      const insights = this.generateCalendarInsights(calendar, historicalData);

      return { calendar, insights };
    } catch (error) {
      logger.error('Error predicting optimal calendar:', error);
      return { calendar: [], insights: [] };
    }
  }

  /**
   * Initialize ML models
   */
  private initializeModels(): void {
    // Initialize simple regression models for different platforms
    this.models.set('engagement_predictor', {
      weights: {
        timing: 0.25,
        content_length: 0.15,
        hashtag_count: 0.10,
        emoji_count: 0.05,
        question_count: 0.10,
        call_to_action: 0.15,
        visual_content: 0.20
      }
    });

    this.models.set('trend_detector', {
      momentum_threshold: 1.5,
      velocity_weight: 0.6,
      volume_weight: 0.4
    });
  }

  /**
   * Helper methods for calculations
   */
  private async calculateTimingScore(scheduledTime: Date | undefined, platform: string): Promise<number> {
    if (!scheduledTime) return 0.5; // Neutral score for immediate posting

    const hour = scheduledTime.getHours();
    const dayOfWeek = scheduledTime.getDay();

    // Platform-specific optimal times
    const optimalTimes = {
      instagram: { hours: [11, 14, 17], days: [1, 2, 3, 4, 5] },
      facebook: { hours: [9, 13, 15], days: [1, 2, 3, 4] },
      twitter: { hours: [8, 12, 17], days: [1, 2, 3, 4] },
      linkedin: { hours: [8, 12, 17], days: [1, 2, 3, 4] }
    };

    const optimal = optimalTimes[platform as keyof typeof optimalTimes] || optimalTimes.instagram;
    
    const hourScore = optimal.hours.includes(hour) ? 1.0 : 0.5;
    const dayScore = optimal.days.includes(dayOfWeek) ? 1.0 : 0.7;

    return (hourScore + dayScore) / 2;
  }

  private async calculateContentScore(content: string | undefined, contentType: string): Promise<number> {
    if (!content) return 0.5;

    let score = 0.5;

    // Length optimization
    const length = content.length;
    if (length >= 100 && length <= 300) score += 0.2;
    else if (length > 300 && length <= 500) score += 0.1;

    // Engagement elements
    if (content.includes('?')) score += 0.1; // Questions
    if (/[!]{1,2}/.test(content)) score += 0.05; // Exclamation
    if (/[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]/gu.test(content)) score += 0.1; // Emojis

    // Call to action
    const ctaWords = ['click', 'visit', 'learn', 'discover', 'try', 'get', 'download', 'sign up'];
    if (ctaWords.some(word => content.toLowerCase().includes(word))) score += 0.15;

    return Math.min(1.0, score);
  }

  private async calculateHashtagScore(hashtags: string[] | undefined, platform: string): Promise<number> {
    if (!hashtags || hashtags.length === 0) return 0.3;

    // Optimal hashtag counts by platform
    const optimalCounts = {
      instagram: { min: 5, max: 15, optimal: 11 },
      facebook: { min: 1, max: 3, optimal: 2 },
      twitter: { min: 1, max: 3, optimal: 2 },
      linkedin: { min: 3, max: 5, optimal: 4 }
    };

    const optimal = optimalCounts[platform as keyof typeof optimalCounts] || optimalCounts.instagram;
    const count = hashtags.length;

    if (count >= optimal.min && count <= optimal.max) {
      return count === optimal.optimal ? 1.0 : 0.8;
    }

    return count < optimal.min ? 0.4 : 0.6;
  }

  private async calculatePlatformScore(platform: string, organizationId: string): Promise<number> {
    // This would analyze historical performance on the platform
    // For now, return a base score
    return 0.7;
  }

  private async calculateAudienceScore(targetAudience: string | undefined, organizationId: string): Promise<number> {
    // This would analyze audience match and engagement
    // For now, return a base score
    return targetAudience ? 0.8 : 0.6;
  }

  private async getHistoricalData(request: PredictionRequest, organizationId: string): Promise<any[]> {
    // Fetch similar historical content for comparison
    return [];
  }

  private async getBaselineMetrics(platform: string, organizationId: string): Promise<any> {
    // Calculate baseline metrics from historical data
    return {
      avgLikes: 50,
      avgComments: 5,
      avgShares: 3,
      avgClicks: 10,
      avgReach: 500,
      avgImpressions: 1000
    };
  }

  private generateRecommendations(factors: any): string[] {
    const recommendations = [];

    if (factors.timing < 0.6) {
      recommendations.push('Consider posting at a more optimal time for your audience');
    }
    if (factors.content < 0.6) {
      recommendations.push('Add more engaging elements like questions or calls to action');
    }
    if (factors.hashtags < 0.6) {
      recommendations.push('Optimize your hashtag strategy for better discoverability');
    }

    return recommendations;
  }

  private identifyRiskFactors(factors: any): string[] {
    const risks = [];

    if (factors.timing < 0.4) {
      risks.push('Posting time may result in low visibility');
    }
    if (factors.content < 0.4) {
      risks.push('Content may not generate sufficient engagement');
    }

    return risks;
  }

  private generateOptimizationSuggestions(request: PredictionRequest, factors: any): string[] {
    const suggestions = [];

    if (factors.content < 0.7) {
      suggestions.push('Add emojis to make content more engaging');
      suggestions.push('Include a clear call to action');
    }
    if (factors.hashtags < 0.7) {
      suggestions.push('Research trending hashtags in your niche');
    }

    return suggestions;
  }

  private calculateConfidence(dataPoints: number, overallScore: number): number {
    const dataConfidence = Math.min(1.0, dataPoints / 50);
    const scoreConfidence = overallScore;
    return (dataConfidence + scoreConfidence) / 2;
  }

  private extractKeywords(content: any): string[] {
    if (!content || typeof content !== 'string') return [];
    
    return content.toLowerCase()
      .replace(/[^\w\s]/g, '')
      .split(/\s+/)
      .filter(word => word.length > 3)
      .filter(word => !['this', 'that', 'with', 'have', 'will', 'from', 'they', 'know', 'want', 'been', 'good', 'much', 'some', 'time'].includes(word));
  }

  private calculateTotalEngagement(analytics: any[]): number {
    if (!analytics || analytics.length === 0) return 0;
    
    return analytics.reduce((total, metric) => {
      return total + (metric.likes || 0) + (metric.comments || 0) + (metric.shares || 0);
    }, 0);
  }

  private findRelatedTopics(topic: string, allTopics: string[]): string[] {
    // Simple similarity based on common words
    return allTopics
      .filter(t => t !== topic && (t.includes(topic) || topic.includes(t)))
      .slice(0, 3);
  }

  private generateContentSuggestions(topic: string, platform: string): string[] {
    return [
      `Create a how-to guide about ${topic}`,
      `Share tips and tricks related to ${topic}`,
      `Post behind-the-scenes content about ${topic}`
    ];
  }

  private async getAudienceData(organizationId: string): Promise<any[]> {
    // Fetch audience analytics data
    return [];
  }

  private segmentAudience(audienceData: any[]): any[] {
    // Segment audience based on behavior patterns
    return [];
  }

  private async analyzeSegment(segment: any, organizationId: string): Promise<AudienceInsight> {
    // Analyze individual audience segment
    return {
      segment: 'High Engagement Users',
      size: 1000,
      engagement: 0.85,
      bestTimes: ['09:00', '15:00', '19:00'],
      preferredContent: ['video', 'carousel', 'story'],
      demographics: {
        ageGroups: { '25-34': 40, '35-44': 35, '18-24': 25 },
        locations: { 'US': 60, 'UK': 20, 'CA': 20 },
        interests: ['technology', 'business', 'lifestyle']
      },
      growthTrend: 0.15
    };
  }

  private async getHistoricalPostingData(organizationId: string, platform: string): Promise<any[]> {
    // Fetch historical posting patterns
    return [];
  }

  private predictOptimalHour(dayOfWeek: number, platform: string, historicalData: any[]): number {
    // Predict optimal posting hour based on historical data
    const defaultHours = { 0: 14, 1: 9, 2: 9, 3: 9, 4: 9, 5: 17, 6: 14 };
    return defaultHours[dayOfWeek as keyof typeof defaultHours] || 12;
  }

  private predictOptimalContentType(date: Date, platform: string, historicalData: any[]): string {
    // Predict optimal content type for the date
    const types = ['image', 'video', 'carousel', 'story'];
    return types[date.getDay() % types.length];
  }

  private async predictOptimalTopic(date: Date, platform: string, organizationId: string): Promise<string> {
    // Predict optimal topic for the date
    const topics = ['industry news', 'tips & tricks', 'behind the scenes', 'user generated content'];
    return topics[date.getDay() % topics.length];
  }

  private calculateCalendarConfidence(date: Date, contentType: string, topic: string): number {
    // Calculate confidence score for calendar prediction
    return 0.7 + Math.random() * 0.2; // 0.7-0.9 range
  }

  private generateCalendarInsights(calendar: any[], historicalData: any[]): string[] {
    return [
      'Video content performs 40% better on Fridays',
      'Morning posts (9-11 AM) show higher engagement rates',
      'Behind-the-scenes content generates more comments'
    ];
  }
}
