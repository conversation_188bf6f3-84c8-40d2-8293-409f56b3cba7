import { PrismaClient } from '@prisma/client';
import { logger } from '../utils/logger.js';
import { CacheService } from '../config/redis.js';
import { AIContentGenerator } from './aiContentGenerator.js';
import { RecommendationEngine } from './recommendationEngine.js';
import { PredictiveAnalyticsService } from './predictiveAnalytics.js';

export interface AutomationRule {
  id: string;
  name: string;
  description: string;
  organizationId: string;
  isActive: boolean;
  trigger: AutomationTrigger;
  conditions: AutomationCondition[];
  actions: AutomationAction[];
  schedule?: AutomationSchedule;
  priority: number;
  createdBy: string;
  createdAt: Date;
  lastExecuted?: Date;
  executionCount: number;
}

export interface AutomationTrigger {
  type: 'schedule' | 'event' | 'performance' | 'trend' | 'manual';
  config: {
    eventType?: string;
    schedule?: string; // cron expression
    performanceThreshold?: number;
    trendScore?: number;
    platforms?: string[];
  };
}

export interface AutomationCondition {
  type: 'time' | 'performance' | 'audience' | 'content' | 'platform';
  operator: 'equals' | 'greater_than' | 'less_than' | 'contains' | 'not_contains';
  value: any;
  field: string;
}

export interface AutomationAction {
  type: 'create_content' | 'schedule_post' | 'send_notification' | 'update_strategy' | 'generate_report' | 'optimize_content';
  config: {
    contentType?: string;
    platform?: string;
    template?: string;
    recipients?: string[];
    message?: string;
    parameters?: Record<string, any>;
  };
}

export interface AutomationSchedule {
  frequency: 'once' | 'daily' | 'weekly' | 'monthly' | 'custom';
  interval?: number;
  daysOfWeek?: number[];
  timeOfDay?: string;
  timezone?: string;
  startDate?: Date;
  endDate?: Date;
}

export interface AutomationExecution {
  ruleId: string;
  executedAt: Date;
  success: boolean;
  results: any[];
  errors?: string[];
  duration: number;
  triggeredBy: string;
}

export class AutomationEngine {
  private prisma: PrismaClient;
  private aiGenerator: AIContentGenerator;
  private recommendationEngine: RecommendationEngine;
  private predictiveAnalytics: PredictiveAnalyticsService;
  private activeRules: Map<string, AutomationRule>;
  private executionQueue: AutomationExecution[];

  constructor(
    prisma: PrismaClient,
    aiGenerator: AIContentGenerator,
    recommendationEngine: RecommendationEngine,
    predictiveAnalytics: PredictiveAnalyticsService
  ) {
    this.prisma = prisma;
    this.aiGenerator = aiGenerator;
    this.recommendationEngine = recommendationEngine;
    this.predictiveAnalytics = predictiveAnalytics;
    this.activeRules = new Map();
    this.executionQueue = [];
    
    this.initializeEngine();
  }

  /**
   * Create a new automation rule
   */
  async createAutomationRule(rule: Omit<AutomationRule, 'id' | 'createdAt' | 'executionCount'>): Promise<AutomationRule> {
    try {
      const newRule: AutomationRule = {
        ...rule,
        id: this.generateRuleId(),
        createdAt: new Date(),
        executionCount: 0
      };

      // Validate rule configuration
      this.validateRule(newRule);

      // Save to database
      await this.saveRule(newRule);

      // Add to active rules if enabled
      if (newRule.isActive) {
        this.activeRules.set(newRule.id, newRule);
        await this.scheduleRule(newRule);
      }

      logger.info(`Created automation rule: ${newRule.name} (${newRule.id})`);
      return newRule;
    } catch (error) {
      logger.error('Error creating automation rule:', error);
      throw error;
    }
  }

  /**
   * Execute automation rule
   */
  async executeRule(ruleId: string, triggeredBy: string = 'system'): Promise<AutomationExecution> {
    const startTime = Date.now();
    const execution: AutomationExecution = {
      ruleId,
      executedAt: new Date(),
      success: false,
      results: [],
      duration: 0,
      triggeredBy
    };

    try {
      const rule = this.activeRules.get(ruleId);
      if (!rule) {
        throw new Error(`Rule ${ruleId} not found or not active`);
      }

      logger.info(`Executing automation rule: ${rule.name} (${ruleId})`);

      // Check conditions
      const conditionsMet = await this.evaluateConditions(rule.conditions, rule.organizationId);
      if (!conditionsMet) {
        logger.debug(`Conditions not met for rule: ${rule.name}`);
        execution.success = true;
        execution.results.push({ message: 'Conditions not met, skipping execution' });
        return execution;
      }

      // Execute actions
      for (const action of rule.actions) {
        try {
          const result = await this.executeAction(action, rule);
          execution.results.push(result);
        } catch (actionError) {
          logger.error(`Error executing action ${action.type}:`, actionError);
          execution.errors = execution.errors || [];
          execution.errors.push(`Action ${action.type}: ${actionError}`);
        }
      }

      // Update rule execution count
      rule.executionCount++;
      rule.lastExecuted = new Date();
      await this.updateRule(rule);

      execution.success = true;
      logger.info(`Successfully executed rule: ${rule.name}`);
    } catch (error) {
      logger.error(`Error executing rule ${ruleId}:`, error);
      execution.errors = [error.toString()];
    } finally {
      execution.duration = Date.now() - startTime;
      await this.saveExecution(execution);
    }

    return execution;
  }

  /**
   * Smart content generation automation
   */
  async createSmartContentAutomation(
    organizationId: string,
    config: {
      platforms: string[];
      contentTypes: string[];
      frequency: number; // posts per week
      topics: string[];
      tone: string;
      autoPublish: boolean;
    }
  ): Promise<AutomationRule> {
    const rule: Omit<AutomationRule, 'id' | 'createdAt' | 'executionCount'> = {
      name: 'Smart Content Generation',
      description: 'Automatically generate and schedule content based on AI recommendations',
      organizationId,
      isActive: true,
      trigger: {
        type: 'schedule',
        config: {
          schedule: this.calculateSchedule(config.frequency),
          platforms: config.platforms
        }
      },
      conditions: [
        {
          type: 'time',
          operator: 'greater_than',
          value: '08:00',
          field: 'current_time'
        }
      ],
      actions: [
        {
          type: 'create_content',
          config: {
            contentType: 'smart_generated',
            platforms: config.platforms,
            parameters: {
              topics: config.topics,
              tone: config.tone,
              autoPublish: config.autoPublish
            }
          }
        }
      ],
      priority: 1,
      createdBy: 'system'
    };

    return await this.createAutomationRule(rule);
  }

  /**
   * Performance-based optimization automation
   */
  async createPerformanceOptimizationAutomation(
    organizationId: string,
    config: {
      platforms: string[];
      performanceThreshold: number;
      optimizationActions: string[];
    }
  ): Promise<AutomationRule> {
    const rule: Omit<AutomationRule, 'id' | 'createdAt' | 'executionCount'> = {
      name: 'Performance Optimization',
      description: 'Automatically optimize content based on performance metrics',
      organizationId,
      isActive: true,
      trigger: {
        type: 'performance',
        config: {
          performanceThreshold: config.performanceThreshold,
          platforms: config.platforms
        }
      },
      conditions: [
        {
          type: 'performance',
          operator: 'less_than',
          value: config.performanceThreshold,
          field: 'engagement_rate'
        }
      ],
      actions: [
        {
          type: 'optimize_content',
          config: {
            platforms: config.platforms,
            parameters: {
              actions: config.optimizationActions
            }
          }
        },
        {
          type: 'send_notification',
          config: {
            message: 'Content performance below threshold - optimization applied',
            recipients: ['admin']
          }
        }
      ],
      priority: 2,
      createdBy: 'system'
    };

    return await this.createAutomationRule(rule);
  }

  /**
   * Trend-based content automation
   */
  async createTrendAutomation(
    organizationId: string,
    config: {
      platforms: string[];
      trendThreshold: number;
      contentTypes: string[];
    }
  ): Promise<AutomationRule> {
    const rule: Omit<AutomationRule, 'id' | 'createdAt' | 'executionCount'> = {
      name: 'Trend-Based Content Creation',
      description: 'Automatically create content based on trending topics',
      organizationId,
      isActive: true,
      trigger: {
        type: 'trend',
        config: {
          trendScore: config.trendThreshold,
          platforms: config.platforms
        }
      },
      conditions: [
        {
          type: 'content',
          operator: 'less_than',
          value: 5,
          field: 'recent_posts_count'
        }
      ],
      actions: [
        {
          type: 'create_content',
          config: {
            contentType: 'trend_based',
            platforms: config.platforms,
            parameters: {
              contentTypes: config.contentTypes,
              useTrends: true
            }
          }
        }
      ],
      priority: 3,
      createdBy: 'system'
    };

    return await this.createAutomationRule(rule);
  }

  /**
   * Execute specific automation action
   */
  private async executeAction(action: AutomationAction, rule: AutomationRule): Promise<any> {
    switch (action.type) {
      case 'create_content':
        return await this.executeCreateContentAction(action, rule);
      
      case 'schedule_post':
        return await this.executeSchedulePostAction(action, rule);
      
      case 'send_notification':
        return await this.executeSendNotificationAction(action, rule);
      
      case 'optimize_content':
        return await this.executeOptimizeContentAction(action, rule);
      
      case 'generate_report':
        return await this.executeGenerateReportAction(action, rule);
      
      default:
        throw new Error(`Unknown action type: ${action.type}`);
    }
  }

  /**
   * Execute content creation action
   */
  private async executeCreateContentAction(action: AutomationAction, rule: AutomationRule): Promise<any> {
    const { platforms, parameters } = action.config;
    const results = [];

    for (const platform of platforms || []) {
      try {
        // Get recommendations for content
        const recommendations = await this.recommendationEngine.generateRecommendations(
          rule.createdBy,
          rule.organizationId,
          5
        );

        // Select best topic from recommendations
        const contentRec = recommendations.find(r => r.type === 'content_idea');
        const topic = contentRec?.data?.topic || parameters?.topics?.[0] || 'industry insights';

        // Generate content using AI
        const contentRequest = {
          type: 'post' as const,
          platform: platform as any,
          topic,
          tone: parameters?.tone || 'professional' as const,
          length: 'medium' as const,
          includeHashtags: true,
          includeEmojis: platform !== 'linkedin'
        };

        const generatedContent = await this.aiGenerator.generateContent(
          contentRequest,
          rule.createdBy,
          rule.organizationId
        );

        // Create content item in database
        const contentItem = await this.prisma.contentItem.create({
          data: {
            content: generatedContent.content,
            type: 'post',
            platforms: [platform],
            hashtags: generatedContent.hashtags,
            status: parameters?.autoPublish ? 'PUBLISHED' : 'DRAFT',
            createdBy: rule.createdBy,
            projectId: await this.getDefaultProjectId(rule.organizationId),
            scheduledAt: parameters?.autoPublish ? new Date() : null,
            metadata: {
              automationRuleId: rule.id,
              aiGenerated: true,
              confidence: generatedContent.confidence
            }
          }
        });

        results.push({
          platform,
          contentId: contentItem.id,
          content: generatedContent.content,
          status: contentItem.status
        });

        logger.info(`Created automated content for ${platform}: ${contentItem.id}`);
      } catch (error) {
        logger.error(`Error creating content for ${platform}:`, error);
        results.push({
          platform,
          error: error.toString()
        });
      }
    }

    return { action: 'create_content', results };
  }

  /**
   * Execute schedule post action
   */
  private async executeSchedulePostAction(action: AutomationAction, rule: AutomationRule): Promise<any> {
    // Get optimal posting times
    const optimalTimes = await this.predictiveAnalytics.predictOptimalPostingTimes(
      rule.createdBy,
      rule.organizationId,
      action.config.platform || 'instagram'
    );

    // Schedule content for optimal times
    const nextOptimalTime = this.calculateNextOptimalTime(optimalTimes.times);

    return {
      action: 'schedule_post',
      scheduledTime: nextOptimalTime,
      confidence: optimalTimes.confidence
    };
  }

  /**
   * Execute send notification action
   */
  private async executeSendNotificationAction(action: AutomationAction, rule: AutomationRule): Promise<any> {
    const { message, recipients } = action.config;

    // Send notifications to specified recipients
    // This would integrate with your notification system
    logger.info(`Automation notification: ${message}`);

    return {
      action: 'send_notification',
      message,
      recipients: recipients || ['admin'],
      sent: true
    };
  }

  /**
   * Execute optimize content action
   */
  private async executeOptimizeContentAction(action: AutomationAction, rule: AutomationRule): Promise<any> {
    // Get recent underperforming content
    const recentContent = await this.prisma.contentItem.findMany({
      where: {
        project: { organizationId: rule.organizationId },
        createdAt: {
          gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // Last 7 days
        }
      },
      include: { analytics: true },
      take: 10
    });

    const optimizations = [];

    for (const content of recentContent) {
      // Analyze performance and suggest optimizations
      const optimization = await this.aiGenerator.optimizeContent(
        {
          content: content.content as any,
          platform: content.platforms[0],
          goals: ['engagement']
        },
        rule.createdBy,
        rule.organizationId
      );

      optimizations.push({
        contentId: content.id,
        optimization
      });
    }

    return {
      action: 'optimize_content',
      optimizations
    };
  }

  /**
   * Execute generate report action
   */
  private async executeGenerateReportAction(action: AutomationAction, rule: AutomationRule): Promise<any> {
    // Generate automated performance report
    const report = {
      generatedAt: new Date(),
      organizationId: rule.organizationId,
      period: '7_days',
      metrics: {
        totalPosts: 0,
        totalEngagement: 0,
        averageEngagementRate: 0,
        topPerformingContent: [],
        recommendations: []
      }
    };

    return {
      action: 'generate_report',
      report
    };
  }

  /**
   * Helper methods
   */
  private initializeEngine(): void {
    // Load active rules from database
    this.loadActiveRules();
    
    // Start automation scheduler
    this.startScheduler();
  }

  private async loadActiveRules(): Promise<void> {
    try {
      // This would load rules from database
      logger.info('Loading active automation rules...');
    } catch (error) {
      logger.error('Error loading automation rules:', error);
    }
  }

  private startScheduler(): void {
    // Start cron-like scheduler for automation rules
    setInterval(() => {
      this.processScheduledRules();
    }, 60000); // Check every minute
  }

  private async processScheduledRules(): Promise<void> {
    const now = new Date();
    
    for (const [ruleId, rule] of this.activeRules) {
      if (rule.trigger.type === 'schedule') {
        const shouldExecute = this.shouldExecuteScheduledRule(rule, now);
        if (shouldExecute) {
          this.executeRule(ruleId, 'scheduler').catch(error => {
            logger.error(`Error executing scheduled rule ${ruleId}:`, error);
          });
        }
      }
    }
  }

  private shouldExecuteScheduledRule(rule: AutomationRule, now: Date): boolean {
    // Check if rule should be executed based on schedule
    // This is a simplified implementation
    if (!rule.lastExecuted) return true;
    
    const timeSinceLastExecution = now.getTime() - rule.lastExecuted.getTime();
    const oneHour = 60 * 60 * 1000;
    
    return timeSinceLastExecution > oneHour;
  }

  private async evaluateConditions(conditions: AutomationCondition[], organizationId: string): Promise<boolean> {
    for (const condition of conditions) {
      const result = await this.evaluateCondition(condition, organizationId);
      if (!result) return false;
    }
    return true;
  }

  private async evaluateCondition(condition: AutomationCondition, organizationId: string): Promise<boolean> {
    // Evaluate individual condition
    // This is a simplified implementation
    return true;
  }

  private generateRuleId(): string {
    return `rule_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private validateRule(rule: AutomationRule): void {
    if (!rule.name || !rule.organizationId) {
      throw new Error('Rule name and organization ID are required');
    }
    
    if (!rule.trigger || !rule.actions || rule.actions.length === 0) {
      throw new Error('Rule must have a trigger and at least one action');
    }
  }

  private async saveRule(rule: AutomationRule): Promise<void> {
    // Save rule to database
    logger.debug(`Saving automation rule: ${rule.id}`);
  }

  private async updateRule(rule: AutomationRule): Promise<void> {
    // Update rule in database
    logger.debug(`Updating automation rule: ${rule.id}`);
  }

  private async scheduleRule(rule: AutomationRule): Promise<void> {
    // Schedule rule for execution
    logger.debug(`Scheduling automation rule: ${rule.id}`);
  }

  private async saveExecution(execution: AutomationExecution): Promise<void> {
    // Save execution log to database
    logger.debug(`Saving execution log for rule: ${execution.ruleId}`);
  }

  private calculateSchedule(frequency: number): string {
    // Convert frequency to cron expression
    const postsPerDay = frequency / 7;
    if (postsPerDay >= 1) {
      return '0 9 * * *'; // Daily at 9 AM
    } else {
      const interval = Math.ceil(7 / frequency);
      return `0 9 */${interval} * *`; // Every N days at 9 AM
    }
  }

  private calculateNextOptimalTime(optimalTimes: string[]): Date {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    
    for (const timeStr of optimalTimes) {
      const [hours, minutes] = timeStr.split(':').map(Number);
      const optimalTime = new Date(today.getTime() + hours * 60 * 60 * 1000 + minutes * 60 * 1000);
      
      if (optimalTime > now) {
        return optimalTime;
      }
    }
    
    // If no optimal time today, schedule for tomorrow
    const tomorrow = new Date(today.getTime() + 24 * 60 * 60 * 1000);
    const [hours, minutes] = optimalTimes[0].split(':').map(Number);
    return new Date(tomorrow.getTime() + hours * 60 * 60 * 1000 + minutes * 60 * 1000);
  }

  private async getDefaultProjectId(organizationId: string): Promise<string> {
    // Get or create default project for automated content
    const defaultProject = await this.prisma.project.findFirst({
      where: {
        organizationId,
        name: 'Automated Content'
      }
    });

    if (defaultProject) {
      return defaultProject.id;
    }

    // Create default project
    const newProject = await this.prisma.project.create({
      data: {
        name: 'Automated Content',
        description: 'Content created by automation rules',
        organizationId,
        status: 'ACTIVE',
        createdBy: 'system'
      }
    });

    return newProject.id;
  }
}
