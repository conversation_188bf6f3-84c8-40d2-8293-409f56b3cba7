# Database
DATABASE_URL="postgresql://username:password@localhost:5432/socialhub_dev"

# Redis
REDIS_URL="redis://localhost:6379"

# JWT
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"
JWT_EXPIRES_IN="7d"

# Server
PORT=5000
NODE_ENV="development"
FRONTEND_URL="http://localhost:3000"

# Logging
LOG_LEVEL="info"

# AWS (for file storage)
AWS_ACCESS_KEY_ID=""
AWS_SECRET_ACCESS_KEY=""
AWS_REGION="us-east-1"
AWS_S3_BUCKET=""

# Email (Nodemailer)
EMAIL_HOST=""
EMAIL_PORT=587
EMAIL_USER=""
EMAIL_PASS=""
EMAIL_FROM=""

# Social Media APIs
FACEBOOK_APP_ID=""
FACEBOOK_APP_SECRET=""
INSTAGRAM_APP_ID=""
INSTAGRAM_APP_SECRET=""
TWITTER_API_KEY=""
TWITTER_API_SECRET=""
LINKEDIN_CLIENT_ID=""
LINKEDIN_CLIENT_SECRET=""
YOUTUBE_API_KEY=""
TIKTOK_CLIENT_ID=""
TIKTOK_CLIENT_SECRET=""

# Stripe (for payments)
STRIPE_SECRET_KEY=""
STRIPE_WEBHOOK_SECRET=""

# Google OAuth
GOOGLE_CLIENT_ID=""
GOOGLE_CLIENT_SECRET=""

# Monitoring
SENTRY_DSN=""
DATADOG_API_KEY=""
