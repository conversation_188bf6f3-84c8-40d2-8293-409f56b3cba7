import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '../ui/card';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { 
  BarChart3, 
  TrendingUp, 
  Eye, 
  Heart,
  Share2,
  MessageCircle,
  Users,
  Calendar,
  Download
} from 'lucide-react';
import { AnalyticsChart } from '../analytics/AnalyticsChart';

// Mock analytics data
const MOCK_ANALYTICS = {
  overview: {
    totalReach: 125000,
    totalEngagement: 8500,
    totalFollowers: 15600,
    engagementRate: 0.068,
    growthRate: 0.12
  },
  platformData: [
    {
      platform: 'INSTAGRAM',
      icon: '📷',
      color: '#E4405F',
      followers: 8500,
      posts: 15,
      engagement: 4200,
      reach: 65000,
      engagementRate: 0.074
    },
    {
      platform: 'FACEBOOK',
      icon: '👥',
      color: '#1877F2',
      followers: 4200,
      posts: 12,
      engagement: 2100,
      reach: 35000,
      engagementRate: 0.05
    },
    {
      platform: 'TWITTER',
      icon: '🐦',
      color: '#1DA1F2',
      followers: 2900,
      posts: 25,
      engagement: 2200,
      reach: 25000,
      engagementRate: 0.088
    }
  ],
  engagementTrend: [
    { date: '2024-01-01', value: 1200 },
    { date: '2024-01-02', value: 1350 },
    { date: '2024-01-03', value: 1100 },
    { date: '2024-01-04', value: 1450 },
    { date: '2024-01-05', value: 1600 },
    { date: '2024-01-06', value: 1380 },
    { date: '2024-01-07', value: 1750 }
  ],
  topContent: [
    {
      id: '1',
      title: 'New product launch announcement',
      platform: 'INSTAGRAM',
      engagement: 850,
      reach: 12000,
      publishedAt: '2024-01-05'
    },
    {
      id: '2',
      title: 'Behind the scenes video',
      platform: 'FACEBOOK',
      engagement: 620,
      reach: 8500,
      publishedAt: '2024-01-04'
    },
    {
      id: '3',
      title: 'Industry insights thread',
      platform: 'TWITTER',
      engagement: 480,
      reach: 6200,
      publishedAt: '2024-01-03'
    }
  ]
};

export const SocialMediaAnalytics: React.FC = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('7d');
  const [selectedPlatform, setSelectedPlatform] = useState('all');

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toString();
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    });
  };

  const getPlatformIcon = (platform: string) => {
    const platformData = MOCK_ANALYTICS.platformData.find(p => p.platform === platform);
    return platformData?.icon || '📱';
  };

  const filteredPlatformData = selectedPlatform === 'all' 
    ? MOCK_ANALYTICS.platformData 
    : MOCK_ANALYTICS.platformData.filter(p => p.platform === selectedPlatform);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Social Media Analytics</h2>
          <p className="text-gray-600 mt-1">
            Track your social media performance across all platforms
          </p>
        </div>
        <div className="flex space-x-3">
          <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
            </SelectContent>
          </Select>
          <Select value={selectedPlatform} onValueChange={setSelectedPlatform}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Platforms</SelectItem>
              <SelectItem value="INSTAGRAM">Instagram</SelectItem>
              <SelectItem value="FACEBOOK">Facebook</SelectItem>
              <SelectItem value="TWITTER">Twitter</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline">
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Overview Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Reach</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatNumber(MOCK_ANALYTICS.overview.totalReach)}
                </p>
              </div>
              <div className="p-3 bg-blue-100 rounded-full">
                <Eye className="w-6 h-6 text-blue-600" />
              </div>
            </div>
            <div className="mt-2 flex items-center text-sm text-green-600">
              <TrendingUp className="w-4 h-4 mr-1" />
              +{(MOCK_ANALYTICS.overview.growthRate * 100).toFixed(1)}%
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Engagement</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatNumber(MOCK_ANALYTICS.overview.totalEngagement)}
                </p>
              </div>
              <div className="p-3 bg-red-100 rounded-full">
                <Heart className="w-6 h-6 text-red-600" />
              </div>
            </div>
            <div className="mt-2 flex items-center text-sm text-green-600">
              <TrendingUp className="w-4 h-4 mr-1" />
              +8.2%
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Followers</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatNumber(MOCK_ANALYTICS.overview.totalFollowers)}
                </p>
              </div>
              <div className="p-3 bg-green-100 rounded-full">
                <Users className="w-6 h-6 text-green-600" />
              </div>
            </div>
            <div className="mt-2 flex items-center text-sm text-green-600">
              <TrendingUp className="w-4 h-4 mr-1" />
              +5.4%
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Engagement Rate</p>
                <p className="text-2xl font-bold text-gray-900">
                  {(MOCK_ANALYTICS.overview.engagementRate * 100).toFixed(1)}%
                </p>
              </div>
              <div className="p-3 bg-purple-100 rounded-full">
                <BarChart3 className="w-6 h-6 text-purple-600" />
              </div>
            </div>
            <div className="mt-2 flex items-center text-sm text-green-600">
              <TrendingUp className="w-4 h-4 mr-1" />
              +2.1%
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Posts</p>
                <p className="text-2xl font-bold text-gray-900">
                  {MOCK_ANALYTICS.platformData.reduce((sum, p) => sum + p.posts, 0)}
                </p>
              </div>
              <div className="p-3 bg-yellow-100 rounded-full">
                <Calendar className="w-6 h-6 text-yellow-600" />
              </div>
            </div>
            <div className="mt-2 flex items-center text-sm text-green-600">
              <TrendingUp className="w-4 h-4 mr-1" />
              +12.3%
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Engagement Trend Chart */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <BarChart3 className="w-5 h-5 mr-2" />
            Engagement Trend
          </CardTitle>
        </CardHeader>
        <CardContent>
          <AnalyticsChart 
            data={MOCK_ANALYTICS.engagementTrend}
            type="line"
            height={300}
            color="#3B82F6"
          />
        </CardContent>
      </Card>

      {/* Platform Performance */}
      <Card>
        <CardHeader>
          <CardTitle>Platform Performance</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {filteredPlatformData.map((platform) => (
              <div key={platform.platform} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div
                      className="w-10 h-10 rounded-full flex items-center justify-center text-white text-lg"
                      style={{ backgroundColor: platform.color }}
                    >
                      {platform.icon}
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">{platform.platform}</h4>
                      <p className="text-sm text-gray-600">{platform.posts} posts</p>
                    </div>
                  </div>
                  <Badge variant="outline">
                    {(platform.engagementRate * 100).toFixed(1)}%
                  </Badge>
                </div>
                
                <div className="space-y-3">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Followers:</span>
                    <span className="font-medium">{formatNumber(platform.followers)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Engagement:</span>
                    <span className="font-medium">{formatNumber(platform.engagement)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Reach:</span>
                    <span className="font-medium">{formatNumber(platform.reach)}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Top Performing Content */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <TrendingUp className="w-5 h-5 mr-2" />
            Top Performing Content
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {MOCK_ANALYTICS.topContent.map((content, index) => (
              <div key={content.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-semibold">
                    {index + 1}
                  </div>
                  <div className="flex items-center space-x-3">
                    <span className="text-lg">{getPlatformIcon(content.platform)}</span>
                    <div>
                      <h4 className="font-medium text-gray-900">{content.title}</h4>
                      <p className="text-sm text-gray-600">
                        {content.platform} • {formatDate(content.publishedAt)}
                      </p>
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-6 text-sm">
                  <div className="text-center">
                    <div className="flex items-center space-x-1 text-gray-600">
                      <Eye className="w-4 h-4" />
                      <span>{formatNumber(content.reach)}</span>
                    </div>
                    <p className="text-xs text-gray-500">Reach</p>
                  </div>
                  <div className="text-center">
                    <div className="flex items-center space-x-1 text-gray-600">
                      <Heart className="w-4 h-4" />
                      <span>{formatNumber(content.engagement)}</span>
                    </div>
                    <p className="text-xs text-gray-500">Engagement</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button variant="outline" className="h-16 flex flex-col items-center justify-center space-y-1">
              <Download className="w-5 h-5" />
              <span className="text-sm">Export Report</span>
            </Button>
            <Button variant="outline" className="h-16 flex flex-col items-center justify-center space-y-1">
              <Calendar className="w-5 h-5" />
              <span className="text-sm">Schedule Analysis</span>
            </Button>
            <Button variant="outline" className="h-16 flex flex-col items-center justify-center space-y-1">
              <Share2 className="w-5 h-5" />
              <span className="text-sm">Share Insights</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SocialMediaAnalytics;
