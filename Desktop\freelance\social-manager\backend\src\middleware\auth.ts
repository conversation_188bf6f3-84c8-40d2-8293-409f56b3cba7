import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { prisma } from '../config/database.js';
import { CustomError } from './errorHandler.js';

// User roles as constants since we're using strings in SQLite
const UserRole = {
  SUPER_ADMIN: 'SUPER_ADMIN',
  AGENCY_ADMIN: 'AGENCY_ADMIN',
  CLIENT_ADMIN: 'CLIENT_ADMIN',
  TEAM_MEMBER: 'TEAM_MEMBER',
  CLIENT_USER: 'CLIENT_USER',
} as const;

type UserRole = typeof UserRole[keyof typeof UserRole];

export interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    email: string;
    role: UserRole;
    organizationId: string;
  };
}

export const authMiddleware = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');

    if (!token) {
      throw new CustomError('Access denied. No token provided.', 401);
    }

    const jwtSecret = process.env.JWT_SECRET;
    if (!jwtSecret) {
      throw new CustomError('JWT secret not configured', 500);
    }

    const decoded = jwt.verify(token, jwtSecret) as any;
    
    // Fetch user from database to ensure they still exist and are active
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      select: {
        id: true,
        email: true,
        role: true,
        organizationId: true,
        isActive: true,
      },
    });

    if (!user || !user.isActive) {
      throw new CustomError('Invalid token or user not found', 401);
    }

    req.user = user;
    next();
  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      next(new CustomError('Invalid token', 401));
    } else {
      next(error);
    }
  }
};

export const requireRole = (roles: UserRole[]) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    if (!req.user) {
      return next(new CustomError('Authentication required', 401));
    }

    if (!roles.includes(req.user.role)) {
      return next(new CustomError('Insufficient permissions', 403));
    }

    next();
  };
};

export const requireSuperAdmin = requireRole([UserRole.SUPER_ADMIN]);
export const requireAgencyAdmin = requireRole([UserRole.SUPER_ADMIN, UserRole.AGENCY_ADMIN]);
export const requireClientAdmin = requireRole([
  UserRole.SUPER_ADMIN,
  UserRole.AGENCY_ADMIN,
  UserRole.CLIENT_ADMIN,
]);

export const requireSameOrganization = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    if (!req.user) {
      return next(new CustomError('Authentication required', 401));
    }

    const { organizationId } = req.params;

    if (organizationId && organizationId !== req.user.organizationId) {
      // Super admins can access any organization
      if (req.user.role !== UserRole.SUPER_ADMIN) {
        return next(new CustomError('Access denied to this organization', 403));
      }
    }

    next();
  } catch (error) {
    next(error);
  }
};

// Alias for compatibility with new routes
export const authenticateToken = authMiddleware;
