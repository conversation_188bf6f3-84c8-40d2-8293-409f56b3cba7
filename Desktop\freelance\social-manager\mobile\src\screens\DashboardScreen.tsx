import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  RefreshControl,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import { Card, Button, Avatar, Chip } from 'react-native-paper';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'react-native-chart-kit';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';

import { useAppDispatch, useAppSelector } from '../hooks/redux';
import { fetchDashboardData } from '../store/slices/dashboardSlice';
import { formatNumber } from '../utils/formatters';

const screenWidth = Dimensions.get('window').width;

const DashboardScreen: React.FC = () => {
  const navigation = useNavigation();
  const dispatch = useAppDispatch();
  const { data, loading, error } = useAppSelector(state => state.dashboard);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      await dispatch(fetchDashboardData()).unwrap();
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadDashboardData();
    setRefreshing(false);
  };

  const chartConfig = {
    backgroundColor: '#6366f1',
    backgroundGradientFrom: '#6366f1',
    backgroundGradientTo: '#8b5cf6',
    decimalPlaces: 0,
    color: (opacity = 1) => `rgba(255, 255, 255, ${opacity})`,
    labelColor: (opacity = 1) => `rgba(255, 255, 255, ${opacity})`,
    style: {
      borderRadius: 16,
    },
    propsForDots: {
      r: '6',
      strokeWidth: '2',
      stroke: '#ffa726',
    },
  };

  const engagementData = {
    labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
    datasets: [
      {
        data: data?.weeklyEngagement || [20, 45, 28, 80, 99, 43, 65],
        color: (opacity = 1) => `rgba(255, 255, 255, ${opacity})`,
        strokeWidth: 2,
      },
    ],
  };

  const platformData = {
    labels: ['Instagram', 'Facebook', 'Twitter', 'LinkedIn'],
    datasets: [
      {
        data: data?.platformEngagement || [85, 70, 60, 45],
      },
    ],
  };

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      {/* Header */}
      <View style={styles.header}>
        <View>
          <Text style={styles.greeting}>Good morning!</Text>
          <Text style={styles.userName}>{data?.user?.name || 'User'}</Text>
        </View>
        <TouchableOpacity
          onPress={() => navigation.navigate('Notifications' as never)}
        >
          <Avatar.Icon size={40} icon="bell" style={styles.notificationIcon} />
        </TouchableOpacity>
      </View>

      {/* Quick Stats */}
      <View style={styles.statsContainer}>
        <Card style={styles.statCard}>
          <Card.Content style={styles.statContent}>
            <Ionicons name="heart" size={24} color="#ef4444" />
            <Text style={styles.statNumber}>
              {formatNumber(data?.totalEngagement || 12500)}
            </Text>
            <Text style={styles.statLabel}>Total Engagement</Text>
          </Card.Content>
        </Card>

        <Card style={styles.statCard}>
          <Card.Content style={styles.statContent}>
            <Ionicons name="eye" size={24} color="#3b82f6" />
            <Text style={styles.statNumber}>
              {formatNumber(data?.totalReach || 45000)}
            </Text>
            <Text style={styles.statLabel}>Total Reach</Text>
          </Card.Content>
        </Card>

        <Card style={styles.statCard}>
          <Card.Content style={styles.statContent}>
            <Ionicons name="people" size={24} color="#10b981" />
            <Text style={styles.statNumber}>
              +{formatNumber(data?.newFollowers || 1250)}
            </Text>
            <Text style={styles.statLabel}>New Followers</Text>
          </Card.Content>
        </Card>
      </View>

      {/* Quick Actions */}
      <Card style={styles.card}>
        <Card.Title title="Quick Actions" />
        <Card.Content>
          <View style={styles.quickActions}>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => navigation.navigate('CreateContent' as never)}
            >
              <Ionicons name="add-circle" size={32} color="#6366f1" />
              <Text style={styles.actionText}>Create Post</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => navigation.navigate('Camera' as never)}
            >
              <Ionicons name="camera" size={32} color="#6366f1" />
              <Text style={styles.actionText}>Take Photo</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => navigation.navigate('AI Assistant' as never)}
            >
              <Ionicons name="bulb" size={32} color="#6366f1" />
              <Text style={styles.actionText}>AI Assistant</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => navigation.navigate('Analytics' as never)}
            >
              <Ionicons name="bar-chart" size={32} color="#6366f1" />
              <Text style={styles.actionText}>Analytics</Text>
            </TouchableOpacity>
          </View>
        </Card.Content>
      </Card>

      {/* Weekly Engagement Chart */}
      <Card style={styles.card}>
        <Card.Title title="Weekly Engagement" />
        <Card.Content>
          <LineChart
            data={engagementData}
            width={screenWidth - 60}
            height={220}
            chartConfig={chartConfig}
            bezier
            style={styles.chart}
          />
        </Card.Content>
      </Card>

      {/* Platform Performance */}
      <Card style={styles.card}>
        <Card.Title title="Platform Performance" />
        <Card.Content>
          <BarChart
            data={platformData}
            width={screenWidth - 60}
            height={220}
            chartConfig={chartConfig}
            style={styles.chart}
            yAxisLabel=""
            yAxisSuffix="%"
          />
        </Card.Content>
      </Card>

      {/* Recent Activity */}
      <Card style={styles.card}>
        <Card.Title title="Recent Activity" />
        <Card.Content>
          {data?.recentActivity?.map((activity, index) => (
            <View key={index} style={styles.activityItem}>
              <Avatar.Icon size={40} icon={activity.icon} />
              <View style={styles.activityContent}>
                <Text style={styles.activityTitle}>{activity.title}</Text>
                <Text style={styles.activityTime}>{activity.time}</Text>
              </View>
              <Chip mode="outlined" compact>
                {activity.platform}
              </Chip>
            </View>
          )) || (
            <Text style={styles.noData}>No recent activity</Text>
          )}
        </Card.Content>
      </Card>

      {/* AI Recommendations */}
      <Card style={styles.card}>
        <Card.Title title="AI Recommendations" />
        <Card.Content>
          {data?.aiRecommendations?.map((recommendation, index) => (
            <View key={index} style={styles.recommendationItem}>
              <Ionicons name="bulb-outline" size={20} color="#f59e0b" />
              <Text style={styles.recommendationText}>
                {recommendation.text}
              </Text>
            </View>
          )) || (
            <Text style={styles.noData}>No recommendations available</Text>
          )}
        </Card.Content>
      </Card>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    paddingTop: 50,
    backgroundColor: '#6366f1',
  },
  greeting: {
    fontSize: 16,
    color: '#e2e8f0',
  },
  userName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#ffffff',
  },
  notificationIcon: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 20,
    marginTop: -30,
  },
  statCard: {
    flex: 1,
    marginHorizontal: 5,
    elevation: 4,
  },
  statContent: {
    alignItems: 'center',
    paddingVertical: 10,
  },
  statNumber: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 5,
  },
  statLabel: {
    fontSize: 12,
    color: '#64748b',
    textAlign: 'center',
  },
  card: {
    margin: 20,
    marginTop: 10,
    elevation: 4,
  },
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    flexWrap: 'wrap',
  },
  actionButton: {
    alignItems: 'center',
    padding: 15,
    width: '22%',
  },
  actionText: {
    fontSize: 12,
    marginTop: 5,
    textAlign: 'center',
    color: '#374151',
  },
  chart: {
    marginVertical: 8,
    borderRadius: 16,
  },
  activityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
  },
  activityContent: {
    flex: 1,
    marginLeft: 15,
  },
  activityTitle: {
    fontSize: 14,
    fontWeight: '500',
  },
  activityTime: {
    fontSize: 12,
    color: '#64748b',
  },
  recommendationItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    paddingVertical: 8,
  },
  recommendationText: {
    flex: 1,
    marginLeft: 10,
    fontSize: 14,
    lineHeight: 20,
  },
  noData: {
    textAlign: 'center',
    color: '#64748b',
    fontStyle: 'italic',
    paddingVertical: 20,
  },
});

export default DashboardScreen;
