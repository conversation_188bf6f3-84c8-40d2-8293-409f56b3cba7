import express from 'express';
import { PrismaClient } from '@prisma/client';
import { SocialMediaIntegrationService } from '../services/socialMediaIntegrations.js';
import { ContentSchedulerService } from '../services/contentScheduler.js';
import { authMiddleware } from '../middleware/auth.js';
import { validateRequest } from '../middleware/validation.js';
import { logger } from '../utils/logger.js';

const router = express.Router();
const prisma = new PrismaClient();
const socialMediaService = new SocialMediaIntegrationService(prisma);
const schedulerService = new ContentSchedulerService(prisma, socialMediaService);

/**
 * @route POST /api/social-media/connect
 * @desc Connect a social media account
 * @access Private
 */
router.post('/connect', authMiddleware, async (req, res) => {
  try {
    const { platform, authCode } = req.body;
    const { organizationId } = req.user;

    if (!platform || !authCode) {
      return res.status(400).json({
        success: false,
        message: 'Platform and authorization code are required'
      });
    }

    const account = await socialMediaService.connectAccount(
      platform,
      authCode,
      organizationId,
      req.user.id
    );

    res.json({
      success: true,
      message: `Successfully connected ${platform} account`,
      data: account
    });
  } catch (error) {
    logger.error('Error connecting social media account:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to connect social media account'
    });
  }
});

/**
 * @route GET /api/social-media/accounts
 * @desc Get connected social media accounts
 * @access Private
 */
router.get('/accounts', authMiddleware, async (req, res) => {
  try {
    const { organizationId } = req.user;
    
    const accounts = await socialMediaService.getConnectedAccounts(organizationId);

    res.json({
      success: true,
      data: accounts
    });
  } catch (error) {
    logger.error('Error getting connected accounts:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get connected accounts'
    });
  }
});

/**
 * @route DELETE /api/social-media/accounts/:accountId
 * @desc Disconnect a social media account
 * @access Private
 */
router.delete('/accounts/:accountId', authMiddleware, async (req, res) => {
  try {
    const { accountId } = req.params;
    
    await socialMediaService.disconnectAccount(accountId);

    res.json({
      success: true,
      message: 'Account disconnected successfully'
    });
  } catch (error) {
    logger.error('Error disconnecting account:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to disconnect account'
    });
  }
});

/**
 * @route POST /api/social-media/publish
 * @desc Publish content to social media platforms
 * @access Private
 */
router.post('/publish', authMiddleware, async (req, res) => {
  try {
    const { content, mediaUrls, platforms, scheduledAt } = req.body;
    const { organizationId } = req.user;

    if (!content || !platforms || platforms.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Content and platforms are required'
      });
    }

    const publishRequest = {
      content,
      mediaUrls: mediaUrls || [],
      platforms,
      scheduledAt: scheduledAt ? new Date(scheduledAt) : undefined,
      organizationId,
      userId: req.user.id
    };

    const result = await socialMediaService.publishContent(publishRequest);

    res.json({
      success: true,
      message: scheduledAt ? 'Content scheduled successfully' : 'Content published successfully',
      data: result
    });
  } catch (error) {
    logger.error('Error publishing content:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to publish content'
    });
  }
});

/**
 * @route POST /api/social-media/schedule
 * @desc Schedule content for publishing
 * @access Private
 */
router.post('/schedule', authMiddleware, async (req, res) => {
  try {
    const { content, mediaUrls, platforms, scheduledAt, timezone, recurring } = req.body;
    const { organizationId } = req.user;

    if (!content || !platforms || !scheduledAt) {
      return res.status(400).json({
        success: false,
        message: 'Content, platforms, and scheduled time are required'
      });
    }

    const scheduleRequest = {
      id: '',
      content,
      mediaUrls: mediaUrls || [],
      platforms,
      scheduledAt: new Date(scheduledAt),
      organizationId,
      userId: req.user.id,
      timezone: timezone || 'UTC',
      recurring
    };

    const contentId = await schedulerService.scheduleContent(scheduleRequest);

    res.json({
      success: true,
      message: 'Content scheduled successfully',
      data: { contentId }
    });
  } catch (error) {
    logger.error('Error scheduling content:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to schedule content'
    });
  }
});

/**
 * @route POST /api/social-media/bulk-schedule
 * @desc Bulk schedule multiple content items
 * @access Private
 */
router.post('/bulk-schedule', authMiddleware, async (req, res) => {
  try {
    const { contents, schedule } = req.body;
    const { organizationId } = req.user;

    if (!contents || !Array.isArray(contents) || contents.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Contents array is required'
      });
    }

    const bulkRequest = {
      contents,
      schedule: {
        ...schedule,
        startDate: new Date(schedule.startDate),
        endDate: schedule.endDate ? new Date(schedule.endDate) : undefined
      },
      organizationId,
      userId: req.user.id
    };

    const contentIds = await schedulerService.bulkScheduleContent(bulkRequest);

    res.json({
      success: true,
      message: `Successfully scheduled ${contentIds.length} content items`,
      data: { contentIds }
    });
  } catch (error) {
    logger.error('Error bulk scheduling content:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to bulk schedule content'
    });
  }
});

/**
 * @route GET /api/social-media/scheduled
 * @desc Get scheduled content
 * @access Private
 */
router.get('/scheduled', authMiddleware, async (req, res) => {
  try {
    const { organizationId } = req.user;
    const { startDate, endDate } = req.query;

    const scheduledContent = await schedulerService.getScheduledContent(
      organizationId,
      startDate ? new Date(startDate as string) : undefined,
      endDate ? new Date(endDate as string) : undefined
    );

    res.json({
      success: true,
      data: scheduledContent
    });
  } catch (error) {
    logger.error('Error getting scheduled content:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get scheduled content'
    });
  }
});

/**
 * @route PUT /api/social-media/scheduled/:contentId
 * @desc Reschedule content
 * @access Private
 */
router.put('/scheduled/:contentId', authMiddleware, async (req, res) => {
  try {
    const { contentId } = req.params;
    const { scheduledAt } = req.body;

    if (!scheduledAt) {
      return res.status(400).json({
        success: false,
        message: 'New scheduled time is required'
      });
    }

    await schedulerService.rescheduleContent(contentId, new Date(scheduledAt));

    res.json({
      success: true,
      message: 'Content rescheduled successfully'
    });
  } catch (error) {
    logger.error('Error rescheduling content:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to reschedule content'
    });
  }
});

/**
 * @route DELETE /api/social-media/scheduled/:contentId
 * @desc Cancel scheduled content
 * @access Private
 */
router.delete('/scheduled/:contentId', authMiddleware, async (req, res) => {
  try {
    const { contentId } = req.params;
    
    await schedulerService.cancelScheduledContent(contentId);

    res.json({
      success: true,
      message: 'Scheduled content cancelled successfully'
    });
  } catch (error) {
    logger.error('Error cancelling scheduled content:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to cancel scheduled content'
    });
  }
});

/**
 * @route GET /api/social-media/optimal-times
 * @desc Get optimal posting times for platforms
 * @access Private
 */
router.get('/optimal-times', authMiddleware, async (req, res) => {
  try {
    const { organizationId } = req.user;
    const { platforms } = req.query;

    const platformList = platforms ? (platforms as string).split(',') : undefined;
    const optimalTimes = await schedulerService.getOptimalPostingTimes(platformList || [], organizationId);

    res.json({
      success: true,
      data: optimalTimes
    });
  } catch (error) {
    logger.error('Error getting optimal posting times:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get optimal posting times'
    });
  }
});

/**
 * @route GET /api/social-media/analytics/:contentId
 * @desc Get analytics for specific content
 * @access Private
 */
router.get('/analytics/:contentId', authMiddleware, async (req, res) => {
  try {
    const { contentId } = req.params;
    
    const analytics = await socialMediaService.getContentAnalytics(contentId);

    res.json({
      success: true,
      data: analytics
    });
  } catch (error) {
    logger.error('Error getting content analytics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get content analytics'
    });
  }
});

/**
 * @route GET /api/social-media/queue-stats
 * @desc Get queue statistics
 * @access Private
 */
router.get('/queue-stats', authMiddleware, async (req, res) => {
  try {
    const stats = await schedulerService.getQueueStats();

    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    logger.error('Error getting queue stats:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get queue statistics'
    });
  }
});

export default router;
