import { useState, useEffect, useCallback } from 'react';
import {
  billingApi,
  SubscriptionPlan,
  CurrentSubscription,
  UsageMetrics,
  Invoice,
  PaymentMethod,
  PaymentIntent
} from '../api/billing';

export const useBilling = () => {
  const [plans, setPlans] = useState<SubscriptionPlan[]>([]);
  const [currentSubscription, setCurrentSubscription] = useState<CurrentSubscription | null>(null);
  const [usage, setUsage] = useState<UsageMetrics | null>(null);
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load subscription plans
  const loadPlans = useCallback(async () => {
    try {
      setLoading(true);
      const data = await billingApi.getSubscriptionPlans();
      setPlans(data);
      setError(null);
    } catch (err) {
      setError('Failed to load subscription plans');
      console.error('Error loading plans:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  // Load current subscription
  const loadCurrentSubscription = useCallback(async () => {
    try {
      setLoading(true);
      const data = await billingApi.getCurrentSubscription();
      setCurrentSubscription(data);
      if (data) {
        setUsage(data.usage);
      }
      setError(null);
    } catch (err) {
      setError('Failed to load current subscription');
      console.error('Error loading subscription:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  // Load usage metrics
  const loadUsage = useCallback(async (period?: string) => {
    try {
      const data = await billingApi.getUsageMetrics(period);
      setUsage(data);
      setError(null);
    } catch (err) {
      setError('Failed to load usage metrics');
      console.error('Error loading usage:', err);
    }
  }, []);

  // Load invoices
  const loadInvoices = useCallback(async (limit: number = 10) => {
    try {
      setLoading(true);
      const data = await billingApi.getInvoices(limit);
      setInvoices(data);
      setError(null);
    } catch (err) {
      setError('Failed to load invoices');
      console.error('Error loading invoices:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  // Load payment methods
  const loadPaymentMethods = useCallback(async () => {
    try {
      setLoading(true);
      const data = await billingApi.getPaymentMethods();
      setPaymentMethods(data);
      setError(null);
    } catch (err) {
      setError('Failed to load payment methods');
      console.error('Error loading payment methods:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  // Create payment intent
  const createPaymentIntent = useCallback(async (planId: string): Promise<PaymentIntent | null> => {
    try {
      setLoading(true);
      const data = await billingApi.createPaymentIntent(planId);
      setError(null);
      return data;
    } catch (err) {
      setError('Failed to create payment intent');
      console.error('Error creating payment intent:', err);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  // Create subscription
  const createSubscription = useCallback(async (planId: string, paymentMethodId: string) => {
    try {
      setLoading(true);
      const result = await billingApi.createSubscription(planId, paymentMethodId);
      await loadCurrentSubscription(); // Reload subscription
      setError(null);
      return result;
    } catch (err) {
      setError('Failed to create subscription');
      console.error('Error creating subscription:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [loadCurrentSubscription]);

  // Update subscription
  const updateSubscription = useCallback(async (planId: string) => {
    try {
      setLoading(true);
      await billingApi.updateSubscription(planId);
      await loadCurrentSubscription(); // Reload subscription
      setError(null);
      return true;
    } catch (err) {
      setError('Failed to update subscription');
      console.error('Error updating subscription:', err);
      return false;
    } finally {
      setLoading(false);
    }
  }, [loadCurrentSubscription]);

  // Cancel subscription
  const cancelSubscription = useCallback(async (immediate: boolean = false) => {
    try {
      setLoading(true);
      await billingApi.cancelSubscription(immediate);
      await loadCurrentSubscription(); // Reload subscription
      setError(null);
      return true;
    } catch (err) {
      setError('Failed to cancel subscription');
      console.error('Error cancelling subscription:', err);
      return false;
    } finally {
      setLoading(false);
    }
  }, [loadCurrentSubscription]);

  // Add payment method
  const addPaymentMethod = useCallback(async (paymentMethodId: string) => {
    try {
      setLoading(true);
      await billingApi.addPaymentMethod(paymentMethodId);
      await loadPaymentMethods(); // Reload payment methods
      setError(null);
      return true;
    } catch (err) {
      setError('Failed to add payment method');
      console.error('Error adding payment method:', err);
      return false;
    } finally {
      setLoading(false);
    }
  }, [loadPaymentMethods]);

  // Set default payment method
  const setDefaultPaymentMethod = useCallback(async (paymentMethodId: string) => {
    try {
      setLoading(true);
      await billingApi.setDefaultPaymentMethod(paymentMethodId);
      await loadPaymentMethods(); // Reload payment methods
      setError(null);
      return true;
    } catch (err) {
      setError('Failed to set default payment method');
      console.error('Error setting default payment method:', err);
      return false;
    } finally {
      setLoading(false);
    }
  }, [loadPaymentMethods]);

  // Remove payment method
  const removePaymentMethod = useCallback(async (paymentMethodId: string) => {
    try {
      setLoading(true);
      await billingApi.removePaymentMethod(paymentMethodId);
      await loadPaymentMethods(); // Reload payment methods
      setError(null);
      return true;
    } catch (err) {
      setError('Failed to remove payment method');
      console.error('Error removing payment method:', err);
      return false;
    } finally {
      setLoading(false);
    }
  }, [loadPaymentMethods]);

  // Utility functions
  const getCurrentPlan = useCallback(() => {
    return currentSubscription?.plan || null;
  }, [currentSubscription]);

  const isSubscribed = useCallback(() => {
    return currentSubscription?.subscription.status === 'active';
  }, [currentSubscription]);

  const getUsagePercentage = useCallback((metric: keyof UsageMetrics) => {
    if (!usage || !currentSubscription) return 0;
    
    const used = usage[metric] as number;
    const limit = (currentSubscription.plan.limits as any)[metric] as number;
    
    return billingApi.formatUsagePercentage(used, limit);
  }, [usage, currentSubscription]);

  const getUsageStatus = useCallback((metric: keyof UsageMetrics) => {
    if (!usage || !currentSubscription) return 'low';
    
    const used = usage[metric] as number;
    const limit = (currentSubscription.plan.limits as any)[metric] as number;
    
    return billingApi.getUsageStatus(used, limit);
  }, [usage, currentSubscription]);

  const isFeatureAvailable = useCallback((featureName: string) => {
    if (!currentSubscription) return false;
    return billingApi.isFeatureIncluded(currentSubscription.plan, featureName);
  }, [currentSubscription]);

  const getDaysUntilBilling = useCallback(() => {
    if (!currentSubscription) return 0;
    return billingApi.getDaysUntilBilling(currentSubscription);
  }, [currentSubscription]);

  const getNextBillingAmount = useCallback(() => {
    if (!currentSubscription) return 0;
    return currentSubscription.plan.price;
  }, [currentSubscription]);

  const canUpgrade = useCallback((targetPlanId: string) => {
    if (!currentSubscription) return true;
    
    const targetPlan = plans.find(p => p.id === targetPlanId);
    if (!targetPlan) return false;
    
    return targetPlan.price > currentSubscription.plan.price;
  }, [currentSubscription, plans]);

  const canDowngrade = useCallback((targetPlanId: string) => {
    if (!currentSubscription) return false;
    
    const targetPlan = plans.find(p => p.id === targetPlanId);
    if (!targetPlan) return false;
    
    return targetPlan.price < currentSubscription.plan.price;
  }, [currentSubscription, plans]);

  // Load initial data
  useEffect(() => {
    loadPlans();
    loadCurrentSubscription();
    loadInvoices();
    loadPaymentMethods();
  }, [loadPlans, loadCurrentSubscription, loadInvoices, loadPaymentMethods]);

  return {
    // State
    plans,
    currentSubscription,
    usage,
    invoices,
    paymentMethods,
    loading,
    error,

    // Actions
    createPaymentIntent,
    createSubscription,
    updateSubscription,
    cancelSubscription,
    addPaymentMethod,
    setDefaultPaymentMethod,
    removePaymentMethod,

    // Data loading
    loadPlans,
    loadCurrentSubscription,
    loadUsage,
    loadInvoices,
    loadPaymentMethods,

    // Utility functions
    getCurrentPlan,
    isSubscribed,
    getUsagePercentage,
    getUsageStatus,
    isFeatureAvailable,
    getDaysUntilBilling,
    getNextBillingAmount,
    canUpgrade,
    canDowngrade,

    // Helper functions from API
    formatPrice: billingApi.formatPrice,
    getPlanBadgeColor: billingApi.getPlanBadgeColor,
    getUsageColor: billingApi.getUsageColor,
    calculateAnnualSavings: billingApi.calculateAnnualSavings,
    calculateAnnualSavingsPercentage: billingApi.calculateAnnualSavingsPercentage,
    comparePlans: billingApi.comparePlans,
    getInvoiceStatusColor: billingApi.getInvoiceStatusColor,
    getInvoiceStatusText: billingApi.getInvoiceStatusText
  };
};

export default useBilling;
