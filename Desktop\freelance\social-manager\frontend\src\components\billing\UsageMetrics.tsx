import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '../ui/card';
import { Progress } from '../ui/progress';
import { Badge } from '../ui/badge';
import { 
  Users, 
  Zap, 
  TrendingUp, 
  Brain,
  Database,
  Clock,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';

// Mock usage data
const MOCK_USAGE = {
  users: { current: 3, limit: 5 },
  socialAccounts: { current: 7, limit: 10 },
  postsPublished: { current: 45, limit: 100 },
  aiGenerations: { current: 28, limit: 50 },
  storageUsed: { current: 2.3, limit: 10 }, // GB
  apiCalls: { current: 1250, limit: 5000 }
};

const CURRENT_PLAN = {
  name: 'Professional',
  price: 79,
  interval: 'month'
};

export const UsageMetrics: React.FC = () => {
  const getUsagePercentage = (current: number, limit: number) => {
    return Math.min((current / limit) * 100, 100);
  };

  const getUsageStatus = (current: number, limit: number) => {
    const percentage = getUsagePercentage(current, limit);
    if (percentage >= 100) return 'exceeded';
    if (percentage >= 80) return 'high';
    if (percentage >= 50) return 'medium';
    return 'low';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'exceeded':
        return 'text-red-600 bg-red-100';
      case 'high':
        return 'text-orange-600 bg-orange-100';
      case 'medium':
        return 'text-yellow-600 bg-yellow-100';
      default:
        return 'text-green-600 bg-green-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'exceeded':
        return <AlertTriangle className="w-4 h-4" />;
      case 'high':
        return <AlertTriangle className="w-4 h-4" />;
      default:
        return <CheckCircle className="w-4 h-4" />;
    }
  };

  const formatNumber = (num: number) => {
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toString();
  };

  const usageMetrics = [
    {
      name: 'Team Members',
      icon: Users,
      current: MOCK_USAGE.users.current,
      limit: MOCK_USAGE.users.limit,
      unit: 'users',
      description: 'Active team members in your organization'
    },
    {
      name: 'Social Accounts',
      icon: Zap,
      current: MOCK_USAGE.socialAccounts.current,
      limit: MOCK_USAGE.socialAccounts.limit,
      unit: 'accounts',
      description: 'Connected social media accounts'
    },
    {
      name: 'Posts Published',
      icon: TrendingUp,
      current: MOCK_USAGE.postsPublished.current,
      limit: MOCK_USAGE.postsPublished.limit,
      unit: 'posts',
      description: 'Posts published this month'
    },
    {
      name: 'AI Generations',
      icon: Brain,
      current: MOCK_USAGE.aiGenerations.current,
      limit: MOCK_USAGE.aiGenerations.limit,
      unit: 'generations',
      description: 'AI-powered content generations this month'
    },
    {
      name: 'Storage Used',
      icon: Database,
      current: MOCK_USAGE.storageUsed.current,
      limit: MOCK_USAGE.storageUsed.limit,
      unit: 'GB',
      description: 'Media storage used'
    },
    {
      name: 'API Calls',
      icon: Clock,
      current: MOCK_USAGE.apiCalls.current,
      limit: MOCK_USAGE.apiCalls.limit,
      unit: 'calls',
      description: 'API calls made this month'
    }
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Usage Overview</h2>
          <p className="text-gray-600 mt-1">
            Monitor your current usage against your {CURRENT_PLAN.name} plan limits
          </p>
        </div>
        <Badge variant="outline" className="text-sm">
          {CURRENT_PLAN.name} Plan - ${CURRENT_PLAN.price}/{CURRENT_PLAN.interval}
        </Badge>
      </div>

      {/* Usage Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {usageMetrics.map((metric) => {
          const percentage = getUsagePercentage(metric.current, metric.limit);
          const status = getUsageStatus(metric.current, metric.limit);
          const Icon = metric.icon;

          return (
            <Card key={metric.name} className="relative overflow-hidden">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Icon className="w-5 h-5 text-gray-600" />
                    <CardTitle className="text-lg">{metric.name}</CardTitle>
                  </div>
                  <Badge className={`${getStatusColor(status)} flex items-center text-xs`}>
                    {getStatusIcon(status)}
                    <span className="ml-1 capitalize">{status}</span>
                  </Badge>
                </div>
                <p className="text-sm text-gray-600">{metric.description}</p>
              </CardHeader>
              
              <CardContent className="space-y-4">
                {/* Usage Numbers */}
                <div className="flex items-center justify-between">
                  <span className="text-2xl font-bold text-gray-900">
                    {metric.unit === 'GB' ? metric.current.toFixed(1) : formatNumber(metric.current)}
                  </span>
                  <span className="text-sm text-gray-500">
                    of {metric.unit === 'GB' ? metric.limit.toFixed(0) : formatNumber(metric.limit)} {metric.unit}
                  </span>
                </div>

                {/* Progress Bar */}
                <div className="space-y-2">
                  <Progress 
                    value={percentage} 
                    className={`h-3 ${
                      status === 'exceeded' ? 'bg-red-100' : 
                      status === 'high' ? 'bg-orange-100' : 
                      status === 'medium' ? 'bg-yellow-100' : 'bg-green-100'
                    }`}
                  />
                  <div className="flex justify-between text-xs text-gray-500">
                    <span>{percentage.toFixed(1)}% used</span>
                    <span>
                      {metric.unit === 'GB' 
                        ? `${(metric.limit - metric.current).toFixed(1)} GB remaining`
                        : `${metric.limit - metric.current} remaining`
                      }
                    </span>
                  </div>
                </div>

                {/* Status Message */}
                {status === 'exceeded' && (
                  <div className="text-xs text-red-600 bg-red-50 p-2 rounded">
                    ⚠️ You've exceeded your limit. Consider upgrading your plan.
                  </div>
                )}
                {status === 'high' && (
                  <div className="text-xs text-orange-600 bg-orange-50 p-2 rounded">
                    📊 You're approaching your limit. Monitor usage closely.
                  </div>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Usage Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Usage Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {usageMetrics.filter(m => getUsageStatus(m.current, m.limit) === 'low').length}
              </div>
              <div className="text-sm text-green-700">Within Limits</div>
            </div>
            <div className="text-center p-4 bg-yellow-50 rounded-lg">
              <div className="text-2xl font-bold text-yellow-600">
                {usageMetrics.filter(m => getUsageStatus(m.current, m.limit) === 'medium').length}
              </div>
              <div className="text-sm text-yellow-700">Moderate Usage</div>
            </div>
            <div className="text-center p-4 bg-orange-50 rounded-lg">
              <div className="text-2xl font-bold text-orange-600">
                {usageMetrics.filter(m => getUsageStatus(m.current, m.limit) === 'high').length}
              </div>
              <div className="text-sm text-orange-700">High Usage</div>
            </div>
            <div className="text-center p-4 bg-red-50 rounded-lg">
              <div className="text-2xl font-bold text-red-600">
                {usageMetrics.filter(m => getUsageStatus(m.current, m.limit) === 'exceeded').length}
              </div>
              <div className="text-sm text-red-700">Over Limit</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Usage Trends */}
      <Card>
        <CardHeader>
          <CardTitle>Usage Trends</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <TrendingUp className="w-5 h-5 text-blue-600" />
                <div>
                  <p className="font-medium text-blue-800">Posts Published</p>
                  <p className="text-sm text-blue-600">+15% from last month</p>
                </div>
              </div>
              <div className="text-blue-600 font-semibold">📈</div>
            </div>
            
            <div className="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <Brain className="w-5 h-5 text-purple-600" />
                <div>
                  <p className="font-medium text-purple-800">AI Generations</p>
                  <p className="text-sm text-purple-600">+25% from last month</p>
                </div>
              </div>
              <div className="text-purple-600 font-semibold">🚀</div>
            </div>
            
            <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <Users className="w-5 h-5 text-green-600" />
                <div>
                  <p className="font-medium text-green-800">Team Growth</p>
                  <p className="text-sm text-green-600">Added 1 new member</p>
                </div>
              </div>
              <div className="text-green-600 font-semibold">👥</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Recommendations */}
      <Card className="bg-gradient-to-r from-blue-50 to-purple-50">
        <CardHeader>
          <CardTitle className="text-blue-800">Usage Recommendations</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mt-0.5">
                <span className="text-blue-600 text-xs">💡</span>
              </div>
              <div>
                <p className="text-sm font-medium text-blue-800">
                  Optimize AI Usage
                </p>
                <p className="text-xs text-blue-600">
                  You're using AI generations efficiently. Consider upgrading to get more AI-powered features.
                </p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center mt-0.5">
                <span className="text-purple-600 text-xs">📊</span>
              </div>
              <div>
                <p className="text-sm font-medium text-purple-800">
                  Scale Your Team
                </p>
                <p className="text-xs text-purple-600">
                  You have room for 2 more team members. Invite colleagues to collaborate.
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default UsageMetrics;
