import { useState, useEffect, useCallback } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  analyticsApi,
  AnalyticsReport,
  RealtimeMetrics,
  CompetitorAnalysis,
  PredictiveInsights,
  ContentPerformanceData,
  CustomDashboard,
  DashboardWidget,
  DashboardLayout,
  PlatformMetrics
} from '../api/analytics';

// Main analytics hook
export const useAnalytics = () => {
  const [dateRange, setDateRange] = useState({
    startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0]
  });
  const [selectedPlatforms, setSelectedPlatforms] = useState<string[]>([]);

  return {
    dateRange,
    setDateRange,
    selectedPlatforms,
    setSelectedPlatforms
  };
};

// Analytics overview hook
export const useAnalyticsOverview = (period: string = '30d') => {
  return useQuery({
    queryKey: ['analytics', 'overview', period],
    queryFn: () => analyticsApi.getOverview(period),
    refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes
    staleTime: 2 * 60 * 1000 // Consider data stale after 2 minutes
  });
};

// Comprehensive analytics report hook
export const useAnalyticsReport = (
  startDate: string,
  endDate: string,
  platforms?: string[]
) => {
  return useQuery({
    queryKey: ['analytics', 'report', startDate, endDate, platforms],
    queryFn: () => analyticsApi.generateReport(startDate, endDate, platforms),
    enabled: !!startDate && !!endDate,
    refetchInterval: 10 * 60 * 1000, // Refetch every 10 minutes
    staleTime: 5 * 60 * 1000
  });
};

// Real-time metrics hook
export const useRealtimeMetrics = () => {
  return useQuery({
    queryKey: ['analytics', 'realtime'],
    queryFn: () => analyticsApi.getRealtimeMetrics(),
    refetchInterval: 30 * 1000, // Refetch every 30 seconds
    staleTime: 15 * 1000 // Consider data stale after 15 seconds
  });
};

// Competitor analysis hook
export const useCompetitorAnalysis = (
  competitors: string[],
  platforms: string[]
) => {
  return useQuery({
    queryKey: ['analytics', 'competitors', competitors, platforms],
    queryFn: () => analyticsApi.getCompetitorAnalysis(competitors, platforms),
    enabled: competitors.length > 0 && platforms.length > 0,
    refetchInterval: 60 * 60 * 1000, // Refetch every hour
    staleTime: 30 * 60 * 1000 // Consider data stale after 30 minutes
  });
};

// Predictive insights hook
export const usePredictiveInsights = (
  timeframe: 'week' | 'month' | 'quarter' = 'month'
) => {
  return useQuery({
    queryKey: ['analytics', 'predictions', timeframe],
    queryFn: () => analyticsApi.getPredictiveInsights(timeframe),
    refetchInterval: 24 * 60 * 60 * 1000, // Refetch daily
    staleTime: 12 * 60 * 60 * 1000 // Consider data stale after 12 hours
  });
};

// Content performance hook
export const useContentPerformance = (
  startDate: string,
  endDate: string,
  platform?: string,
  sortBy?: string,
  limit?: number
) => {
  return useQuery({
    queryKey: ['analytics', 'content-performance', startDate, endDate, platform, sortBy, limit],
    queryFn: () => analyticsApi.getContentPerformance(startDate, endDate, platform, sortBy, limit),
    enabled: !!startDate && !!endDate,
    refetchInterval: 15 * 60 * 1000, // Refetch every 15 minutes
    staleTime: 5 * 60 * 1000
  });
};

// Platform analytics hook
export const usePlatformAnalytics = (
  period: string = '30d',
  platforms?: string[]
) => {
  return useQuery({
    queryKey: ['analytics', 'platforms', period, platforms],
    queryFn: () => analyticsApi.getPlatformAnalytics(period, platforms),
    refetchInterval: 10 * 60 * 1000, // Refetch every 10 minutes
    staleTime: 5 * 60 * 1000
  });
};

// Custom dashboards hook
export const useCustomDashboards = () => {
  const queryClient = useQueryClient();

  const dashboardsQuery = useQuery({
    queryKey: ['analytics', 'dashboards'],
    queryFn: () => analyticsApi.getCustomDashboards(),
    staleTime: 10 * 60 * 1000 // Consider data stale after 10 minutes
  });

  const createDashboardMutation = useMutation({
    mutationFn: ({ name, widgets, layout }: {
      name: string;
      widgets: DashboardWidget[];
      layout: DashboardLayout;
    }) => analyticsApi.createCustomDashboard(name, widgets, layout),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['analytics', 'dashboards'] });
    }
  });

  return {
    dashboards: dashboardsQuery.data || [],
    isLoading: dashboardsQuery.isLoading,
    error: dashboardsQuery.error,
    createDashboard: createDashboardMutation.mutate,
    isCreating: createDashboardMutation.isPending
  };
};

// Export report hook
export const useExportReport = () => {
  const [isExporting, setIsExporting] = useState(false);
  const [exportError, setExportError] = useState<string | null>(null);

  const exportReport = useCallback(async (
    startDate: string,
    endDate: string,
    format: 'pdf' | 'excel' | 'csv',
    platforms?: string[]
  ) => {
    try {
      setIsExporting(true);
      setExportError(null);

      const result = await analyticsApi.exportReport(startDate, endDate, format, platforms);

      // Trigger download
      const link = document.createElement('a');
      link.href = result.downloadUrl;
      link.download = result.filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      return result;
    } catch (error) {
      setExportError('Failed to export report');
      console.error('Export error:', error);
      throw error;
    } finally {
      setIsExporting(false);
    }
  }, []);

  return {
    exportReport,
    isExporting,
    exportError
  };
};

// Analytics utilities hook
export const useAnalyticsUtils = () => {
  const formatMetric = useCallback((
    value: number,
    type: 'number' | 'percentage' | 'currency' = 'number'
  ) => {
    return analyticsApi.formatMetric(value, type);
  }, []);

  const getPerformanceColor = useCallback((performance: 'excellent' | 'good' | 'average' | 'poor') => {
    return analyticsApi.getPerformanceColor(performance);
  }, []);

  const getTrendIcon = useCallback((trend: 'up' | 'down' | 'stable') => {
    return analyticsApi.getTrendIcon(trend);
  }, []);

  const calculateGrowthRate = useCallback((current: number, previous: number) => {
    return analyticsApi.calculateGrowthRate(current, previous);
  }, []);

  const getEngagementRateCategory = useCallback((rate: number) => {
    return analyticsApi.getEngagementRateCategory(rate);
  }, []);

  const getBestPostingTime = useCallback((platformMetrics: PlatformMetrics) => {
    return analyticsApi.getBestPostingTime(platformMetrics);
  }, []);

  return {
    formatMetric,
    getPerformanceColor,
    getTrendIcon,
    calculateGrowthRate,
    getEngagementRateCategory,
    getBestPostingTime
  };
};

// Legacy hooks for backward compatibility
export const useContentAnalytics = (contentId: string) => {
  return useQuery({
    queryKey: ['analytics', 'content', contentId],
    queryFn: async () => {
      // This would need to be implemented in the new API
      // For now, return mock data or throw an error
      throw new Error('Use useContentPerformance instead');
    },
    enabled: !!contentId,
    refetchInterval: 10 * 60 * 1000
  });
};
