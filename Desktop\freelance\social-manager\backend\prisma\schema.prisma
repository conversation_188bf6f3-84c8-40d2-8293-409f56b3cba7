// Simplified Prisma schema for SQLite development
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

// Core Models
model Organization {
  id               String   @id @default(cuid())
  name             String
  slug             String   @unique
  planType         String   @default("STARTER") // STARTER, PROF<PERSON><PERSON>ONAL, AGENCY, ENTERPRISE
  status           String   @default("ACTIVE")  // ACTIVE, SUSPENDED, CANCELLED
  settings         String?  // JSON string
  stripeCustomerId String?  // Stripe customer ID for billing
  contactEmail     String?  // Contact email for billing
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  // Relations
  users         User[]
  projects      Project[]
  integrations  SocialMediaIntegration[]
  subscriptions Subscription[]
  usageRecords  UsageRecord[]
  dashboards    CustomDashboard[]

  @@map("organizations")
}

model User {
  id             String   @id @default(cuid())
  email          String   @unique
  password       String?
  firstName      String
  lastName       String
  avatar         String?
  role           String   @default("TEAM_MEMBER") // SUPER_ADMIN, AGEN<PERSON>Y_ADMIN, <PERSON><PERSON>IENT_ADMIN, TEAM_MEMBER, CLIENT_USER
  permissions    String?  // JSON string
  isActive       Boolean  @default(true)
  emailVerified  Boolean  @default(false)
  lastLoginAt    DateTime?
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // Foreign Keys
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  // Relations
  projects       ProjectMember[]
  contentItems   ContentItem[]
  createdProjects Project[] @relation("ProjectCreator")

  @@map("users")
}

model Project {
  id          String        @id @default(cuid())
  name        String
  description String?
  type        String        @default("CAMPAIGN") // CAMPAIGN, ONGOING, ONE_TIME
  status      String        @default("PLANNING") // PLANNING, IN_PROGRESS, REVIEW, COMPLETED, CANCELLED
  budget      Float?
  deadline    DateTime?
  startDate   DateTime?
  endDate     DateTime?
  metadata    String?       // JSON string
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt

  // Foreign Keys
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  createdById    String
  createdBy      User @relation("ProjectCreator", fields: [createdById], references: [id])

  // Relations
  members      ProjectMember[]
  contentItems ContentItem[]

  @@map("projects")
}

model ProjectMember {
  id        String      @id @default(cuid())
  role      String      @default("MEMBER") // OWNER, MANAGER, MEMBER, VIEWER
  joinedAt  DateTime    @default(now())

  // Foreign Keys
  projectId String
  project   Project @relation(fields: [projectId], references: [id], onDelete: Cascade)
  userId    String
  user      User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([projectId, userId])
  @@map("project_members")
}

model ContentItem {
  id          String        @id @default(cuid())
  title       String
  description String?
  type        String        // POST, STORY, REEL, VIDEO, ARTICLE, CAROUSEL
  platforms   String        // JSON array of platforms
  contentData String        // JSON string
  status      String        @default("DRAFT") // DRAFT, REVIEW, APPROVED, SCHEDULED, PUBLISHED, FAILED
  scheduledAt DateTime?
  publishedAt DateTime?
  metadata    String?       // JSON string for additional data
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt

  // Foreign Keys
  projectId String
  project   Project @relation(fields: [projectId], references: [id], onDelete: Cascade)
  createdById String
  createdBy   User @relation(fields: [createdById], references: [id])

  // Relations
  analytics ContentAnalytics?

  @@map("content_items")
}

model SocialMediaIntegration {
  id            String    @id @default(cuid())
  platform      String    // INSTAGRAM, FACEBOOK, TWITTER, LINKEDIN, TIKTOK, YOUTUBE, PINTEREST
  accountName   String
  accountId     String
  accessToken   String
  refreshToken  String?
  expiresAt     DateTime?
  isActive      Boolean   @default(true)
  settings      String?   // JSON string for platform-specific settings
  lastTestedAt  DateTime?
  connectedBy   String
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // Foreign Keys
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@unique([organizationId, platform, accountId])
  @@map("social_media_integrations")
}

// Analytics Models
model ContentAnalytics {
  id          String   @id @default(cuid())
  metrics     String   // JSON string with analytics data
  lastUpdated DateTime @default(now())
  createdAt   DateTime @default(now())

  // Foreign Keys
  contentId String      @unique
  content   ContentItem @relation(fields: [contentId], references: [id], onDelete: Cascade)

  @@map("content_analytics")
}

model CustomDashboard {
  id        String   @id @default(cuid())
  name      String
  widgets   String   // JSON string with widget configurations
  layout    String   // JSON string with layout configuration
  isDefault Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Foreign Keys
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@map("custom_dashboards")
}

// Billing Models
model SubscriptionPlan {
  id            String   @id @default(cuid())
  name          String
  description   String
  price         Float
  currency      String   @default("usd")
  interval      String   // month, year
  features      String   // JSON string
  limits        String   // JSON string
  stripePriceId String   @unique
  isActive      Boolean  @default(true)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relations
  subscriptions Subscription[]

  @@map("subscription_plans")
}

model Subscription {
  id                   String   @id @default(cuid())
  stripeSubscriptionId String   @unique
  status               String   // active, canceled, incomplete, etc.
  currentPeriodStart   DateTime
  currentPeriodEnd     DateTime
  cancelAtPeriodEnd    Boolean  @default(false)
  createdAt            DateTime @default(now())
  updatedAt            DateTime @updatedAt

  // Foreign Keys
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  planId         String
  plan           SubscriptionPlan @relation(fields: [planId], references: [id])

  @@map("subscriptions")
}

model UsageRecord {
  id       String @id @default(cuid())
  period   String // YYYY-MM format
  metric   String // users, social_accounts, posts_published, ai_generations, etc.
  quantity Int    @default(0)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Foreign Keys
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@unique([organizationId, period, metric])
  @@map("usage_records")
}
