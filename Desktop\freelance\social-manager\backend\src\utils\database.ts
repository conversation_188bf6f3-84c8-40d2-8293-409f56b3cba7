import { PrismaClient } from '@prisma/client';
import { logger } from './logger.js';

/**
 * Database optimization utilities
 */
export class DatabaseOptimizer {
  private prisma: PrismaClient;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }

  /**
   * Create optimized database indexes
   */
  async createOptimizedIndexes(): Promise<void> {
    try {
      logger.info('Creating optimized database indexes...');

      // User indexes
      await this.createIndexIfNotExists('users', 'idx_users_email', ['email']);
      await this.createIndexIfNotExists('users', 'idx_users_organization_id', ['organization_id']);
      await this.createIndexIfNotExists('users', 'idx_users_role', ['role']);
      await this.createIndexIfNotExists('users', 'idx_users_created_at', ['created_at']);

      // Organization indexes
      await this.createIndexIfNotExists('organizations', 'idx_organizations_plan_type', ['plan_type']);
      await this.createIndexIfNotExists('organizations', 'idx_organizations_created_at', ['created_at']);

      // Project indexes
      await this.createIndexIfNotExists('projects', 'idx_projects_organization_id', ['organization_id']);
      await this.createIndexIfNotExists('projects', 'idx_projects_status', ['status']);
      await this.createIndexIfNotExists('projects', 'idx_projects_deadline', ['deadline']);
      await this.createIndexIfNotExists('projects', 'idx_projects_created_at', ['created_at']);
      await this.createIndexIfNotExists('projects', 'idx_projects_org_status', ['organization_id', 'status']);

      // Content indexes
      await this.createIndexIfNotExists('content_items', 'idx_content_project_id', ['project_id']);
      await this.createIndexIfNotExists('content_items', 'idx_content_status', ['status']);
      await this.createIndexIfNotExists('content_items', 'idx_content_scheduled_at', ['scheduled_at']);
      await this.createIndexIfNotExists('content_items', 'idx_content_type', ['type']);
      await this.createIndexIfNotExists('content_items', 'idx_content_platforms', ['platforms']);
      await this.createIndexIfNotExists('content_items', 'idx_content_project_status', ['project_id', 'status']);

      // Analytics indexes
      await this.createIndexIfNotExists('analytics_events', 'idx_analytics_content_id', ['content_id']);
      await this.createIndexIfNotExists('analytics_events', 'idx_analytics_platform', ['platform']);
      await this.createIndexIfNotExists('analytics_events', 'idx_analytics_event_type', ['event_type']);
      await this.createIndexIfNotExists('analytics_events', 'idx_analytics_timestamp', ['timestamp']);
      await this.createIndexIfNotExists('analytics_events', 'idx_analytics_content_platform', ['content_id', 'platform']);
      await this.createIndexIfNotExists('analytics_events', 'idx_analytics_platform_timestamp', ['platform', 'timestamp']);

      // Social accounts indexes
      await this.createIndexIfNotExists('social_accounts', 'idx_social_organization_id', ['organization_id']);
      await this.createIndexIfNotExists('social_accounts', 'idx_social_platform', ['platform']);
      await this.createIndexIfNotExists('social_accounts', 'idx_social_is_active', ['is_active']);
      await this.createIndexIfNotExists('social_accounts', 'idx_social_org_platform', ['organization_id', 'platform']);

      // Subscriptions indexes
      await this.createIndexIfNotExists('subscriptions', 'idx_subscriptions_organization_id', ['organization_id']);
      await this.createIndexIfNotExists('subscriptions', 'idx_subscriptions_status', ['status']);
      await this.createIndexIfNotExists('subscriptions', 'idx_subscriptions_plan_id', ['plan_id']);
      await this.createIndexIfNotExists('subscriptions', 'idx_subscriptions_current_period_end', ['current_period_end']);

      logger.info('Database indexes created successfully');
    } catch (error) {
      logger.error('Error creating database indexes:', error);
      throw error;
    }
  }

  /**
   * Create index if it doesn't exist
   */
  private async createIndexIfNotExists(
    tableName: string,
    indexName: string,
    columns: string[]
  ): Promise<void> {
    try {
      const columnList = columns.join(', ');
      const query = `
        CREATE INDEX IF NOT EXISTS ${indexName} 
        ON ${tableName} (${columnList})
      `;
      
      await this.prisma.$executeRawUnsafe(query);
      logger.debug(`Index ${indexName} created on ${tableName}(${columnList})`);
    } catch (error) {
      logger.error(`Error creating index ${indexName}:`, error);
    }
  }

  /**
   * Analyze query performance
   */
  async analyzeQuery(query: string): Promise<any> {
    try {
      const explainQuery = `EXPLAIN ANALYZE ${query}`;
      const result = await this.prisma.$queryRawUnsafe(explainQuery);
      return result;
    } catch (error) {
      logger.error('Error analyzing query:', error);
      throw error;
    }
  }

  /**
   * Get database statistics
   */
  async getDatabaseStats(): Promise<any> {
    try {
      const stats = await this.prisma.$queryRaw`
        SELECT 
          schemaname,
          tablename,
          attname,
          n_distinct,
          correlation
        FROM pg_stats 
        WHERE schemaname = 'public'
        ORDER BY tablename, attname
      `;
      return stats;
    } catch (error) {
      logger.error('Error getting database stats:', error);
      throw error;
    }
  }

  /**
   * Get slow queries
   */
  async getSlowQueries(limit: number = 10): Promise<any> {
    try {
      const slowQueries = await this.prisma.$queryRaw`
        SELECT 
          query,
          calls,
          total_time,
          mean_time,
          rows
        FROM pg_stat_statements 
        ORDER BY mean_time DESC 
        LIMIT ${limit}
      `;
      return slowQueries;
    } catch (error) {
      logger.error('Error getting slow queries:', error);
      throw error;
    }
  }

  /**
   * Optimize table statistics
   */
  async updateTableStatistics(): Promise<void> {
    try {
      logger.info('Updating table statistics...');
      
      const tables = [
        'users',
        'organizations',
        'projects',
        'content_items',
        'analytics_events',
        'social_accounts',
        'subscriptions'
      ];

      for (const table of tables) {
        await this.prisma.$executeRawUnsafe(`ANALYZE ${table}`);
        logger.debug(`Updated statistics for table: ${table}`);
      }

      logger.info('Table statistics updated successfully');
    } catch (error) {
      logger.error('Error updating table statistics:', error);
      throw error;
    }
  }

  /**
   * Clean up old analytics data
   */
  async cleanupOldAnalytics(daysToKeep: number = 90): Promise<number> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

      const result = await this.prisma.analyticsEvent.deleteMany({
        where: {
          timestamp: {
            lt: cutoffDate
          }
        }
      });

      logger.info(`Cleaned up ${result.count} old analytics records`);
      return result.count;
    } catch (error) {
      logger.error('Error cleaning up old analytics:', error);
      throw error;
    }
  }

  /**
   * Get table sizes
   */
  async getTableSizes(): Promise<any> {
    try {
      const tableSizes = await this.prisma.$queryRaw`
        SELECT 
          tablename,
          pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
          pg_total_relation_size(schemaname||'.'||tablename) as size_bytes
        FROM pg_tables 
        WHERE schemaname = 'public'
        ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
      `;
      return tableSizes;
    } catch (error) {
      logger.error('Error getting table sizes:', error);
      throw error;
    }
  }

  /**
   * Vacuum and analyze all tables
   */
  async vacuumAnalyze(): Promise<void> {
    try {
      logger.info('Running VACUUM ANALYZE...');
      await this.prisma.$executeRawUnsafe('VACUUM ANALYZE');
      logger.info('VACUUM ANALYZE completed successfully');
    } catch (error) {
      logger.error('Error running VACUUM ANALYZE:', error);
      throw error;
    }
  }
}

/**
 * Query optimization utilities
 */
export class QueryOptimizer {
  /**
   * Optimize pagination queries
   */
  static getPaginationParams(page?: number, limit?: number) {
    const pageNum = Math.max(1, page || 1);
    const limitNum = Math.min(100, Math.max(1, limit || 20)); // Max 100 items per page
    const skip = (pageNum - 1) * limitNum;

    return {
      skip,
      take: limitNum,
      page: pageNum,
      limit: limitNum
    };
  }

  /**
   * Build optimized where clause for text search
   */
  static buildTextSearchWhere(searchTerm?: string, fields: string[] = []) {
    if (!searchTerm || fields.length === 0) {
      return {};
    }

    const searchConditions = fields.map(field => ({
      [field]: {
        contains: searchTerm,
        mode: 'insensitive' as const
      }
    }));

    return {
      OR: searchConditions
    };
  }

  /**
   * Build date range filter
   */
  static buildDateRangeWhere(
    field: string,
    startDate?: string | Date,
    endDate?: string | Date
  ) {
    const where: any = {};

    if (startDate || endDate) {
      where[field] = {};
      
      if (startDate) {
        where[field].gte = new Date(startDate);
      }
      
      if (endDate) {
        where[field].lte = new Date(endDate);
      }
    }

    return where;
  }

  /**
   * Build optimized include for related data
   */
  static buildOptimizedInclude(includes: string[] = []) {
    const includeObj: any = {};

    includes.forEach(include => {
      switch (include) {
        case 'user':
          includeObj.user = {
            select: {
              id: true,
              email: true,
              firstName: true,
              lastName: true,
              role: true
            }
          };
          break;
        case 'organization':
          includeObj.organization = {
            select: {
              id: true,
              name: true,
              planType: true
            }
          };
          break;
        case 'project':
          includeObj.project = {
            select: {
              id: true,
              name: true,
              status: true,
              deadline: true
            }
          };
          break;
        default:
          includeObj[include] = true;
      }
    });

    return includeObj;
  }
}
