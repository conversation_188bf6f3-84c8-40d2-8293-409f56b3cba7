/**
 * Performance monitoring and optimization utilities
 */

interface PerformanceMetric {
  name: string;
  value: number;
  timestamp: number;
  metadata?: Record<string, any>;
}

interface WebVitalsMetric {
  name: 'CLS' | 'FID' | 'FCP' | 'LCP' | 'TTFB';
  value: number;
  rating: 'good' | 'needs-improvement' | 'poor';
  delta: number;
  id: string;
}

class PerformanceMonitor {
  private metrics: PerformanceMetric[] = [];
  private observers: PerformanceObserver[] = [];

  constructor() {
    this.initializeObservers();
  }

  /**
   * Initialize performance observers
   */
  private initializeObservers(): void {
    if (typeof window === 'undefined') return;

    // Long Task Observer
    if ('PerformanceObserver' in window) {
      try {
        const longTaskObserver = new PerformanceObserver((list) => {
          list.getEntries().forEach((entry) => {
            this.recordMetric({
              name: 'long-task',
              value: entry.duration,
              timestamp: entry.startTime,
              metadata: {
                type: entry.entryType,
                name: entry.name
              }
            });
          });
        });
        longTaskObserver.observe({ entryTypes: ['longtask'] });
        this.observers.push(longTaskObserver);
      } catch (error) {
        console.warn('Long task observer not supported:', error);
      }

      // Navigation Observer
      try {
        const navigationObserver = new PerformanceObserver((list) => {
          list.getEntries().forEach((entry) => {
            const navEntry = entry as PerformanceNavigationTiming;
            this.recordMetric({
              name: 'navigation-timing',
              value: navEntry.loadEventEnd - navEntry.navigationStart,
              timestamp: navEntry.navigationStart,
              metadata: {
                domContentLoaded: navEntry.domContentLoadedEventEnd - navEntry.navigationStart,
                firstPaint: navEntry.responseEnd - navEntry.navigationStart,
                domInteractive: navEntry.domInteractive - navEntry.navigationStart
              }
            });
          });
        });
        navigationObserver.observe({ entryTypes: ['navigation'] });
        this.observers.push(navigationObserver);
      } catch (error) {
        console.warn('Navigation observer not supported:', error);
      }

      // Resource Observer
      try {
        const resourceObserver = new PerformanceObserver((list) => {
          list.getEntries().forEach((entry) => {
            const resourceEntry = entry as PerformanceResourceTiming;
            this.recordMetric({
              name: 'resource-timing',
              value: resourceEntry.responseEnd - resourceEntry.startTime,
              timestamp: resourceEntry.startTime,
              metadata: {
                name: resourceEntry.name,
                type: resourceEntry.initiatorType,
                size: resourceEntry.transferSize,
                cached: resourceEntry.transferSize === 0
              }
            });
          });
        });
        resourceObserver.observe({ entryTypes: ['resource'] });
        this.observers.push(resourceObserver);
      } catch (error) {
        console.warn('Resource observer not supported:', error);
      }
    }
  }

  /**
   * Record a performance metric
   */
  recordMetric(metric: PerformanceMetric): void {
    this.metrics.push(metric);
    
    // Keep only last 1000 metrics to prevent memory leaks
    if (this.metrics.length > 1000) {
      this.metrics = this.metrics.slice(-1000);
    }

    // Send to analytics if configured
    this.sendToAnalytics(metric);
  }

  /**
   * Measure function execution time
   */
  measureFunction<T>(name: string, fn: () => T): T {
    const start = performance.now();
    const result = fn();
    const end = performance.now();

    this.recordMetric({
      name: `function-${name}`,
      value: end - start,
      timestamp: start
    });

    return result;
  }

  /**
   * Measure async function execution time
   */
  async measureAsyncFunction<T>(name: string, fn: () => Promise<T>): Promise<T> {
    const start = performance.now();
    const result = await fn();
    const end = performance.now();

    this.recordMetric({
      name: `async-function-${name}`,
      value: end - start,
      timestamp: start
    });

    return result;
  }

  /**
   * Measure component render time
   */
  measureRender(componentName: string, renderFn: () => void): void {
    const start = performance.now();
    renderFn();
    const end = performance.now();

    this.recordMetric({
      name: `render-${componentName}`,
      value: end - start,
      timestamp: start
    });
  }

  /**
   * Get metrics by name
   */
  getMetrics(name?: string): PerformanceMetric[] {
    if (name) {
      return this.metrics.filter(metric => metric.name === name);
    }
    return [...this.metrics];
  }

  /**
   * Get average metric value
   */
  getAverageMetric(name: string): number {
    const metrics = this.getMetrics(name);
    if (metrics.length === 0) return 0;
    
    const sum = metrics.reduce((acc, metric) => acc + metric.value, 0);
    return sum / metrics.length;
  }

  /**
   * Clear all metrics
   */
  clearMetrics(): void {
    this.metrics = [];
  }

  /**
   * Send metrics to analytics service
   */
  private sendToAnalytics(metric: PerformanceMetric): void {
    // Only send important metrics to avoid spam
    const importantMetrics = [
      'navigation-timing',
      'long-task',
      'web-vitals'
    ];

    if (importantMetrics.some(name => metric.name.includes(name))) {
      // Send to your analytics service
      // This could be Google Analytics, DataDog, or your own service
      if (window.gtag) {
        window.gtag('event', 'performance_metric', {
          metric_name: metric.name,
          metric_value: metric.value,
          custom_parameter: metric.metadata
        });
      }
    }
  }

  /**
   * Disconnect all observers
   */
  disconnect(): void {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
  }
}

/**
 * Web Vitals monitoring
 */
export function initWebVitals(): void {
  if (typeof window === 'undefined') return;

  // Import web-vitals dynamically to avoid SSR issues
  import('web-vitals').then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {
    const sendToAnalytics = (metric: WebVitalsMetric) => {
      performanceMonitor.recordMetric({
        name: `web-vitals-${metric.name}`,
        value: metric.value,
        timestamp: Date.now(),
        metadata: {
          rating: metric.rating,
          delta: metric.delta,
          id: metric.id
        }
      });
    };

    getCLS(sendToAnalytics);
    getFID(sendToAnalytics);
    getFCP(sendToAnalytics);
    getLCP(sendToAnalytics);
    getTTFB(sendToAnalytics);
  }).catch(error => {
    console.warn('Web Vitals not available:', error);
  });
}

/**
 * Image optimization utilities
 */
export class ImageOptimizer {
  /**
   * Create optimized image URL with WebP support
   */
  static getOptimizedImageUrl(
    originalUrl: string,
    width?: number,
    height?: number,
    quality: number = 80
  ): string {
    if (!originalUrl) return '';

    // Check if browser supports WebP
    const supportsWebP = this.supportsWebP();
    
    // If it's already an optimized URL, return as is
    if (originalUrl.includes('w_') || originalUrl.includes('h_')) {
      return originalUrl;
    }

    // Build optimization parameters
    const params = new URLSearchParams();
    if (width) params.append('w', width.toString());
    if (height) params.append('h', height.toString());
    params.append('q', quality.toString());
    if (supportsWebP) params.append('f', 'webp');

    // If using a CDN like Cloudinary or similar
    if (originalUrl.includes('cloudinary.com')) {
      const parts = originalUrl.split('/upload/');
      if (parts.length === 2) {
        return `${parts[0]}/upload/c_fill,${params.toString()}/${parts[1]}`;
      }
    }

    // For other URLs, append query parameters
    const separator = originalUrl.includes('?') ? '&' : '?';
    return `${originalUrl}${separator}${params.toString()}`;
  }

  /**
   * Check if browser supports WebP
   */
  static supportsWebP(): boolean {
    if (typeof window === 'undefined') return false;
    
    const canvas = document.createElement('canvas');
    canvas.width = 1;
    canvas.height = 1;
    return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
  }

  /**
   * Lazy load images with intersection observer
   */
  static lazyLoadImages(selector: string = 'img[data-src]'): void {
    if (typeof window === 'undefined' || !('IntersectionObserver' in window)) {
      return;
    }

    const images = document.querySelectorAll(selector);
    
    const imageObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target as HTMLImageElement;
          const src = img.dataset.src;
          
          if (src) {
            img.src = src;
            img.removeAttribute('data-src');
            imageObserver.unobserve(img);
          }
        }
      });
    });

    images.forEach(img => imageObserver.observe(img));
  }
}

/**
 * Bundle size analyzer
 */
export class BundleAnalyzer {
  /**
   * Analyze bundle size and report large chunks
   */
  static analyzeBundleSize(): void {
    if (typeof window === 'undefined') return;

    // Get all script tags
    const scripts = Array.from(document.querySelectorAll('script[src]'));
    
    scripts.forEach(script => {
      const src = (script as HTMLScriptElement).src;
      if (src && !src.includes('node_modules')) {
        // Estimate size based on script loading time
        const start = performance.now();
        
        fetch(src, { method: 'HEAD' })
          .then(response => {
            const size = response.headers.get('content-length');
            const end = performance.now();
            
            if (size && parseInt(size) > 100000) { // > 100KB
              console.warn(`Large bundle detected: ${src} (${(parseInt(size) / 1024).toFixed(2)}KB)`);
            }
            
            performanceMonitor.recordMetric({
              name: 'bundle-load-time',
              value: end - start,
              timestamp: start,
              metadata: {
                src,
                size: size ? parseInt(size) : null
              }
            });
          })
          .catch(() => {
            // Ignore errors for external scripts
          });
      }
    });
  }
}

// Global performance monitor instance
export const performanceMonitor = new PerformanceMonitor();

// Initialize on module load
if (typeof window !== 'undefined') {
  initWebVitals();
  
  // Analyze bundle size after page load
  window.addEventListener('load', () => {
    setTimeout(() => {
      BundleAnalyzer.analyzeBundleSize();
      ImageOptimizer.lazyLoadImages();
    }, 1000);
  });
}

// Cleanup on page unload
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    performanceMonitor.disconnect();
  });
}
