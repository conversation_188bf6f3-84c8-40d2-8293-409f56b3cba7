export interface ContentItem {
  id: string
  title: string
  description?: string
  type: ContentType
  platforms: Platform[]
  contentData: any
  status: ContentStatus
  scheduledAt?: string
  publishedAt?: string
  createdAt: string
  updatedAt: string
  projectId: string
  createdById: string
  createdBy: {
    id: string
    firstName: string
    lastName: string
    avatar?: string
  }
  assets?: Asset[]
  publications?: Publication[]
}

export interface Asset {
  id: string
  filename: string
  originalName: string
  mimeType: string
  size: number
  url: string
  thumbnailUrl?: string
  metadata?: any
  createdAt: string
}

export interface Publication {
  id: string
  platform: Platform
  platformPostId?: string
  status: PublicationStatus
  scheduledAt: string
  publishedAt?: string
  error?: string
  metadata?: any
  socialAccount: {
    id: string
    platform: Platform
    username: string
    displayName?: string
  }
}

export type ContentType = 'POST' | 'STORY' | 'REEL' | 'VIDEO' | 'ARTICLE' | 'CAROUSEL';

export type ContentStatus = 'DRAFT' | 'REVIEW' | 'APPROVED' | 'SCHEDULED' | 'PUBLISHED' | 'FAILED';

export type Platform = 'INSTAGRAM' | 'FACEBOOK' | 'TWITTER' | 'LINKEDIN' | 'TIKTOK' | 'YOUTUBE' | 'PINTEREST';

export type PublicationStatus = 'SCHEDULED' | 'PUBLISHING' | 'PUBLISHED' | 'FAILED' | 'CANCELLED';

export interface CreateContentData {
  title: string
  description?: string
  type: ContentType
  platforms: Platform[]
  contentData: any
  projectId: string
  scheduledAt?: string
}

export interface UpdateContentData extends Partial<CreateContentData> {
  status?: ContentStatus
}
