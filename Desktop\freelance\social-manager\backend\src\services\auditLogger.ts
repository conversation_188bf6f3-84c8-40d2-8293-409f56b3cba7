import { PrismaClient } from '@prisma/client';
import { Request } from 'express';
import { logger } from '../utils/logger.js';
import { CacheService } from '../config/redis.js';

export enum AuditAction {
  // Authentication
  LOGIN = 'LOGIN',
  LOGOUT = 'LOGOUT',
  LOGIN_FAILED = 'LOGIN_FAILED',
  PASSWORD_CHANGED = 'PASSWORD_CHANGED',
  TWO_FACTOR_ENABLED = 'TWO_FACTOR_ENABLED',
  TWO_FACTOR_DISABLED = 'TWO_FACTOR_DISABLED',

  // User Management
  USER_CREATED = 'USER_CREATED',
  USER_UPDATED = 'USER_UPDATED',
  USER_DELETED = 'USER_DELETED',
  USER_ROLE_CHANGED = 'USER_ROLE_CHANGED',
  USER_SUSPENDED = 'USER_SUSPENDED',
  USER_ACTIVATED = 'USER_ACTIVATED',

  // Organization Management
  ORGANIZATION_CREATED = 'ORGANIZATION_CREATED',
  ORGANIZATION_UPDATED = 'ORGANIZATION_UPDATED',
  ORGANIZATION_DELETED = 'ORGANIZATION_DELETED',
  ORGANIZATION_SETTINGS_CHANGED = 'ORGANIZATION_SETTINGS_CHANGED',

  // Project Management
  PROJECT_CREATED = 'PROJECT_CREATED',
  PROJECT_UPDATED = 'PROJECT_UPDATED',
  PROJECT_DELETED = 'PROJECT_DELETED',
  PROJECT_STATUS_CHANGED = 'PROJECT_STATUS_CHANGED',

  // Content Management
  CONTENT_CREATED = 'CONTENT_CREATED',
  CONTENT_UPDATED = 'CONTENT_UPDATED',
  CONTENT_DELETED = 'CONTENT_DELETED',
  CONTENT_PUBLISHED = 'CONTENT_PUBLISHED',
  CONTENT_SCHEDULED = 'CONTENT_SCHEDULED',

  // Social Media
  SOCIAL_ACCOUNT_CONNECTED = 'SOCIAL_ACCOUNT_CONNECTED',
  SOCIAL_ACCOUNT_DISCONNECTED = 'SOCIAL_ACCOUNT_DISCONNECTED',
  SOCIAL_POST_PUBLISHED = 'SOCIAL_POST_PUBLISHED',

  // Security
  SECURITY_VIOLATION = 'SECURITY_VIOLATION',
  API_KEY_CREATED = 'API_KEY_CREATED',
  API_KEY_REVOKED = 'API_KEY_REVOKED',
  SUSPICIOUS_ACTIVITY = 'SUSPICIOUS_ACTIVITY',

  // Data Access
  DATA_EXPORTED = 'DATA_EXPORTED',
  DATA_IMPORTED = 'DATA_IMPORTED',
  BULK_OPERATION = 'BULK_OPERATION',

  // System
  SYSTEM_BACKUP = 'SYSTEM_BACKUP',
  SYSTEM_RESTORE = 'SYSTEM_RESTORE',
  SYSTEM_MAINTENANCE = 'SYSTEM_MAINTENANCE'
}

export enum AuditSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}

export interface AuditLogEntry {
  id?: string;
  action: AuditAction;
  severity: AuditSeverity;
  userId?: string;
  organizationId?: string;
  resourceType?: string;
  resourceId?: string;
  details: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  requestId?: string;
  timestamp: Date;
  success: boolean;
  errorMessage?: string;
}

export class AuditLogger {
  private prisma: PrismaClient;
  private batchSize: number = 100;
  private batchTimeout: number = 5000; // 5 seconds
  private pendingLogs: AuditLogEntry[] = [];
  private batchTimer?: NodeJS.Timeout;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
    this.initializeBatchProcessor();
  }

  /**
   * Log an audit event
   */
  async log(entry: Omit<AuditLogEntry, 'id' | 'timestamp'>): Promise<void> {
    const auditEntry: AuditLogEntry = {
      ...entry,
      timestamp: new Date()
    };

    // Add to batch
    this.pendingLogs.push(auditEntry);

    // Process immediately for critical events
    if (entry.severity === AuditSeverity.CRITICAL) {
      await this.processBatch();
    }

    // Process batch if it's full
    if (this.pendingLogs.length >= this.batchSize) {
      await this.processBatch();
    }

    // Also log to application logger for immediate visibility
    this.logToApplicationLogger(auditEntry);
  }

  /**
   * Log authentication event
   */
  async logAuth(
    action: AuditAction,
    userId: string,
    success: boolean,
    req?: Request,
    details: Record<string, any> = {}
  ): Promise<void> {
    await this.log({
      action,
      severity: success ? AuditSeverity.LOW : AuditSeverity.MEDIUM,
      userId,
      details: {
        ...details,
        method: req?.method,
        path: req?.path
      },
      ipAddress: req?.ip,
      userAgent: req?.headers['user-agent'],
      requestId: req?.requestId,
      success
    });
  }

  /**
   * Log user management event
   */
  async logUserManagement(
    action: AuditAction,
    actorUserId: string,
    targetUserId: string,
    organizationId: string,
    details: Record<string, any> = {},
    req?: Request
  ): Promise<void> {
    await this.log({
      action,
      severity: AuditSeverity.MEDIUM,
      userId: actorUserId,
      organizationId,
      resourceType: 'user',
      resourceId: targetUserId,
      details,
      ipAddress: req?.ip,
      userAgent: req?.headers['user-agent'],
      requestId: req?.requestId,
      success: true
    });
  }

  /**
   * Log data access event
   */
  async logDataAccess(
    action: AuditAction,
    userId: string,
    organizationId: string,
    resourceType: string,
    resourceId: string,
    details: Record<string, any> = {},
    req?: Request
  ): Promise<void> {
    await this.log({
      action,
      severity: AuditSeverity.LOW,
      userId,
      organizationId,
      resourceType,
      resourceId,
      details,
      ipAddress: req?.ip,
      userAgent: req?.headers['user-agent'],
      requestId: req?.requestId,
      success: true
    });
  }

  /**
   * Log security event
   */
  async logSecurity(
    action: AuditAction,
    severity: AuditSeverity,
    details: Record<string, any> = {},
    req?: Request,
    userId?: string
  ): Promise<void> {
    await this.log({
      action,
      severity,
      userId,
      details,
      ipAddress: req?.ip,
      userAgent: req?.headers['user-agent'],
      requestId: req?.requestId,
      success: false
    });
  }

  /**
   * Log system event
   */
  async logSystem(
    action: AuditAction,
    details: Record<string, any> = {},
    success: boolean = true
  ): Promise<void> {
    await this.log({
      action,
      severity: AuditSeverity.MEDIUM,
      details,
      success
    });
  }

  /**
   * Get audit logs with filtering
   */
  async getAuditLogs(filters: {
    organizationId?: string;
    userId?: string;
    action?: AuditAction;
    severity?: AuditSeverity;
    resourceType?: string;
    resourceId?: string;
    startDate?: Date;
    endDate?: Date;
    page?: number;
    limit?: number;
  }): Promise<{ logs: AuditLogEntry[]; total: number }> {
    const {
      organizationId,
      userId,
      action,
      severity,
      resourceType,
      resourceId,
      startDate,
      endDate,
      page = 1,
      limit = 50
    } = filters;

    const where: any = {};

    if (organizationId) where.organizationId = organizationId;
    if (userId) where.userId = userId;
    if (action) where.action = action;
    if (severity) where.severity = severity;
    if (resourceType) where.resourceType = resourceType;
    if (resourceId) where.resourceId = resourceId;

    if (startDate || endDate) {
      where.timestamp = {};
      if (startDate) where.timestamp.gte = startDate;
      if (endDate) where.timestamp.lte = endDate;
    }

    const [logs, total] = await Promise.all([
      this.prisma.auditLog.findMany({
        where,
        orderBy: { timestamp: 'desc' },
        skip: (page - 1) * limit,
        take: limit
      }),
      this.prisma.auditLog.count({ where })
    ]);

    return { logs, total };
  }

  /**
   * Get audit statistics
   */
  async getAuditStats(organizationId?: string): Promise<{
    totalLogs: number;
    logsByAction: Record<string, number>;
    logsBySeverity: Record<string, number>;
    recentActivity: AuditLogEntry[];
  }> {
    const where = organizationId ? { organizationId } : {};

    const [totalLogs, logsByAction, logsBySeverity, recentActivity] = await Promise.all([
      this.prisma.auditLog.count({ where }),
      this.prisma.auditLog.groupBy({
        by: ['action'],
        where,
        _count: { action: true }
      }),
      this.prisma.auditLog.groupBy({
        by: ['severity'],
        where,
        _count: { severity: true }
      }),
      this.prisma.auditLog.findMany({
        where,
        orderBy: { timestamp: 'desc' },
        take: 10
      })
    ]);

    return {
      totalLogs,
      logsByAction: logsByAction.reduce((acc, item) => {
        acc[item.action] = item._count.action;
        return acc;
      }, {} as Record<string, number>),
      logsBySeverity: logsBySeverity.reduce((acc, item) => {
        acc[item.severity] = item._count.severity;
        return acc;
      }, {} as Record<string, number>),
      recentActivity
    };
  }

  /**
   * Clean up old audit logs
   */
  async cleanupOldLogs(retentionDays: number = 365): Promise<number> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

    const result = await this.prisma.auditLog.deleteMany({
      where: {
        timestamp: {
          lt: cutoffDate
        },
        severity: {
          not: AuditSeverity.CRITICAL // Keep critical logs longer
        }
      }
    });

    logger.info(`Cleaned up ${result.count} old audit logs`);
    return result.count;
  }

  /**
   * Initialize batch processor
   */
  private initializeBatchProcessor(): void {
    // Process batch every 5 seconds
    this.batchTimer = setInterval(() => {
      if (this.pendingLogs.length > 0) {
        this.processBatch().catch(error => {
          logger.error('Error processing audit log batch:', error);
        });
      }
    }, this.batchTimeout);
  }

  /**
   * Process pending logs in batch
   */
  private async processBatch(): Promise<void> {
    if (this.pendingLogs.length === 0) return;

    const logsToProcess = [...this.pendingLogs];
    this.pendingLogs = [];

    try {
      await this.prisma.auditLog.createMany({
        data: logsToProcess.map(log => ({
          action: log.action,
          severity: log.severity,
          userId: log.userId,
          organizationId: log.organizationId,
          resourceType: log.resourceType,
          resourceId: log.resourceId,
          details: log.details,
          ipAddress: log.ipAddress,
          userAgent: log.userAgent,
          requestId: log.requestId,
          timestamp: log.timestamp,
          success: log.success,
          errorMessage: log.errorMessage
        }))
      });

      logger.debug(`Processed ${logsToProcess.length} audit logs`);
    } catch (error) {
      logger.error('Error saving audit logs to database:', error);
      
      // Try to cache failed logs for retry
      try {
        const failedLogs = JSON.stringify(logsToProcess);
        await CacheService.set(
          `audit_failed_${Date.now()}`,
          failedLogs,
          3600 // 1 hour
        );
      } catch (cacheError) {
        logger.error('Error caching failed audit logs:', cacheError);
      }
    }
  }

  /**
   * Log to application logger for immediate visibility
   */
  private logToApplicationLogger(entry: AuditLogEntry): void {
    const logData = {
      action: entry.action,
      severity: entry.severity,
      userId: entry.userId,
      organizationId: entry.organizationId,
      resourceType: entry.resourceType,
      resourceId: entry.resourceId,
      success: entry.success,
      requestId: entry.requestId,
      details: entry.details
    };

    switch (entry.severity) {
      case AuditSeverity.CRITICAL:
        logger.error('AUDIT [CRITICAL]', logData);
        break;
      case AuditSeverity.HIGH:
        logger.warn('AUDIT [HIGH]', logData);
        break;
      case AuditSeverity.MEDIUM:
        logger.info('AUDIT [MEDIUM]', logData);
        break;
      case AuditSeverity.LOW:
        logger.debug('AUDIT [LOW]', logData);
        break;
    }
  }

  /**
   * Cleanup on shutdown
   */
  async shutdown(): Promise<void> {
    if (this.batchTimer) {
      clearInterval(this.batchTimer);
    }
    
    // Process any remaining logs
    await this.processBatch();
  }
}
