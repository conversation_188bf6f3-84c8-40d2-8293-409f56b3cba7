import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  Legend,
} from 'recharts'

interface PlatformChartProps {
  data: Record<string, number>
}

const PlatformChart: React.FC<PlatformChartProps> = ({ data }) => {
  // Convert data to chart format
  const chartData = Object.entries(data).map(([platform, count]) => ({
    name: platform,
    value: count,
    icon: getPlatformIcon(platform),
  }))

  // Platform colors
  const COLORS = {
    INSTAGRAM: '#E4405F',
    FACEBOOK: '#1877F2',
    TWITTER: '#1DA1F2',
    LINKEDIN: '#0A66C2',
    TIKTOK: '#000000',
    YOUTUBE: '#FF0000',
    PINTEREST: '#BD081C',
  }

  function getPlatformIcon(platform: string) {
    switch (platform) {
      case 'INSTAGRAM': return '📷'
      case 'FACEBOOK': return '👥'
      case 'TWITTER': return '🐦'
      case 'LINKEDIN': return '💼'
      case 'TIKTOK': return '🎵'
      case 'YOUTUBE': return '📺'
      case 'PINTEREST': return '📌'
      default: return '📱'
    }
  }

  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <div className="flex items-center space-x-2">
            <span className="text-lg">{data.icon}</span>
            <span className="font-medium text-gray-900">{data.name}</span>
          </div>
          <p className="text-sm text-gray-600">
            Content: {data.value}
          </p>
        </div>
      )
    }
    return null
  }

  const CustomLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }: any) => {
    if (percent < 0.05) return null // Don't show labels for slices smaller than 5%
    
    const RADIAN = Math.PI / 180
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5
    const x = cx + radius * Math.cos(-midAngle * RADIAN)
    const y = cy + radius * Math.sin(-midAngle * RADIAN)

    return (
      <text 
        x={x} 
        y={y} 
        fill="white" 
        textAnchor={x > cx ? 'start' : 'end'} 
        dominantBaseline="central"
        fontSize={12}
        fontWeight="bold"
      >
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    )
  }

  const CustomLegend = ({ payload }: any) => {
    return (
      <div className="flex flex-wrap justify-center gap-4 mt-4">
        {payload.map((entry: any, index: number) => (
          <div key={index} className="flex items-center space-x-2">
            <div 
              className="w-3 h-3 rounded-full"
              style={{ backgroundColor: entry.color }}
            />
            <span className="text-sm text-gray-600">
              {entry.payload.icon} {entry.value}
            </span>
          </div>
        ))}
      </div>
    )
  }

  if (chartData.length === 0) {
    return (
      <div className="h-80 flex items-center justify-center text-gray-500">
        No platform data available
      </div>
    )
  }

  return (
    <div className="h-80">
      <ResponsiveContainer width="100%" height="100%">
        <PieChart>
          <Pie
            data={chartData}
            cx="50%"
            cy="50%"
            labelLine={false}
            label={CustomLabel}
            outerRadius={80}
            fill="#8884d8"
            dataKey="value"
          >
            {chartData.map((entry, index) => (
              <Cell 
                key={`cell-${index}`} 
                fill={COLORS[entry.name as keyof typeof COLORS] || '#8884d8'} 
              />
            ))}
          </Pie>
          <Tooltip content={<CustomTooltip />} />
          <Legend content={<CustomLegend />} />
        </PieChart>
      </ResponsiveContainer>
    </div>
  )
}

export default PlatformChart
