import { apiClient } from './client'
import { LoginCredentials, RegisterData, AuthResponse, ApiResponse } from '../types/auth'

export const authAPI = {
  // Login user
  login: (credentials: LoginCredentials) => {
    return apiClient.post<ApiResponse<AuthResponse>>('/auth/login', credentials)
  },

  // Register user
  register: (userData: RegisterData) => {
    return apiClient.post<ApiResponse<AuthResponse>>('/auth/register', userData)
  },

  // Get current user
  getCurrentUser: () => {
    return apiClient.get<ApiResponse<{ user: any }>>('/auth/me')
  },

  // Logout user (client-side only for now)
  logout: () => {
    localStorage.removeItem('token')
    return Promise.resolve()
  },

  // Refresh token (if implemented)
  refreshToken: () => {
    return apiClient.post<ApiResponse<{ token: string }>>('/auth/refresh')
  },

  // Request password reset
  requestPasswordReset: (email: string) => {
    return apiClient.post<ApiResponse<{ message: string }>>('/auth/forgot-password', { email })
  },

  // Reset password
  resetPassword: (token: string, password: string) => {
    return apiClient.post<ApiResponse<{ message: string }>>('/auth/reset-password', {
      token,
      password,
    })
  },

  // Verify email
  verifyEmail: (token: string) => {
    return apiClient.post<ApiResponse<{ message: string }>>('/auth/verify-email', { token })
  },

  // Resend verification email
  resendVerificationEmail: () => {
    return apiClient.post<ApiResponse<{ message: string }>>('/auth/resend-verification')
  },
}
