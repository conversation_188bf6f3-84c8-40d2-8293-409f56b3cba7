{"name": "socialhub-mobile", "version": "1.0.0", "description": "SocialHub Pro Mobile Application", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build:android": "eas build --platform android", "build:ios": "eas build --platform ios", "submit:android": "eas submit --platform android", "submit:ios": "eas submit --platform ios", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "type-check": "tsc --noEmit"}, "dependencies": {"@expo/vector-icons": "^13.0.0", "@react-native-async-storage/async-storage": "^1.19.0", "@react-native-community/netinfo": "^9.4.0", "@react-navigation/bottom-tabs": "^6.5.0", "@react-navigation/native": "^6.1.0", "@react-navigation/stack": "^6.3.0", "@reduxjs/toolkit": "^1.9.0", "axios": "^1.4.0", "expo": "~49.0.0", "expo-auth-session": "~5.0.0", "expo-camera": "~13.4.0", "expo-constants": "~14.4.0", "expo-device": "~5.4.0", "expo-file-system": "~15.4.0", "expo-image-picker": "~14.3.0", "expo-linear-gradient": "~12.3.0", "expo-location": "~16.1.0", "expo-notifications": "~0.20.0", "expo-secure-store": "~12.3.0", "expo-status-bar": "~1.6.0", "react": "18.2.0", "react-native": "0.72.0", "react-native-chart-kit": "^6.12.0", "react-native-gesture-handler": "~2.12.0", "react-native-paper": "^5.8.0", "react-native-reanimated": "~3.3.0", "react-native-safe-area-context": "^4.6.0", "react-native-screens": "~3.22.0", "react-native-svg": "^13.9.0", "react-native-vector-icons": "^9.2.0", "react-redux": "^8.1.0", "redux-persist": "^6.0.0", "socket.io-client": "^4.7.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.2.0", "@types/react-native": "~0.72.0", "@typescript-eslint/eslint-plugin": "^5.59.0", "@typescript-eslint/parser": "^5.59.0", "eslint": "^8.42.0", "eslint-config-expo": "^7.0.0", "jest": "^29.5.0", "jest-expo": "~49.0.0", "typescript": "^5.1.0"}, "keywords": ["react-native", "expo", "social-media", "mobile-app", "<PERSON><PERSON><PERSON>"], "author": "SocialHub Pro Team", "license": "MIT", "private": true}