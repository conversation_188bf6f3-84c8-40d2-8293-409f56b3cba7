import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { 
  Users, 
  TrendingUp, 
  Eye, 
  Heart,
  Activity,
  Clock,
  Zap
} from 'lucide-react';

interface RealtimeMetrics {
  activeUsers: number;
  todayPosts: number;
  todayEngagement: number;
  liveMetrics: { platform: string; metric: string; value: number; change: number }[];
}

interface RealtimeMetricsProps {
  data: RealtimeMetrics;
}

export const RealtimeMetrics: React.FC<RealtimeMetricsProps> = ({ data }) => {
  const [currentTime, setCurrentTime] = useState(new Date());

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toString();
  };

  const getPlatformIcon = (platform: string) => {
    const icons: Record<string, string> = {
      INSTAGRAM: '📷',
      FACEBOOK: '👥',
      TWITTER: '🐦',
      LINKEDIN: '💼',
      TIKTOK: '🎵',
      YOUTUBE: '📺',
      PINTEREST: '📌'
    };
    return icons[platform] || '📱';
  };

  const getMetricIcon = (metric: string) => {
    const icons: Record<string, React.ReactNode> = {
      engagement: <Heart className="w-4 h-4" />,
      reach: <Eye className="w-4 h-4" />,
      followers: <Users className="w-4 h-4" />,
      posts: <TrendingUp className="w-4 h-4" />
    };
    return icons[metric] || <Activity className="w-4 h-4" />;
  };

  const getChangeColor = (change: number) => {
    if (change > 0) return 'text-green-600';
    if (change < 0) return 'text-red-600';
    return 'text-gray-600';
  };

  const getChangeIcon = (change: number) => {
    if (change > 0) return '↗';
    if (change < 0) return '↘';
    return '→';
  };

  return (
    <div className="space-y-6">
      {/* Header with Live Indicator */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <h2 className="text-2xl font-bold text-gray-900">Real-time Metrics</h2>
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
            <span className="text-sm text-green-600 font-medium">Live</span>
          </div>
        </div>
        <div className="text-right">
          <p className="text-sm text-gray-600">Last updated</p>
          <p className="text-sm font-medium">
            {currentTime.toLocaleTimeString()}
          </p>
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="border-l-4 border-l-green-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active Users</p>
                <p className="text-3xl font-bold text-gray-900">{data.activeUsers}</p>
              </div>
              <div className="p-3 bg-green-100 rounded-full">
                <Users className="w-6 h-6 text-green-600" />
              </div>
            </div>
            <div className="mt-2 flex items-center">
              <Activity className="w-4 h-4 text-green-500 mr-1" />
              <span className="text-sm text-green-600">Currently online</span>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-blue-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Today's Posts</p>
                <p className="text-3xl font-bold text-gray-900">{data.todayPosts}</p>
              </div>
              <div className="p-3 bg-blue-100 rounded-full">
                <TrendingUp className="w-6 h-6 text-blue-600" />
              </div>
            </div>
            <div className="mt-2 flex items-center">
              <Clock className="w-4 h-4 text-blue-500 mr-1" />
              <span className="text-sm text-blue-600">Published today</span>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-purple-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Today's Engagement</p>
                <p className="text-3xl font-bold text-gray-900">
                  {formatNumber(data.todayEngagement)}
                </p>
              </div>
              <div className="p-3 bg-purple-100 rounded-full">
                <Heart className="w-6 h-6 text-purple-600" />
              </div>
            </div>
            <div className="mt-2 flex items-center">
              <Zap className="w-4 h-4 text-purple-500 mr-1" />
              <span className="text-sm text-purple-600">Total interactions</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Live Platform Metrics */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Activity className="w-5 h-5 mr-2" />
            Live Platform Activity
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {data.liveMetrics.map((metric, index) => (
              <div
                key={index}
                className="border rounded-lg p-4 hover:shadow-md transition-shadow"
              >
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-2">
                    <span className="text-lg">{getPlatformIcon(metric.platform)}</span>
                    <span className="font-medium text-sm">{metric.platform}</span>
                  </div>
                  <Badge variant="outline" className="text-xs">
                    Live
                  </Badge>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    {getMetricIcon(metric.metric)}
                    <span className="text-sm text-gray-600 capitalize">
                      {metric.metric}
                    </span>
                  </div>
                  <div className="text-right">
                    <p className="text-lg font-bold text-gray-900">
                      {formatNumber(metric.value)}
                    </p>
                    <div className={`flex items-center text-xs ${getChangeColor(metric.change)}`}>
                      <span className="mr-1">{getChangeIcon(metric.change)}</span>
                      <span>{Math.abs(metric.change).toFixed(1)}%</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Activity Timeline */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Clock className="w-5 h-5 mr-2" />
            Recent Activity
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Mock recent activities */}
            <div className="flex items-center space-x-3 p-3 bg-green-50 rounded-lg">
              <div className="w-2 h-2 bg-green-500 rounded-full" />
              <div className="flex-1">
                <p className="text-sm font-medium">New post published on Instagram</p>
                <p className="text-xs text-gray-500">2 minutes ago</p>
              </div>
              <Badge variant="outline" className="text-xs">📷 Instagram</Badge>
            </div>
            
            <div className="flex items-center space-x-3 p-3 bg-blue-50 rounded-lg">
              <div className="w-2 h-2 bg-blue-500 rounded-full" />
              <div className="flex-1">
                <p className="text-sm font-medium">Engagement spike detected on LinkedIn</p>
                <p className="text-xs text-gray-500">5 minutes ago</p>
              </div>
              <Badge variant="outline" className="text-xs">💼 LinkedIn</Badge>
            </div>
            
            <div className="flex items-center space-x-3 p-3 bg-purple-50 rounded-lg">
              <div className="w-2 h-2 bg-purple-500 rounded-full" />
              <div className="flex-1">
                <p className="text-sm font-medium">Scheduled post went live on Twitter</p>
                <p className="text-xs text-gray-500">8 minutes ago</p>
              </div>
              <Badge variant="outline" className="text-xs">🐦 Twitter</Badge>
            </div>
            
            <div className="flex items-center space-x-3 p-3 bg-orange-50 rounded-lg">
              <div className="w-2 h-2 bg-orange-500 rounded-full" />
              <div className="flex-1">
                <p className="text-sm font-medium">New follower milestone reached on YouTube</p>
                <p className="text-xs text-gray-500">12 minutes ago</p>
              </div>
              <Badge variant="outline" className="text-xs">📺 YouTube</Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* System Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Zap className="w-5 h-5 mr-2" />
            System Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-3 bg-green-50 rounded-lg">
              <div className="w-8 h-8 bg-green-500 rounded-full mx-auto mb-2 flex items-center justify-center">
                <span className="text-white text-sm">✓</span>
              </div>
              <p className="text-sm font-medium">API Status</p>
              <p className="text-xs text-green-600">All systems operational</p>
            </div>
            
            <div className="text-center p-3 bg-green-50 rounded-lg">
              <div className="w-8 h-8 bg-green-500 rounded-full mx-auto mb-2 flex items-center justify-center">
                <span className="text-white text-sm">✓</span>
              </div>
              <p className="text-sm font-medium">Publishing Queue</p>
              <p className="text-xs text-green-600">Processing normally</p>
            </div>
            
            <div className="text-center p-3 bg-green-50 rounded-lg">
              <div className="w-8 h-8 bg-green-500 rounded-full mx-auto mb-2 flex items-center justify-center">
                <span className="text-white text-sm">✓</span>
              </div>
              <p className="text-sm font-medium">Analytics</p>
              <p className="text-xs text-green-600">Data syncing</p>
            </div>
            
            <div className="text-center p-3 bg-green-50 rounded-lg">
              <div className="w-8 h-8 bg-green-500 rounded-full mx-auto mb-2 flex items-center justify-center">
                <span className="text-white text-sm">✓</span>
              </div>
              <p className="text-sm font-medium">Integrations</p>
              <p className="text-xs text-green-600">All connected</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default RealtimeMetrics;
