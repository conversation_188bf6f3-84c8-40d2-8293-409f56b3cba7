# SocialHub Pro - Social Media Management Platform

A comprehensive, production-ready social media management SaaS platform built with React, Node.js, and PostgreSQL.

## 🚀 Features

### Phase 1 - Core Platform (Current)
- ✅ User authentication and role management
- ✅ Modern React 18 + TypeScript frontend
- ✅ Node.js + Express + PostgreSQL backend
- ✅ Redux Toolkit for state management
- ✅ Tailwind CSS for styling
- ✅ JWT-based authentication
- ✅ Role-based access control
- ✅ Responsive dashboard layout

### Upcoming Features
- Project management system
- Content creation and scheduling
- Social media platform integrations
- Analytics and reporting
- Team collaboration tools
- Client portal
- Mobile application

## 🏗️ Tech Stack

### Frontend
- **React 18** with TypeScript
- **Vite** for fast development
- **Redux Toolkit** + React Query for state management
- **Tailwind CSS** + Headless UI for styling
- **React Router** for navigation
- **React Hook Form** + Zod for form validation

### Backend
- **Node.js 18** + Express.js
- **PostgreSQL** with Prisma ORM
- **Redis** for caching and sessions
- **JWT** authentication
- **Socket.io** for real-time features
- **Winston** for logging

### Infrastructure
- **Docker** for containerization
- **AWS** for production deployment
- **GitHub Actions** for CI/CD

## 🛠️ Development Setup

### Prerequisites
- Node.js 18+
- PostgreSQL 14+
- Redis 7+
- Docker (optional)

### Quick Start

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd social-manager
   ```

2. **Install dependencies**
   ```bash
   npm install
   npm run setup
   ```

3. **Set up environment variables**
   ```bash
   # Copy environment files
   cp .env.example .env
   cp frontend/.env.example frontend/.env
   cp backend/.env.example backend/.env
   
   # Update the .env files with your configuration
   ```

4. **Set up the database**
   ```bash
   # Generate Prisma client
   npm run db:generate
   
   # Run database migrations
   npm run db:migrate
   
   # Seed the database (optional)
   npm run db:seed
   ```

5. **Start the development servers**
   ```bash
   # Start both frontend and backend
   npm run dev
   
   # Or start individually
   npm run dev:frontend  # Frontend on http://localhost:3000
   npm run dev:backend   # Backend on http://localhost:5000
   ```

### Using Docker

1. **Start with Docker Compose**
   ```bash
   npm run docker:dev
   ```

This will start:
- Frontend on http://localhost:3000
- Backend on http://localhost:5000
- PostgreSQL on localhost:5432
- Redis on localhost:6379

## 📁 Project Structure

```
social-manager/
├── frontend/                 # React frontend application
│   ├── src/
│   │   ├── components/      # Reusable UI components
│   │   ├── pages/          # Page components
│   │   ├── store/          # Redux store and slices
│   │   ├── api/            # API client and endpoints
│   │   ├── types/          # TypeScript type definitions
│   │   ├── utils/          # Utility functions
│   │   └── hooks/          # Custom React hooks
│   ├── public/             # Static assets
│   └── package.json
├── backend/                 # Node.js backend API
│   ├── src/
│   │   ├── controllers/    # Route controllers
│   │   ├── middleware/     # Express middleware
│   │   ├── routes/         # API routes
│   │   ├── services/       # Business logic
│   │   ├── config/         # Configuration files
│   │   ├── utils/          # Utility functions
│   │   └── types/          # TypeScript type definitions
│   ├── prisma/             # Database schema and migrations
│   └── package.json
├── docker-compose.dev.yml   # Development Docker setup
├── docker-compose.prod.yml  # Production Docker setup
└── package.json            # Root package.json for workspace
```

## 🔧 Available Scripts

### Root Level
- `npm run dev` - Start both frontend and backend in development mode
- `npm run build` - Build both frontend and backend for production
- `npm run test` - Run tests for both frontend and backend
- `npm run lint` - Lint both frontend and backend code

### Database
- `npm run db:migrate` - Run database migrations
- `npm run db:generate` - Generate Prisma client
- `npm run db:seed` - Seed the database with sample data
- `npm run db:studio` - Open Prisma Studio

### Docker
- `npm run docker:dev` - Start development environment with Docker
- `npm run docker:prod` - Start production environment with Docker

## 🔐 Authentication

The platform uses JWT-based authentication with the following user roles:

- **Super Admin** - Complete platform control
- **Agency Admin** - Client account and team management
- **Client Admin** - Company account management
- **Team Member** - Content creation and collaboration
- **Client User** - Project viewing and feedback

## 🌐 API Documentation

The backend API follows RESTful conventions:

- `POST /api/auth/login` - User login
- `POST /api/auth/register` - User registration
- `GET /api/auth/me` - Get current user
- `GET /api/projects` - List projects
- `POST /api/projects` - Create project
- `GET /api/content` - List content items
- `POST /api/content` - Create content

## 🚀 Deployment

### Production Environment Variables

Ensure the following environment variables are set in production:

```bash
# Database
DATABASE_URL=************************************/database
REDIS_URL=redis://host:6379

# Security
JWT_SECRET=your-super-secret-jwt-key
NODE_ENV=production

# AWS (for file storage)
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_S3_BUCKET=your-bucket-name

# Email
EMAIL_HOST=smtp.example.com
EMAIL_USER=your-email
EMAIL_PASS=your-password
```

### Docker Production Deployment

```bash
# Build and start production containers
docker-compose -f docker-compose.prod.yml up -d

# View logs
docker-compose -f docker-compose.prod.yml logs -f
```

## 📊 Monitoring

The application includes comprehensive logging and monitoring:

- **Winston** for structured logging
- **Health check** endpoints
- **Error tracking** with Sentry (configurable)
- **Performance monitoring** with DataDog (configurable)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:

- Create an issue in the GitHub repository
- Contact the development team
- Check the documentation in the `/docs` folder

---

**SocialHub Pro** - Built with ❤️ for modern social media management
