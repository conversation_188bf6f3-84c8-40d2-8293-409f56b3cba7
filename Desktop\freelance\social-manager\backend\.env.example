# Database Configuration
DATABASE_URL="file:./dev.db"

# Server Configuration
NODE_ENV=development
PORT=5000
FRONTEND_URL=http://localhost:3000

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=7d

# Redis Configuration (Optional for development)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# OpenAI Configuration
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_MODEL=gpt-4

# Social Media Platform API Keys

# Facebook/Instagram
FACEBOOK_APP_ID=your-facebook-app-id
FACEBOOK_APP_SECRET=your-facebook-app-secret
FACEBOOK_REDIRECT_URI=http://localhost:5000/api/social-media/auth/facebook/callback

# Twitter
TWITTER_CLIENT_ID=your-twitter-client-id
TWITTER_CLIENT_SECRET=your-twitter-client-secret
TWITTER_REDIRECT_URI=http://localhost:5000/api/social-media/auth/twitter/callback

# LinkedIn
LINKEDIN_CLIENT_ID=your-linkedin-client-id
LINKEDIN_CLIENT_SECRET=your-linkedin-client-secret
LINKEDIN_REDIRECT_URI=http://localhost:5000/api/social-media/auth/linkedin/callback

# TikTok
TIKTOK_APP_ID=your-tiktok-app-id
TIKTOK_APP_SECRET=your-tiktok-app-secret
TIKTOK_REDIRECT_URI=http://localhost:5000/api/social-media/auth/tiktok/callback

# Google (YouTube)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_REDIRECT_URI=http://localhost:5000/api/social-media/auth/google/callback

# Pinterest
PINTEREST_CLIENT_ID=your-pinterest-client-id
PINTEREST_CLIENT_SECRET=your-pinterest-client-secret
PINTEREST_REDIRECT_URI=http://localhost:5000/api/social-media/auth/pinterest/callback

# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_your-stripe-secret-key
STRIPE_PUBLISHABLE_KEY=pk_test_your-stripe-publishable-key
STRIPE_WEBHOOK_SECRET=whsec_your-webhook-secret

# AWS Configuration (for file storage)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=your-s3-bucket-name

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Third-party Integrations

# Unsplash (for stock photos)
UNSPLASH_ACCESS_KEY=your-unsplash-access-key

# Pexels (for stock photos)
PEXELS_API_KEY=your-pexels-api-key

# Zapier (for automation)
ZAPIER_WEBHOOK_URL=your-zapier-webhook-url

# Analytics & Monitoring
SENTRY_DSN=your-sentry-dsn
GOOGLE_ANALYTICS_ID=your-ga-tracking-id

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# File Upload Limits
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,video/mp4,video/mov

# Security
BCRYPT_ROUNDS=12
SESSION_SECRET=your-session-secret

# Feature Flags
ENABLE_AI_FEATURES=true
ENABLE_ANALYTICS=true
ENABLE_BILLING=true
ENABLE_SOCIAL_INTEGRATIONS=true

# Development Tools
LOG_LEVEL=info
ENABLE_CORS=true
ENABLE_MORGAN_LOGGING=true
