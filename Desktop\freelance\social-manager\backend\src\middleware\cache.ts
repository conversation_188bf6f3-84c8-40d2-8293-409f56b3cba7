import { Request, Response, NextFunction } from 'express';
import { CacheService } from '../config/redis.js';
import { logger } from '../utils/logger.js';

interface CacheOptions {
  ttl?: number; // Time to live in seconds
  keyGenerator?: (req: Request) => string;
  skipCache?: (req: Request) => boolean;
}

/**
 * Cache middleware for API responses
 */
export function cacheMiddleware(options: CacheOptions = {}) {
  const {
    ttl = 300, // Default 5 minutes
    keyGenerator = defaultKeyGenerator,
    skipCache = () => false
  } = options;

  return async (req: Request, res: Response, next: NextFunction) => {
    // Skip caching for non-GET requests or when skipCache returns true
    if (req.method !== 'GET' || skipCache(req)) {
      return next();
    }

    const cacheKey = keyGenerator(req);

    try {
      // Try to get cached response
      const cachedResponse = await CacheService.get(cacheKey);
      
      if (cachedResponse) {
        logger.debug(`Cache hit for key: ${cacheKey}`);
        const parsedResponse = JSON.parse(cachedResponse);
        
        // Set cache headers
        res.set('X-Cache', 'HIT');
        res.set('X-Cache-Key', cacheKey);
        
        return res.status(parsedResponse.status).json(parsedResponse.data);
      }

      logger.debug(`Cache miss for key: ${cacheKey}`);
      
      // Store original res.json method
      const originalJson = res.json;
      
      // Override res.json to cache the response
      res.json = function(data: any) {
        // Cache the response
        const responseToCache = {
          status: res.statusCode,
          data: data
        };
        
        CacheService.set(cacheKey, JSON.stringify(responseToCache), ttl)
          .catch(error => logger.error('Error caching response:', error));
        
        // Set cache headers
        res.set('X-Cache', 'MISS');
        res.set('X-Cache-Key', cacheKey);
        
        // Call original json method
        return originalJson.call(this, data);
      };

      next();
    } catch (error) {
      logger.error('Cache middleware error:', error);
      next(); // Continue without caching on error
    }
  };
}

/**
 * Default cache key generator
 */
function defaultKeyGenerator(req: Request): string {
  const userId = req.user?.id || 'anonymous';
  const organizationId = req.user?.organizationId || 'no-org';
  const url = req.originalUrl;
  const query = JSON.stringify(req.query);
  
  return `api:${organizationId}:${userId}:${url}:${query}`;
}

/**
 * Cache key generators for specific use cases
 */
export const cacheKeyGenerators = {
  // User-specific cache
  userSpecific: (req: Request) => {
    const userId = req.user?.id || 'anonymous';
    return `user:${userId}:${req.originalUrl}:${JSON.stringify(req.query)}`;
  },

  // Organization-specific cache
  organizationSpecific: (req: Request) => {
    const organizationId = req.user?.organizationId || 'no-org';
    return `org:${organizationId}:${req.originalUrl}:${JSON.stringify(req.query)}`;
  },

  // Global cache (same for all users)
  global: (req: Request) => {
    return `global:${req.originalUrl}:${JSON.stringify(req.query)}`;
  },

  // Project-specific cache
  projectSpecific: (req: Request) => {
    const projectId = req.params.projectId || req.query.projectId;
    const organizationId = req.user?.organizationId || 'no-org';
    return `project:${organizationId}:${projectId}:${req.originalUrl}:${JSON.stringify(req.query)}`;
  }
};

/**
 * Cache invalidation utilities
 */
export class CacheInvalidation {
  /**
   * Invalidate all cache entries for a user
   */
  static async invalidateUser(userId: string): Promise<void> {
    try {
      const pattern = `user:${userId}:*`;
      await CacheService.del(pattern);
      logger.info(`Invalidated cache for user: ${userId}`);
    } catch (error) {
      logger.error(`Error invalidating user cache: ${userId}`, error);
    }
  }

  /**
   * Invalidate all cache entries for an organization
   */
  static async invalidateOrganization(organizationId: string): Promise<void> {
    try {
      const pattern = `org:${organizationId}:*`;
      await CacheService.del(pattern);
      logger.info(`Invalidated cache for organization: ${organizationId}`);
    } catch (error) {
      logger.error(`Error invalidating organization cache: ${organizationId}`, error);
    }
  }

  /**
   * Invalidate all cache entries for a project
   */
  static async invalidateProject(organizationId: string, projectId: string): Promise<void> {
    try {
      const pattern = `project:${organizationId}:${projectId}:*`;
      await CacheService.del(pattern);
      logger.info(`Invalidated cache for project: ${projectId}`);
    } catch (error) {
      logger.error(`Error invalidating project cache: ${projectId}`, error);
    }
  }

  /**
   * Invalidate cache entries by pattern
   */
  static async invalidatePattern(pattern: string): Promise<void> {
    try {
      await CacheService.del(pattern);
      logger.info(`Invalidated cache pattern: ${pattern}`);
    } catch (error) {
      logger.error(`Error invalidating cache pattern: ${pattern}`, error);
    }
  }
}

/**
 * Predefined cache configurations for common use cases
 */
export const cacheConfigs = {
  // Short-term cache for frequently changing data
  shortTerm: {
    ttl: 60, // 1 minute
    keyGenerator: cacheKeyGenerators.userSpecific
  },

  // Medium-term cache for moderately changing data
  mediumTerm: {
    ttl: 300, // 5 minutes
    keyGenerator: cacheKeyGenerators.organizationSpecific
  },

  // Long-term cache for rarely changing data
  longTerm: {
    ttl: 3600, // 1 hour
    keyGenerator: cacheKeyGenerators.global
  },

  // Analytics cache (can be cached longer as it's historical data)
  analytics: {
    ttl: 1800, // 30 minutes
    keyGenerator: cacheKeyGenerators.organizationSpecific,
    skipCache: (req: Request) => {
      // Skip cache for real-time analytics requests
      return req.query.realtime === 'true';
    }
  },

  // Project-specific cache
  project: {
    ttl: 600, // 10 minutes
    keyGenerator: cacheKeyGenerators.projectSpecific
  }
};
