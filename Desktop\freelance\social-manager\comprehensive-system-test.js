const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api';
const FRONTEND_URL = 'http://localhost:3001';

// Test data
const testUser = {
  email: '<EMAIL>',
  password: 'password123',
  name: 'Test User',
  organizationName: 'Test Organization'
};

let authToken = '';
let userId = '';
let organizationId = '';
let projectId = '';
let contentId = '';

// Test results tracking
const testResults = {
  passed: 0,
  failed: 0,
  tests: []
};

function logTest(name, passed, details = '') {
  const status = passed ? '✅ PASS' : '❌ FAIL';
  console.log(`${status}: ${name}`);
  if (details) console.log(`   ${details}`);
  
  testResults.tests.push({ name, passed, details });
  if (passed) testResults.passed++;
  else testResults.failed++;
}

async function testSystemHealth() {
  console.log('🏥 SYSTEM HEALTH CHECKS');
  console.log('========================\n');

  // Test Backend Health
  try {
    const response = await axios.get(`${BASE_URL}/health`);
    logTest('Backend Health Check', response.status === 200 && response.data.success, 
           `Status: ${response.status}, Message: ${response.data.message}`);
  } catch (error) {
    logTest('Backend Health Check', false, `Error: ${error.message}`);
  }

  // Test Frontend Availability
  try {
    const response = await axios.get(FRONTEND_URL);
    logTest('Frontend Availability', response.status === 200, 
           `Status: ${response.status}, Content-Type: ${response.headers['content-type']}`);
  } catch (error) {
    logTest('Frontend Availability', false, `Error: ${error.message}`);
  }
}

async function testAuthentication() {
  console.log('\n🔐 AUTHENTICATION TESTS');
  console.log('========================\n');

  // Test Registration (might fail if user exists)
  try {
    const response = await axios.post(`${BASE_URL}/auth/register`, testUser);
    logTest('User Registration', response.status === 201, 
           `Status: ${response.status}, User ID: ${response.data.data?.user?.id}`);
    
    if (response.data.success) {
      authToken = response.data.data.token;
      userId = response.data.data.user.id;
      organizationId = response.data.data.user.organizationId;
    }
  } catch (error) {
    if (error.response?.status === 400 && error.response?.data?.message?.includes('already exists')) {
      logTest('User Registration', true, 'User already exists (expected)');
    } else {
      logTest('User Registration', false, `Error: ${error.response?.data?.message || error.message}`);
    }
  }

  // Test Login
  try {
    const response = await axios.post(`${BASE_URL}/auth/login`, {
      email: testUser.email,
      password: testUser.password
    });
    logTest('User Login', response.status === 200 && response.data.success, 
           `Status: ${response.status}, Token received: ${!!response.data.data?.token}`);
    
    if (response.data.success) {
      authToken = response.data.data.token;
      userId = response.data.data.user.id;
      organizationId = response.data.data.user.organizationId;
    }
  } catch (error) {
    logTest('User Login', false, `Error: ${error.response?.data?.message || error.message}`);
  }

  // Test Invalid Login
  try {
    await axios.post(`${BASE_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'wrongpassword'
    });
    logTest('Invalid Login Rejection', false, 'Should have failed but succeeded');
  } catch (error) {
    logTest('Invalid Login Rejection', error.response?.status === 401, 
           `Status: ${error.response?.status}, Message: ${error.response?.data?.message}`);
  }

  // Test Protected Route
  if (authToken) {
    try {
      const response = await axios.get(`${BASE_URL}/auth/me`, {
        headers: { 'Authorization': `Bearer ${authToken}` }
      });
      logTest('Protected Route Access', response.status === 200 && response.data.success, 
             `Status: ${response.status}, User: ${response.data.data?.user?.name}`);
    } catch (error) {
      logTest('Protected Route Access', false, `Error: ${error.response?.data?.message || error.message}`);
    }
  }
}

async function testCoreFeatures() {
  console.log('\n🚀 CORE FEATURES TESTS');
  console.log('=======================\n');

  const authHeaders = {
    'Authorization': `Bearer ${authToken}`,
    'Content-Type': 'application/json'
  };

  // Test Organizations
  try {
    const response = await axios.get(`${BASE_URL}/organizations`, { headers: authHeaders });
    logTest('Organizations API', response.status === 200, 
           `Status: ${response.status}, Organizations: ${response.data.data?.organizations?.length || 0}`);
  } catch (error) {
    logTest('Organizations API', false, `Error: ${error.response?.data?.message || error.message}`);
  }

  // Test Projects
  try {
    // Create Project
    const projectData = {
      name: 'System Test Project',
      description: 'A project created during comprehensive system testing',
      type: 'SOCIAL_MEDIA_CAMPAIGN',
      budget: 10000,
      deadline: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
    };
    
    const createResponse = await axios.post(`${BASE_URL}/projects`, projectData, { headers: authHeaders });
    logTest('Project Creation', createResponse.status === 201 && createResponse.data.success, 
           `Status: ${createResponse.status}, Project ID: ${createResponse.data.data?.project?.id}`);
    
    if (createResponse.data.success) {
      projectId = createResponse.data.data.project.id;
    }

    // Get Projects
    const getResponse = await axios.get(`${BASE_URL}/projects`, { headers: authHeaders });
    logTest('Project Retrieval', getResponse.status === 200, 
           `Status: ${getResponse.status}, Projects found: ${getResponse.data.data?.projects?.length || 0}`);
  } catch (error) {
    logTest('Projects API', false, `Error: ${error.response?.data?.message || error.message}`);
  }

  // Test Content Management
  try {
    const contentData = {
      title: 'System Test Content',
      description: 'Content created during comprehensive system testing',
      type: 'POST',
      platforms: ['INSTAGRAM', 'FACEBOOK'],
      contentData: {
        text: 'This is a comprehensive system test post! 🚀 #testing #socialmedia',
        hashtags: ['#testing', '#socialmedia', '#systemtest'],
        mentions: [],
        media: []
      },
      projectId: projectId,
      scheduledAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
    };
    
    const createResponse = await axios.post(`${BASE_URL}/content`, contentData, { headers: authHeaders });
    logTest('Content Creation', createResponse.status === 201 && createResponse.data.success, 
           `Status: ${createResponse.status}, Content ID: ${createResponse.data.data?.contentItem?.id}`);
    
    if (createResponse.data.success) {
      contentId = createResponse.data.data.contentItem.id;
    }

    // Get Content
    const getResponse = await axios.get(`${BASE_URL}/content`, { headers: authHeaders });
    logTest('Content Retrieval', getResponse.status === 200, 
           `Status: ${getResponse.status}, Content items: ${getResponse.data.data?.content?.length || 0}`);
  } catch (error) {
    logTest('Content Management', false, `Error: ${error.response?.data?.message || error.message}`);
  }
}

async function testAnalytics() {
  console.log('\n📊 ANALYTICS TESTS');
  console.log('===================\n');

  const authHeaders = {
    'Authorization': `Bearer ${authToken}`,
    'Content-Type': 'application/json'
  };

  // Test Analytics Overview
  try {
    const response = await axios.get(`${BASE_URL}/analytics/overview?period=30d`, { headers: authHeaders });
    logTest('Analytics Overview', response.status === 200 && response.data.success, 
           `Status: ${response.status}, Data received: ${!!response.data.data}`);
  } catch (error) {
    logTest('Analytics Overview', false, `Error: ${error.response?.data?.message || error.message}`);
  }

  // Test Performance Analytics
  try {
    const response = await axios.get(`${BASE_URL}/analytics/performance`, { headers: authHeaders });
    logTest('Performance Analytics', response.status === 200 && response.data.success, 
           `Status: ${response.status}, Performance data: ${!!response.data.data}`);
  } catch (error) {
    logTest('Performance Analytics', false, `Error: ${error.response?.data?.message || error.message}`);
  }
}

async function testIntegrations() {
  console.log('\n🔗 INTEGRATIONS TESTS');
  console.log('======================\n');

  const authHeaders = {
    'Authorization': `Bearer ${authToken}`,
    'Content-Type': 'application/json'
  };

  // Test Get Integrations
  try {
    const response = await axios.get(`${BASE_URL}/integrations`, { headers: authHeaders });
    logTest('Integrations List', response.status === 200, 
           `Status: ${response.status}, Integrations: ${response.data.data?.integrations?.length || 0}`);
  } catch (error) {
    logTest('Integrations List', false, `Error: ${error.response?.data?.message || error.message}`);
  }

  // Test Platform Capabilities
  try {
    const response = await axios.get(`${BASE_URL}/integrations/platforms/INSTAGRAM/capabilities`, { headers: authHeaders });
    logTest('Platform Capabilities', response.status === 200, 
           `Status: ${response.status}, Capabilities received: ${!!response.data.data}`);
  } catch (error) {
    logTest('Platform Capabilities', false, `Error: ${error.response?.data?.message || error.message}`);
  }
}

async function runComprehensiveSystemTest() {
  console.log('🚀 COMPREHENSIVE SYSTEM TEST');
  console.log('=============================');
  console.log('Testing SocialHub Pro Platform\n');

  try {
    await testSystemHealth();
    await testAuthentication();
    await testCoreFeatures();
    await testAnalytics();
    await testIntegrations();

    // Print comprehensive summary
    console.log('\n📊 COMPREHENSIVE TEST SUMMARY');
    console.log('==============================');
    console.log(`✅ Passed: ${testResults.passed}`);
    console.log(`❌ Failed: ${testResults.failed}`);
    console.log(`📈 Success Rate: ${((testResults.passed / (testResults.passed + testResults.failed)) * 100).toFixed(1)}%`);
    
    if (testResults.failed > 0) {
      console.log('\n❌ FAILED TESTS:');
      testResults.tests.filter(t => !t.passed).forEach(test => {
        console.log(`   - ${test.name}: ${test.details}`);
      });
    }

    console.log('\n🎯 SYSTEM STATUS:');
    if (testResults.failed === 0) {
      console.log('🟢 ALL SYSTEMS OPERATIONAL - READY FOR PRODUCTION! 🚀');
    } else if (testResults.failed <= 2) {
      console.log('🟡 MINOR ISSUES DETECTED - MOSTLY OPERATIONAL ⚠️');
    } else {
      console.log('🔴 MULTIPLE ISSUES DETECTED - NEEDS ATTENTION ❌');
    }

    console.log('\n✨ PLATFORM CAPABILITIES:');
    console.log('- 🔐 Authentication & Authorization');
    console.log('- 👥 User & Organization Management');
    console.log('- 📋 Project Management');
    console.log('- 📝 Content Creation & Scheduling');
    console.log('- 📊 Analytics & Reporting');
    console.log('- 🔗 Social Media Integrations');
    console.log('- 🎨 Modern React Frontend');
    console.log('- ⚡ Real-time Updates');

  } catch (error) {
    console.error('❌ Critical system error:', error.message);
  }
}

// Run the comprehensive test
runComprehensiveSystemTest();
