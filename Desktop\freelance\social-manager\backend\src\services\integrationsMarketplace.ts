import { PrismaClient } from '@prisma/client';
import { logger } from '../utils/logger.js';
import { CacheService } from '../config/redis.js';

export interface Integration {
  id: string;
  name: string;
  description: string;
  category: 'social_media' | 'analytics' | 'crm' | 'email_marketing' | 'e_commerce' | 'productivity' | 'design' | 'ai_tools';
  provider: string;
  version: string;
  logoUrl: string;
  screenshots: string[];
  pricing: {
    type: 'free' | 'freemium' | 'paid';
    price?: number;
    currency?: string;
    billingCycle?: 'monthly' | 'yearly';
  };
  features: string[];
  requirements: string[];
  permissions: string[];
  webhookSupport: boolean;
  apiVersion: string;
  documentation: {
    setupGuide: string;
    apiDocs: string;
    examples: string;
  };
  ratings: {
    average: number;
    count: number;
    distribution: { [key: number]: number };
  };
  installation: {
    type: 'oauth' | 'api_key' | 'webhook' | 'custom';
    config: any;
  };
  status: 'active' | 'deprecated' | 'beta' | 'coming_soon';
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
}

export interface IntegrationInstance {
  id: string;
  integrationId: string;
  organizationId: string;
  userId: string;
  name: string;
  config: any;
  credentials: any; // Encrypted
  status: 'active' | 'inactive' | 'error' | 'pending';
  lastSync: Date;
  syncFrequency: number; // in minutes
  errorMessage?: string;
  metrics: {
    totalSyncs: number;
    successfulSyncs: number;
    failedSyncs: number;
    lastSyncDuration: number;
    dataTransferred: number;
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface MarketplaceStats {
  totalIntegrations: number;
  activeInstallations: number;
  popularCategories: { category: string; count: number }[];
  topRatedIntegrations: Integration[];
  recentlyAdded: Integration[];
  trendingIntegrations: Integration[];
}

export interface WebhookEvent {
  id: string;
  integrationInstanceId: string;
  eventType: string;
  payload: any;
  headers: any;
  timestamp: Date;
  processed: boolean;
  processingTime?: number;
  error?: string;
}

export class IntegrationsMarketplace {
  private prisma: PrismaClient;
  private integrations: Map<string, Integration>;
  private webhookHandlers: Map<string, Function>;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
    this.integrations = new Map();
    this.webhookHandlers = new Map();
    this.initializeMarketplace();
  }

  /**
   * Get all available integrations
   */
  async getIntegrations(filters?: {
    category?: string;
    provider?: string;
    pricing?: string;
    search?: string;
    tags?: string[];
    page?: number;
    limit?: number;
  }): Promise<{ integrations: Integration[]; total: number; stats: MarketplaceStats }> {
    try {
      const cacheKey = `integrations:${JSON.stringify(filters)}`;
      const cached = await CacheService.get(cacheKey);
      if (cached) {
        return JSON.parse(cached);
      }

      // Apply filters
      let filteredIntegrations = Array.from(this.integrations.values());

      if (filters?.category) {
        filteredIntegrations = filteredIntegrations.filter(i => i.category === filters.category);
      }

      if (filters?.provider) {
        filteredIntegrations = filteredIntegrations.filter(i => i.provider === filters.provider);
      }

      if (filters?.pricing) {
        filteredIntegrations = filteredIntegrations.filter(i => i.pricing.type === filters.pricing);
      }

      if (filters?.search) {
        const searchTerm = filters.search.toLowerCase();
        filteredIntegrations = filteredIntegrations.filter(i =>
          i.name.toLowerCase().includes(searchTerm) ||
          i.description.toLowerCase().includes(searchTerm) ||
          i.tags.some(tag => tag.toLowerCase().includes(searchTerm))
        );
      }

      if (filters?.tags && filters.tags.length > 0) {
        filteredIntegrations = filteredIntegrations.filter(i =>
          filters.tags!.some(tag => i.tags.includes(tag))
        );
      }

      // Pagination
      const page = filters?.page || 1;
      const limit = filters?.limit || 20;
      const startIndex = (page - 1) * limit;
      const paginatedIntegrations = filteredIntegrations.slice(startIndex, startIndex + limit);

      // Generate stats
      const stats = await this.generateMarketplaceStats();

      const result = {
        integrations: paginatedIntegrations,
        total: filteredIntegrations.length,
        stats
      };

      // Cache for 1 hour
      await CacheService.set(cacheKey, JSON.stringify(result), 3600);

      return result;
    } catch (error) {
      logger.error('Error getting integrations:', error);
      throw error;
    }
  }

  /**
   * Install integration for organization
   */
  async installIntegration(
    integrationId: string,
    organizationId: string,
    userId: string,
    config: any,
    credentials: any
  ): Promise<IntegrationInstance> {
    try {
      const integration = this.integrations.get(integrationId);
      if (!integration) {
        throw new Error(`Integration ${integrationId} not found`);
      }

      // Validate configuration
      await this.validateIntegrationConfig(integration, config);

      // Encrypt credentials
      const encryptedCredentials = await this.encryptCredentials(credentials);

      // Create integration instance
      const instance: IntegrationInstance = {
        id: this.generateInstanceId(),
        integrationId,
        organizationId,
        userId,
        name: `${integration.name} - ${organizationId}`,
        config,
        credentials: encryptedCredentials,
        status: 'pending',
        lastSync: new Date(),
        syncFrequency: 60, // Default 1 hour
        metrics: {
          totalSyncs: 0,
          successfulSyncs: 0,
          failedSyncs: 0,
          lastSyncDuration: 0,
          dataTransferred: 0
        },
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // Test connection
      const connectionTest = await this.testIntegrationConnection(integration, instance);
      if (connectionTest.success) {
        instance.status = 'active';
      } else {
        instance.status = 'error';
        instance.errorMessage = connectionTest.error;
      }

      // Store instance
      await this.storeIntegrationInstance(instance);

      // Set up sync schedule
      if (instance.status === 'active') {
        await this.scheduleSync(instance);
      }

      logger.info(`Integration ${integration.name} installed for organization ${organizationId}`);

      return instance;
    } catch (error) {
      logger.error('Error installing integration:', error);
      throw error;
    }
  }

  /**
   * Sync integration data
   */
  async syncIntegration(instanceId: string): Promise<{
    success: boolean;
    dataTransferred: number;
    duration: number;
    error?: string;
  }> {
    try {
      const instance = await this.getIntegrationInstance(instanceId);
      if (!instance) {
        throw new Error(`Integration instance ${instanceId} not found`);
      }

      const integration = this.integrations.get(instance.integrationId);
      if (!integration) {
        throw new Error(`Integration ${instance.integrationId} not found`);
      }

      const startTime = Date.now();

      // Perform sync based on integration type
      const syncResult = await this.performSync(integration, instance);

      const duration = Date.now() - startTime;

      // Update metrics
      instance.metrics.totalSyncs++;
      instance.lastSync = new Date();
      instance.metrics.lastSyncDuration = duration;

      if (syncResult.success) {
        instance.metrics.successfulSyncs++;
        instance.metrics.dataTransferred += syncResult.dataTransferred;
        instance.status = 'active';
        instance.errorMessage = undefined;
      } else {
        instance.metrics.failedSyncs++;
        instance.status = 'error';
        instance.errorMessage = syncResult.error;
      }

      // Update instance
      await this.updateIntegrationInstance(instance);

      return {
        success: syncResult.success,
        dataTransferred: syncResult.dataTransferred,
        duration,
        error: syncResult.error
      };
    } catch (error) {
      logger.error(`Error syncing integration ${instanceId}:`, error);
      throw error;
    }
  }

  /**
   * Handle webhook events
   */
  async handleWebhook(
    integrationId: string,
    eventType: string,
    payload: any,
    headers: any
  ): Promise<{ success: boolean; processed: boolean }> {
    try {
      const webhookEvent: WebhookEvent = {
        id: this.generateEventId(),
        integrationInstanceId: integrationId,
        eventType,
        payload,
        headers,
        timestamp: new Date(),
        processed: false
      };

      // Store webhook event
      await this.storeWebhookEvent(webhookEvent);

      // Process webhook
      const handler = this.webhookHandlers.get(eventType);
      if (handler) {
        const startTime = Date.now();
        try {
          await handler(payload, headers);
          webhookEvent.processed = true;
          webhookEvent.processingTime = Date.now() - startTime;
        } catch (error) {
          webhookEvent.error = error.toString();
          logger.error(`Error processing webhook ${eventType}:`, error);
        }
      }

      // Update webhook event
      await this.updateWebhookEvent(webhookEvent);

      return {
        success: true,
        processed: webhookEvent.processed
      };
    } catch (error) {
      logger.error('Error handling webhook:', error);
      throw error;
    }
  }

  /**
   * Get integration analytics
   */
  async getIntegrationAnalytics(
    organizationId: string,
    timeframe: number = 30
  ): Promise<{
    totalInstances: number;
    activeInstances: number;
    syncSuccess: number;
    dataTransferred: number;
    topPerforming: IntegrationInstance[];
    recentErrors: any[];
  }> {
    try {
      const instances = await this.getOrganizationInstances(organizationId);

      const analytics = {
        totalInstances: instances.length,
        activeInstances: instances.filter(i => i.status === 'active').length,
        syncSuccess: this.calculateSyncSuccessRate(instances),
        dataTransferred: instances.reduce((sum, i) => sum + i.metrics.dataTransferred, 0),
        topPerforming: instances
          .sort((a, b) => b.metrics.successfulSyncs - a.metrics.successfulSyncs)
          .slice(0, 5),
        recentErrors: instances
          .filter(i => i.status === 'error')
          .map(i => ({
            name: i.name,
            error: i.errorMessage,
            lastSync: i.lastSync
          }))
      };

      return analytics;
    } catch (error) {
      logger.error('Error getting integration analytics:', error);
      throw error;
    }
  }

  /**
   * Initialize marketplace with built-in integrations
   */
  private initializeMarketplace(): void {
    // Social Media Integrations
    this.addIntegration({
      id: 'zapier',
      name: 'Zapier',
      description: 'Connect with 5000+ apps through Zapier automation',
      category: 'productivity',
      provider: 'Zapier',
      version: '1.0.0',
      logoUrl: '/integrations/zapier.png',
      screenshots: [],
      pricing: { type: 'freemium' },
      features: ['Automated workflows', 'Multi-app connections', 'Real-time triggers'],
      requirements: ['Zapier account'],
      permissions: ['webhook_access'],
      webhookSupport: true,
      apiVersion: 'v1',
      documentation: {
        setupGuide: '/docs/zapier-setup',
        apiDocs: '/docs/zapier-api',
        examples: '/docs/zapier-examples'
      },
      ratings: { average: 4.8, count: 1250, distribution: { 5: 800, 4: 300, 3: 100, 2: 30, 1: 20 } },
      installation: { type: 'webhook', config: {} },
      status: 'active',
      tags: ['automation', 'workflow', 'productivity'],
      createdAt: new Date(),
      updatedAt: new Date()
    });

    this.addIntegration({
      id: 'google-analytics',
      name: 'Google Analytics',
      description: 'Track website traffic and social media referrals',
      category: 'analytics',
      provider: 'Google',
      version: '4.0.0',
      logoUrl: '/integrations/google-analytics.png',
      screenshots: [],
      pricing: { type: 'free' },
      features: ['Traffic tracking', 'Conversion tracking', 'Custom reports'],
      requirements: ['Google Analytics account'],
      permissions: ['analytics_read'],
      webhookSupport: false,
      apiVersion: 'v4',
      documentation: {
        setupGuide: '/docs/ga-setup',
        apiDocs: '/docs/ga-api',
        examples: '/docs/ga-examples'
      },
      ratings: { average: 4.6, count: 2100, distribution: { 5: 1200, 4: 600, 3: 200, 2: 70, 1: 30 } },
      installation: { type: 'oauth', config: {} },
      status: 'active',
      tags: ['analytics', 'google', 'tracking'],
      createdAt: new Date(),
      updatedAt: new Date()
    });

    this.addIntegration({
      id: 'shopify',
      name: 'Shopify',
      description: 'Sync products and promote your e-commerce store',
      category: 'e_commerce',
      provider: 'Shopify',
      version: '2023.1',
      logoUrl: '/integrations/shopify.png',
      screenshots: [],
      pricing: { type: 'free' },
      features: ['Product sync', 'Order tracking', 'Inventory management'],
      requirements: ['Shopify store'],
      permissions: ['products_read', 'orders_read'],
      webhookSupport: true,
      apiVersion: '2023-01',
      documentation: {
        setupGuide: '/docs/shopify-setup',
        apiDocs: '/docs/shopify-api',
        examples: '/docs/shopify-examples'
      },
      ratings: { average: 4.7, count: 890, distribution: { 5: 600, 4: 200, 3: 60, 2: 20, 1: 10 } },
      installation: { type: 'oauth', config: {} },
      status: 'active',
      tags: ['ecommerce', 'products', 'sales'],
      createdAt: new Date(),
      updatedAt: new Date()
    });

    // Add more integrations...
    this.initializeWebhookHandlers();
  }

  private addIntegration(integration: Integration): void {
    this.integrations.set(integration.id, integration);
  }

  private initializeWebhookHandlers(): void {
    // Zapier webhook handler
    this.webhookHandlers.set('zapier.trigger', async (payload, headers) => {
      // Handle Zapier trigger
      logger.info('Processing Zapier webhook:', payload);
    });

    // Shopify webhook handler
    this.webhookHandlers.set('shopify.order.created', async (payload, headers) => {
      // Handle new Shopify order
      logger.info('Processing Shopify order webhook:', payload);
    });
  }

  // Helper methods
  private async generateMarketplaceStats(): Promise<MarketplaceStats> {
    const integrations = Array.from(this.integrations.values());
    
    return {
      totalIntegrations: integrations.length,
      activeInstallations: 0, // Would be calculated from database
      popularCategories: this.calculatePopularCategories(integrations),
      topRatedIntegrations: integrations
        .sort((a, b) => b.ratings.average - a.ratings.average)
        .slice(0, 5),
      recentlyAdded: integrations
        .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
        .slice(0, 5),
      trendingIntegrations: integrations
        .sort((a, b) => b.ratings.count - a.ratings.count)
        .slice(0, 5)
    };
  }

  private calculatePopularCategories(integrations: Integration[]): { category: string; count: number }[] {
    const categories = new Map<string, number>();
    
    integrations.forEach(integration => {
      const count = categories.get(integration.category) || 0;
      categories.set(integration.category, count + 1);
    });

    return Array.from(categories.entries())
      .map(([category, count]) => ({ category, count }))
      .sort((a, b) => b.count - a.count);
  }

  private async validateIntegrationConfig(integration: Integration, config: any): Promise<void> {
    // Validate configuration based on integration requirements
    // This would be specific to each integration
  }

  private async encryptCredentials(credentials: any): Promise<any> {
    // Encrypt sensitive credentials
    // Implementation would use proper encryption
    return credentials;
  }

  private async testIntegrationConnection(integration: Integration, instance: IntegrationInstance): Promise<{ success: boolean; error?: string }> {
    // Test connection to integration
    // Implementation would be specific to each integration
    return { success: true };
  }

  private generateInstanceId(): string {
    return `inst_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateEventId(): string {
    return `evt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private async storeIntegrationInstance(instance: IntegrationInstance): Promise<void> {
    // Store in database
    logger.debug(`Storing integration instance: ${instance.id}`);
  }

  private async getIntegrationInstance(instanceId: string): Promise<IntegrationInstance | null> {
    // Get from database
    return null;
  }

  private async updateIntegrationInstance(instance: IntegrationInstance): Promise<void> {
    // Update in database
    logger.debug(`Updating integration instance: ${instance.id}`);
  }

  private async scheduleSync(instance: IntegrationInstance): Promise<void> {
    // Schedule periodic sync
    logger.debug(`Scheduling sync for instance: ${instance.id}`);
  }

  private async performSync(integration: Integration, instance: IntegrationInstance): Promise<{ success: boolean; dataTransferred: number; error?: string }> {
    // Perform actual sync
    return { success: true, dataTransferred: 1000 };
  }

  private async storeWebhookEvent(event: WebhookEvent): Promise<void> {
    // Store webhook event
    logger.debug(`Storing webhook event: ${event.id}`);
  }

  private async updateWebhookEvent(event: WebhookEvent): Promise<void> {
    // Update webhook event
    logger.debug(`Updating webhook event: ${event.id}`);
  }

  private async getOrganizationInstances(organizationId: string): Promise<IntegrationInstance[]> {
    // Get organization instances from database
    return [];
  }

  private calculateSyncSuccessRate(instances: IntegrationInstance[]): number {
    if (instances.length === 0) return 0;
    
    const totalSyncs = instances.reduce((sum, i) => sum + i.metrics.totalSyncs, 0);
    const successfulSyncs = instances.reduce((sum, i) => sum + i.metrics.successfulSyncs, 0);
    
    return totalSyncs > 0 ? successfulSyncs / totalSyncs : 0;
  }
}
