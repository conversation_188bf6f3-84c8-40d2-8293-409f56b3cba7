import { 
  ChartBarIcon,
  CheckCircleIcon,
  DocumentTextIcon,
} from '@heroicons/react/24/outline'

interface ProjectPerformanceData {
  id: string
  name: string
  totalContent: number
  publishedContent: number
  status: string
}

interface ProjectPerformanceProps {
  data: ProjectPerformanceData[]
}

const ProjectPerformance: React.FC<ProjectPerformanceProps> = ({ data }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'bg-green-100 text-green-800'
      case 'PAUSED':
        return 'bg-yellow-100 text-yellow-800'
      case 'COMPLETED':
        return 'bg-blue-100 text-blue-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const calculatePublishRate = (published: number, total: number) => {
    if (total === 0) return 0
    return Math.round((published / total) * 100)
  }

  return (
    <div className="card">
      <div className="card-header">
        <h3 className="card-title">Project Performance</h3>
        <p className="card-description">Content creation and publishing by project</p>
      </div>
      <div className="card-content">
        {data.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            No project data available
          </div>
        ) : (
          <div className="space-y-4">
            {data.map((project) => {
              const publishRate = calculatePublishRate(project.publishedContent, project.totalContent)
              
              return (
                <div key={project.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-blue-50 rounded-lg">
                        <ChartBarIcon className="h-5 w-5 text-blue-600" />
                      </div>
                      <div>
                        <h4 className="font-medium text-gray-900">{project.name}</h4>
                        <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(project.status)}`}>
                          {project.status}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-3 gap-4 mb-3">
                    <div className="text-center">
                      <div className="flex items-center justify-center space-x-1 text-gray-600 mb-1">
                        <DocumentTextIcon className="h-4 w-4" />
                        <span className="text-xs">Total</span>
                      </div>
                      <div className="text-lg font-semibold text-gray-900">
                        {project.totalContent}
                      </div>
                    </div>
                    <div className="text-center">
                      <div className="flex items-center justify-center space-x-1 text-green-600 mb-1">
                        <CheckCircleIcon className="h-4 w-4" />
                        <span className="text-xs">Published</span>
                      </div>
                      <div className="text-lg font-semibold text-green-600">
                        {project.publishedContent}
                      </div>
                    </div>
                    <div className="text-center">
                      <div className="text-xs text-gray-600 mb-1">Publish Rate</div>
                      <div className="text-lg font-semibold text-blue-600">
                        {publishRate}%
                      </div>
                    </div>
                  </div>

                  {/* Progress Bar */}
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-green-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${publishRate}%` }}
                    />
                  </div>
                </div>
              )
            })}
          </div>
        )}
      </div>
    </div>
  )
}

export default ProjectPerformance
