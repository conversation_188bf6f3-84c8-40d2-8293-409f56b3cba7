version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: socialhub-postgres
    environment:
      POSTGRES_DB: socialhub_dev
      POSTGRES_USER: socialhub
      POSTGRES_PASSWORD: socialhub123
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/prisma/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - socialhub-network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: socialhub-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - socialhub-network

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    container_name: socialhub-backend
    environment:
      - NODE_ENV=development
      - DATABASE_URL=*************************************************/socialhub_dev
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=dev-jwt-secret-change-in-production
      - PORT=5000
      - FRONTEND_URL=http://localhost:3000
    ports:
      - "5000:5000"
    volumes:
      - ./backend:/app
      - /app/node_modules
    depends_on:
      - postgres
      - redis
    networks:
      - socialhub-network
    command: npm run dev

  # Frontend React App
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: socialhub-frontend
    environment:
      - VITE_API_URL=http://localhost:5000/api
      - VITE_WS_URL=http://localhost:5000
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend
    networks:
      - socialhub-network
    command: npm run dev

volumes:
  postgres_data:
  redis_data:

networks:
  socialhub-network:
    driver: bridge
