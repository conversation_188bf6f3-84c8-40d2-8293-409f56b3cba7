import express from 'express';
import { PrismaClient } from '@prisma/client';
import { AnalyticsService } from '../services/analyticsService.js';
import { SocialMediaIntegrationService } from '../services/socialMediaIntegrations.js';
import { authMiddleware } from '../middleware/auth.js';
import { logger } from '../utils/logger.js';

const router = express.Router();
const prisma = new PrismaClient();
const socialMediaService = new SocialMediaIntegrationService(prisma);
const analyticsService = new AnalyticsService(prisma, socialMediaService);

/**
 * @route GET /api/analytics/report
 * @desc Generate comprehensive analytics report
 * @access Private
 */
router.get('/report', authMiddleware, async (req, res) => {
  try {
    const { organizationId } = req.user;
    const { startDate, endDate, platforms } = req.query;

    if (!startDate || !endDate) {
      return res.status(400).json({
        success: false,
        message: 'Start date and end date are required'
      });
    }

    const platformList = platforms ? (platforms as string).split(',') : undefined;

    const report = await analyticsService.generateReport(
      organizationId,
      new Date(startDate as string),
      new Date(endDate as string),
      platformList
    );

    res.json({
      success: true,
      data: report
    });
  } catch (error) {
    logger.error('Error generating analytics report:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate analytics report'
    });
  }
});

/**
 * @route GET /api/analytics/overview
 * @desc Get analytics overview for dashboard
 * @access Private
 */
router.get('/overview', authMiddleware, async (req, res) => {
  try {
    const { organizationId } = req.user;
    const { period } = req.query;

    // Default to last 30 days
    const endDate = new Date();
    const startDate = new Date();

    switch (period) {
      case '7d':
        startDate.setDate(startDate.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(startDate.getDate() - 30);
        break;
      case '90d':
        startDate.setDate(startDate.getDate() - 90);
        break;
      default:
        startDate.setDate(startDate.getDate() - 30);
    }

    const report = await analyticsService.generateReport(
      organizationId,
      startDate,
      endDate
    );

    // Return simplified overview for dashboard
    const overview = {
      totalPosts: report.overview.totalPosts,
      totalEngagement: report.overview.totalEngagement,
      totalReach: report.overview.totalReach,
      averageEngagementRate: report.overview.averageEngagementRate,
      followerGrowth: report.overview.followerGrowth,
      topPerformingPlatform: report.overview.topPerformingPlatform,
      platformBreakdown: report.platformBreakdown.map(p => ({
        platform: p.platform,
        posts: p.posts,
        engagement: p.engagement,
        engagementRate: p.engagementRate
      })),
      recentContent: report.contentPerformance.slice(0, 5),
      trends: {
        engagement: report.trends.engagementTrend.slice(-7), // Last 7 days
        reach: report.trends.reachTrend.slice(-7)
      }
    };

    res.json({
      success: true,
      data: overview
    });
  } catch (error) {
    logger.error('Error getting analytics overview:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get analytics overview'
    });
  }
});

/**
 * @route GET /api/analytics/realtime
 * @desc Get real-time analytics metrics
 * @access Private
 */
router.get('/realtime', authMiddleware, async (req, res) => {
  try {
    const { organizationId } = req.user;

    const metrics = await analyticsService.getRealtimeMetrics(organizationId);

    res.json({
      success: true,
      data: metrics
    });
  } catch (error) {
    logger.error('Error getting realtime metrics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get realtime metrics'
    });
  }
});

/**
 * @route GET /api/analytics/competitor-analysis
 * @desc Get competitor analysis
 * @access Private
 */
router.get('/competitor-analysis', authMiddleware, async (req, res) => {
  try {
    const { organizationId } = req.user;
    const { competitors, platforms } = req.query;

    if (!competitors) {
      return res.status(400).json({
        success: false,
        message: 'Competitor handles are required'
      });
    }

    const competitorList = (competitors as string).split(',');
    const platformList = platforms ? (platforms as string).split(',') : ['INSTAGRAM', 'FACEBOOK'];

    const analysis = await analyticsService.getCompetitorAnalysis(
      organizationId,
      competitorList,
      platformList
    );

    res.json({
      success: true,
      data: analysis
    });
  } catch (error) {
    logger.error('Error getting competitor analysis:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get competitor analysis'
    });
  }
});

/**
 * @route GET /api/analytics/predictions
 * @desc Get predictive analytics insights
 * @access Private
 */
router.get('/predictions', authMiddleware, async (req, res) => {
  try {
    const { organizationId } = req.user;
    const { timeframe } = req.query;

    const validTimeframes = ['week', 'month', 'quarter'];
    const selectedTimeframe = validTimeframes.includes(timeframe as string)
      ? (timeframe as 'week' | 'month' | 'quarter')
      : 'month';

    const insights = await analyticsService.getPredictiveInsights(
      organizationId,
      selectedTimeframe
    );

    res.json({
      success: true,
      data: insights
    });
  } catch (error) {
    logger.error('Error getting predictive insights:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get predictive insights'
    });
  }
});

/**
 * @route POST /api/analytics/export
 * @desc Export analytics report
 * @access Private
 */
router.post('/export', authMiddleware, async (req, res) => {
  try {
    const { organizationId } = req.user;
    const { startDate, endDate, format, platforms } = req.body;

    if (!startDate || !endDate || !format) {
      return res.status(400).json({
        success: false,
        message: 'Start date, end date, and format are required'
      });
    }

    const validFormats = ['pdf', 'excel', 'csv'];
    if (!validFormats.includes(format)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid format. Must be pdf, excel, or csv'
      });
    }

    const result = await analyticsService.exportReport(
      organizationId,
      new Date(startDate),
      new Date(endDate),
      format,
      platforms
    );

    res.json({
      success: true,
      message: 'Report exported successfully',
      data: result
    });
  } catch (error) {
    logger.error('Error exporting report:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to export report'
    });
  }
});

/**
 * @route GET /api/analytics/dashboards
 * @desc Get custom dashboards
 * @access Private
 */
router.get('/dashboards', authMiddleware, async (req, res) => {
  try {
    const { organizationId } = req.user;

    const dashboards = await analyticsService.getCustomDashboards(organizationId);

    res.json({
      success: true,
      data: dashboards
    });
  } catch (error) {
    logger.error('Error getting custom dashboards:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get custom dashboards'
    });
  }
});

/**
 * @route POST /api/analytics/dashboards
 * @desc Create custom dashboard
 * @access Private
 */
router.post('/dashboards', authMiddleware, async (req, res) => {
  try {
    const { organizationId } = req.user;
    const { name, widgets, layout } = req.body;

    if (!name || !widgets || !layout) {
      return res.status(400).json({
        success: false,
        message: 'Name, widgets, and layout are required'
      });
    }

    const dashboard = await analyticsService.createCustomDashboard(
      organizationId,
      name,
      widgets,
      layout
    );

    res.json({
      success: true,
      message: 'Dashboard created successfully',
      data: dashboard
    });
  } catch (error) {
    logger.error('Error creating custom dashboard:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create custom dashboard'
    });
  }
});

/**
 * @route GET /api/analytics/performance
 * @desc Get performance metrics (alias for content-performance)
 * @access Private
 */
router.get('/performance', authMiddleware, async (req, res) => {
  try {
    const { organizationId } = req.user;
    const { startDate, endDate, platform, sortBy, limit } = req.query;

    // Default to last 30 days if no dates provided
    const endDateObj = endDate ? new Date(endDate as string) : new Date();
    const startDateObj = startDate ? new Date(startDate as string) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

    const platforms = platform ? [platform as string] : undefined;
    const report = await analyticsService.generateReport(
      organizationId,
      startDateObj,
      endDateObj,
      platforms
    );

    let contentPerformance = report.contentPerformance;

    // Sort by specified metric
    if (sortBy) {
      const sortField = sortBy as keyof typeof contentPerformance[0];
      contentPerformance.sort((a, b) => {
        const aValue = a[sortField] as number;
        const bValue = b[sortField] as number;
        return bValue - aValue;
      });
    }

    // Limit results
    if (limit) {
      contentPerformance = contentPerformance.slice(0, parseInt(limit as string));
    }

    res.json({
      success: true,
      data: {
        content: contentPerformance,
        summary: {
          totalContent: report.contentPerformance.length,
          averageEngagement: report.overview.averageEngagementRate,
          topPerformer: contentPerformance[0],
          performanceDistribution: {
            excellent: contentPerformance.filter(c => c.performance === 'excellent').length,
            good: contentPerformance.filter(c => c.performance === 'good').length,
            average: contentPerformance.filter(c => c.performance === 'average').length,
            poor: contentPerformance.filter(c => c.performance === 'poor').length
          }
        },
        overview: report.overview,
        trends: report.trends
      }
    });
  } catch (error) {
    logger.error('Error getting performance metrics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get performance metrics'
    });
  }
});

/**
 * @route GET /api/analytics/content-performance
 * @desc Get detailed content performance metrics
 * @access Private
 */
router.get('/content-performance', authMiddleware, async (req, res) => {
  try {
    const { organizationId } = req.user;
    const { startDate, endDate, platform, sortBy, limit } = req.query;

    if (!startDate || !endDate) {
      return res.status(400).json({
        success: false,
        message: 'Start date and end date are required'
      });
    }

    const platforms = platform ? [platform as string] : undefined;
    const report = await analyticsService.generateReport(
      organizationId,
      new Date(startDate as string),
      new Date(endDate as string),
      platforms
    );

    let contentPerformance = report.contentPerformance;

    // Sort by specified metric
    if (sortBy) {
      const sortField = sortBy as keyof typeof contentPerformance[0];
      contentPerformance.sort((a, b) => {
        const aValue = a[sortField] as number;
        const bValue = b[sortField] as number;
        return bValue - aValue;
      });
    }

    // Limit results
    if (limit) {
      contentPerformance = contentPerformance.slice(0, parseInt(limit as string));
    }

    res.json({
      success: true,
      data: {
        content: contentPerformance,
        summary: {
          totalContent: report.contentPerformance.length,
          averageEngagement: report.overview.averageEngagementRate,
          topPerformer: contentPerformance[0],
          performanceDistribution: {
            excellent: contentPerformance.filter(c => c.performance === 'excellent').length,
            good: contentPerformance.filter(c => c.performance === 'good').length,
            average: contentPerformance.filter(c => c.performance === 'average').length,
            poor: contentPerformance.filter(c => c.performance === 'poor').length
          }
        }
      }
    });
  } catch (error) {
    logger.error('Error getting content performance:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get content performance'
    });
  }
});

/**
 * @route GET /api/analytics/platforms
 * @desc Get platform comparison analytics
 * @access Private
 */
router.get('/platforms', authMiddleware, async (req, res) => {
  try {
    const { organizationId } = req.user;
    const { period, platforms } = req.query;

    // Default to last 30 days
    const endDate = new Date();
    const startDate = new Date();

    switch (period) {
      case '7d':
        startDate.setDate(startDate.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(startDate.getDate() - 30);
        break;
      case '90d':
        startDate.setDate(startDate.getDate() - 90);
        break;
      default:
        startDate.setDate(startDate.getDate() - 30);
    }

    const platformList = platforms ? (platforms as string).split(',') : undefined;
    const report = await analyticsService.generateReport(
      organizationId,
      startDate,
      endDate,
      platformList
    );

    res.json({
      success: true,
      data: {
        platformBreakdown: report.platformBreakdown,
        period: period || '30d'
      }
    });
  } catch (error) {
    logger.error('Error getting platform analytics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get platform analytics'
    });
  }
});

export default router;
