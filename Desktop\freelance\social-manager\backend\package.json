{"name": "socialhub-backend", "version": "1.0.0", "description": "SocialHub Pro Backend API", "main": "dist/server.js", "type": "module", "scripts": {"dev": "tsx watch src/server.ts", "build": "tsc", "start": "node dist/server.js", "test": "jest --config jest.config.cjs", "test:watch": "jest --config jest.config.cjs --watch", "test:coverage": "jest --config jest.config.cjs --coverage", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "db:migrate": "prisma migrate dev", "db:generate": "prisma generate", "db:seed": "tsx src/prisma/seed.ts", "db:studio": "prisma studio", "db:reset": "prisma migrate reset", "docker:build": "docker build -t socialhub-backend .", "docker:run": "docker run -p 5000:5000 socialhub-backend"}, "prisma": {"seed": "tsx prisma/seed.ts"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "@prisma/client": "^5.7.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-google-oauth20": "^2.0.0", "passport-local": "^1.0.0", "nodemailer": "^6.9.7", "multer": "^1.4.5-lts.1", "sharp": "^0.33.1", "aws-sdk": "^2.1506.0", "redis": "^4.6.11", "bull": "^4.12.2", "socket.io": "^4.7.4", "winston": "^3.11.0", "dotenv": "^16.3.1", "zod": "^3.22.4", "stripe": "^14.9.0", "axios": "^1.6.2", "date-fns": "^3.0.6", "uuid": "^9.0.1", "cron": "^3.1.6"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/compression": "^1.7.5", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/passport": "^1.0.16", "@types/passport-jwt": "^3.0.13", "@types/passport-google-oauth20": "^2.0.14", "@types/passport-local": "^1.0.38", "@types/nodemailer": "^6.4.14", "@types/multer": "^1.4.11", "@types/uuid": "^9.0.7", "@types/node": "^20.10.0", "@types/jest": "^29.5.8", "@types/supertest": "^2.0.16", "typescript": "^5.3.3", "tsx": "^4.6.2", "prisma": "^5.7.1", "jest": "^29.7.0", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "eslint": "^8.55.0", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "nodemon": "^3.0.2"}, "engines": {"node": ">=18.0.0"}, "keywords": ["social-media", "api", "nodejs", "express", "postgresql", "prisma", "typescript"], "author": "SocialHub Pro Team", "license": "MIT"}