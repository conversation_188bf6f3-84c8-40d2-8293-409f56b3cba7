import React, { useEffect, useState } from 'react';
import { StatusBar } from 'expo-status-bar';
import { Provider } from 'react-redux';
import { PersistGate } from 'redux-persist/integration/react';
import { NavigationContainer } from '@react-navigation/native';
import { Provider as PaperProvider } from 'react-native-paper';
import NetInfo from '@react-native-community/netinfo';
import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';

import { store, persistor } from './src/store/store';
import { theme } from './src/theme/theme';
import AppNavigator from './src/navigation/AppNavigator';
import LoadingScreen from './src/components/LoadingScreen';
import OfflineNotice from './src/components/OfflineNotice';
import { NotificationService } from './src/services/NotificationService';
import { OfflineService } from './src/services/OfflineService';

// Configure notifications
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
  }),
});

export default function App() {
  const [isConnected, setIsConnected] = useState<boolean>(true);
  const [isReady, setIsReady] = useState<boolean>(false);

  useEffect(() => {
    initializeApp();
  }, []);

  const initializeApp = async () => {
    try {
      // Initialize notification service
      await NotificationService.initialize();

      // Initialize offline service
      await OfflineService.initialize();

      // Set up network listener
      const unsubscribe = NetInfo.addEventListener(state => {
        setIsConnected(state.isConnected ?? false);
        
        if (state.isConnected) {
          // Sync offline data when connection is restored
          OfflineService.syncOfflineData();
        }
      });

      // Register for push notifications
      if (Device.isDevice) {
        await NotificationService.registerForPushNotifications();
      }

      setIsReady(true);

      return () => {
        unsubscribe();
      };
    } catch (error) {
      console.error('Error initializing app:', error);
      setIsReady(true); // Still allow app to load
    }
  };

  if (!isReady) {
    return <LoadingScreen />;
  }

  return (
    <Provider store={store}>
      <PersistGate loading={<LoadingScreen />} persistor={persistor}>
        <PaperProvider theme={theme}>
          <NavigationContainer>
            <StatusBar style="auto" />
            <AppNavigator />
            {!isConnected && <OfflineNotice />}
          </NavigationContainer>
        </PaperProvider>
      </PersistGate>
    </Provider>
  );
}
