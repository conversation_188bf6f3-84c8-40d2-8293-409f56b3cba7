import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '../ui/card';
import { <PERSON><PERSON> } from '../ui/button';
import { Badge } from '../ui/badge';
import { Check, Star, Zap, Crown } from 'lucide-react';

const PLANS = [
  {
    id: 'starter',
    name: 'Starter',
    price: 29,
    interval: 'month',
    description: 'Perfect for individuals and small businesses',
    icon: <Zap className="w-6 h-6" />,
    color: 'blue',
    popular: false,
    features: [
      '3 Social Media Accounts',
      '30 Posts per Month',
      'Basic Analytics',
      'Content Scheduler',
      'Email Support',
      '1 User Account'
    ],
    limits: {
      users: 1,
      socialAccounts: 3,
      postsPerMonth: 30,
      aiGenerations: 10,
      analyticsRetention: 30
    }
  },
  {
    id: 'professional',
    name: 'Professional',
    price: 79,
    interval: 'month',
    description: 'Ideal for growing businesses and agencies',
    icon: <Star className="w-6 h-6" />,
    color: 'purple',
    popular: true,
    features: [
      '10 Social Media Accounts',
      '100 Posts per Month',
      'Advanced Analytics',
      'AI Content Optimization',
      'Priority Support',
      '5 User Accounts',
      'Custom Branding',
      'Bulk Scheduling'
    ],
    limits: {
      users: 5,
      socialAccounts: 10,
      postsPerMonth: 100,
      aiGenerations: 50,
      analyticsRetention: 90
    }
  },
  {
    id: 'agency',
    name: 'Agency',
    price: 199,
    interval: 'month',
    description: 'For agencies managing multiple clients',
    icon: <Crown className="w-6 h-6" />,
    color: 'gold',
    popular: false,
    features: [
      '50 Social Media Accounts',
      '500 Posts per Month',
      'White-label Solution',
      'Client Management',
      'Advanced AI Features',
      '25 User Accounts',
      'API Access',
      'Dedicated Support'
    ],
    limits: {
      users: 25,
      socialAccounts: 50,
      postsPerMonth: 500,
      aiGenerations: 200,
      analyticsRetention: 365
    }
  }
];

export const SubscriptionPlans: React.FC = () => {
  const handleSelectPlan = (planId: string) => {
    console.log('Selected plan:', planId);
    // Handle plan selection
  };

  const getColorClasses = (color: string, variant: 'bg' | 'text' | 'border') => {
    const colors = {
      blue: {
        bg: 'bg-blue-500',
        text: 'text-blue-600',
        border: 'border-blue-500'
      },
      purple: {
        bg: 'bg-purple-500',
        text: 'text-purple-600',
        border: 'border-purple-500'
      },
      gold: {
        bg: 'bg-yellow-500',
        text: 'text-yellow-600',
        border: 'border-yellow-500'
      }
    };
    return colors[color as keyof typeof colors]?.[variant] || colors.blue[variant];
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-3xl font-bold text-gray-900 mb-4">Choose Your Plan</h2>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Select the perfect plan for your social media management needs. 
          All plans include our core features with different usage limits.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        {PLANS.map((plan) => (
          <Card 
            key={plan.id} 
            className={`relative ${plan.popular ? 'ring-2 ring-purple-500 shadow-lg' : ''}`}
          >
            {plan.popular && (
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                <Badge className="bg-purple-500 text-white px-4 py-1">
                  Most Popular
                </Badge>
              </div>
            )}
            
            <CardHeader className="text-center pb-4">
              <div className={`w-12 h-12 ${getColorClasses(plan.color, 'bg')} rounded-full flex items-center justify-center text-white mx-auto mb-4`}>
                {plan.icon}
              </div>
              <CardTitle className="text-2xl">{plan.name}</CardTitle>
              <p className="text-gray-600 text-sm">{plan.description}</p>
              <div className="mt-4">
                <span className="text-4xl font-bold text-gray-900">${plan.price}</span>
                <span className="text-gray-600">/{plan.interval}</span>
              </div>
            </CardHeader>

            <CardContent className="space-y-6">
              <ul className="space-y-3">
                {plan.features.map((feature, index) => (
                  <li key={index} className="flex items-center space-x-3">
                    <Check className="w-5 h-5 text-green-500 flex-shrink-0" />
                    <span className="text-sm text-gray-700">{feature}</span>
                  </li>
                ))}
              </ul>

              <div className="border-t pt-4">
                <h4 className="font-medium text-gray-900 mb-3">Usage Limits</h4>
                <div className="space-y-2 text-sm text-gray-600">
                  <div className="flex justify-between">
                    <span>Users:</span>
                    <span className="font-medium">{plan.limits.users}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Social Accounts:</span>
                    <span className="font-medium">{plan.limits.socialAccounts}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Posts/Month:</span>
                    <span className="font-medium">{plan.limits.postsPerMonth}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>AI Generations:</span>
                    <span className="font-medium">{plan.limits.aiGenerations}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Analytics Retention:</span>
                    <span className="font-medium">{plan.limits.analyticsRetention} days</span>
                  </div>
                </div>
              </div>

              <Button 
                className={`w-full ${plan.popular ? 'bg-purple-600 hover:bg-purple-700' : ''}`}
                onClick={() => handleSelectPlan(plan.id)}
              >
                {plan.popular ? 'Get Started' : 'Choose Plan'}
              </Button>

              {plan.id === 'professional' && (
                <div className="text-center">
                  <p className="text-xs text-gray-500">
                    Save 20% with annual billing
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Enterprise Option */}
      <Card className="bg-gradient-to-r from-gray-50 to-gray-100">
        <CardContent className="p-8 text-center">
          <div className="max-w-2xl mx-auto">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">Enterprise Solution</h3>
            <p className="text-gray-600 mb-6">
              Need more than 50 social accounts or custom features? 
              Our enterprise solution offers unlimited usage, custom integrations, 
              and dedicated support.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button variant="outline" size="lg">
                Contact Sales
              </Button>
              <Button variant="outline" size="lg">
                Schedule Demo
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* FAQ Section */}
      <Card>
        <CardHeader>
          <CardTitle>Frequently Asked Questions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Can I change plans anytime?</h4>
              <p className="text-sm text-gray-600">
                Yes, you can upgrade or downgrade your plan at any time. 
                Changes take effect immediately.
              </p>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-2">What happens if I exceed my limits?</h4>
              <p className="text-sm text-gray-600">
                We'll notify you when you're approaching your limits. 
                You can upgrade your plan or purchase additional usage.
              </p>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Is there a free trial?</h4>
              <p className="text-sm text-gray-600">
                Yes, all plans come with a 14-day free trial. 
                No credit card required to start.
              </p>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Do you offer annual discounts?</h4>
              <p className="text-sm text-gray-600">
                Yes, save 20% when you choose annual billing on any plan.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SubscriptionPlans;
