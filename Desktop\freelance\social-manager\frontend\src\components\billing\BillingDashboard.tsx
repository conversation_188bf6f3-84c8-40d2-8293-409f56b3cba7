import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Progress } from '../ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { 
  CreditCard, 
  Download, 
  Calendar, 
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Clock,
  DollarSign,
  Users,
  Zap,
  Settings
} from 'lucide-react';
import useBilling from '../../hooks/useBilling';
import { SubscriptionPlans } from './SubscriptionPlans.tsx';
import { PaymentMethods } from './PaymentMethods.tsx';
import { InvoiceHistory } from './InvoiceHistory.tsx';
import { UsageMetrics } from './UsageMetrics.tsx';

export const BillingDashboard: React.FC = () => {
  const {
    currentSubscription,
    usage,
    invoices,
    loading,
    error,
    isSubscribed,
    getCurrentPlan,
    getDaysUntilBilling,
    getNextBillingAmount,
    getUsagePercentage,
    getUsageStatus,
    formatPrice,
    getPlanBadgeColor
  } = useBilling();

  const [activeTab, setActiveTab] = useState('overview');

  const currentPlan = getCurrentPlan();
  const daysUntilBilling = getDaysUntilBilling();
  const nextBillingAmount = getNextBillingAmount();

  const usageMetrics = [
    {
      name: 'Users',
      key: 'users' as const,
      current: usage?.users || 0,
      limit: currentPlan?.limits.users || 0,
      icon: Users
    },
    {
      name: 'Social Accounts',
      key: 'socialAccounts' as const,
      current: usage?.socialAccounts || 0,
      limit: currentPlan?.limits.socialAccounts || 0,
      icon: Zap
    },
    {
      name: 'Posts Published',
      key: 'postsPublished' as const,
      current: usage?.postsPublished || 0,
      limit: currentPlan?.limits.postsPerMonth || 0,
      icon: TrendingUp
    },
    {
      name: 'AI Generations',
      key: 'aiGenerations' as const,
      current: usage?.aiGenerations || 0,
      limit: currentPlan?.limits.aiGenerations || 0,
      icon: Zap
    }
  ];

  if (loading && !currentSubscription) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Billing & Subscription</h1>
          <p className="text-gray-600 mt-1">
            Manage your subscription, usage, and billing information
          </p>
        </div>
        <div className="flex space-x-3">
          <Button variant="outline">
            <Download className="w-4 h-4 mr-2" />
            Download Invoice
          </Button>
          <Button variant="outline">
            <Settings className="w-4 h-4 mr-2" />
            Billing Settings
          </Button>
        </div>
      </div>

      {/* Error Alert */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 flex items-center">
          <AlertTriangle className="w-5 h-5 text-red-500 mr-3" />
          <span className="text-red-700">{error}</span>
        </div>
      )}

      {/* Subscription Status */}
      {currentSubscription && (
        <Card className="border-l-4 border-l-blue-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="p-3 bg-blue-100 rounded-full">
                  <CreditCard className="w-6 h-6 text-blue-600" />
                </div>
                <div>
                  <div className="flex items-center space-x-2">
                    <h3 className="text-lg font-semibold text-gray-900">
                      {currentPlan?.name} Plan
                    </h3>
                    <Badge 
                      style={{ backgroundColor: getPlanBadgeColor(currentPlan?.name || '') }}
                      className="text-white"
                    >
                      {currentSubscription.subscription.status}
                    </Badge>
                  </div>
                  <p className="text-gray-600">
                    {formatPrice(currentPlan?.price || 0)} / {currentPlan?.interval}
                  </p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-sm text-gray-600">Next billing</p>
                <p className="text-lg font-semibold text-gray-900">
                  {daysUntilBilling} days
                </p>
                <p className="text-sm text-gray-600">
                  {formatPrice(nextBillingAmount)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Current Plan</p>
                <p className="text-2xl font-bold text-gray-900">
                  {currentPlan?.name || 'Free'}
                </p>
              </div>
              <div className="p-3 bg-blue-100 rounded-full">
                <CreditCard className="w-6 h-6 text-blue-600" />
              </div>
            </div>
            <p className="text-xs text-gray-500 mt-2">
              {isSubscribed() ? 'Active subscription' : 'No active subscription'}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Monthly Spend</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatPrice(currentPlan?.price || 0)}
                </p>
              </div>
              <div className="p-3 bg-green-100 rounded-full">
                <DollarSign className="w-6 h-6 text-green-600" />
              </div>
            </div>
            <p className="text-xs text-gray-500 mt-2">
              Billed {currentPlan?.interval}ly
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Next Billing</p>
                <p className="text-2xl font-bold text-gray-900">{daysUntilBilling}</p>
              </div>
              <div className="p-3 bg-orange-100 rounded-full">
                <Calendar className="w-6 h-6 text-orange-600" />
              </div>
            </div>
            <p className="text-xs text-gray-500 mt-2">
              Days remaining
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Invoices</p>
                <p className="text-2xl font-bold text-gray-900">{invoices.length}</p>
              </div>
              <div className="p-3 bg-purple-100 rounded-full">
                <Download className="w-6 h-6 text-purple-600" />
              </div>
            </div>
            <p className="text-xs text-gray-500 mt-2">
              Total invoices
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Usage Overview */}
      {currentSubscription && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <TrendingUp className="w-5 h-5 mr-2" />
              Usage Overview
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {usageMetrics.map((metric) => {
                const percentage = getUsagePercentage(metric.key);
                const status = getUsageStatus(metric.key);
                const Icon = metric.icon;

                return (
                  <div key={metric.key} className="space-y-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Icon className="w-4 h-4 text-gray-600" />
                        <span className="text-sm font-medium text-gray-700">
                          {metric.name}
                        </span>
                      </div>
                      <Badge variant={status === 'exceeded' ? 'destructive' : 'secondary'}>
                        {status}
                      </Badge>
                    </div>
                    <div className="space-y-1">
                      <div className="flex justify-between text-sm">
                        <span>{metric.current}</span>
                        <span className="text-gray-500">
                          {metric.limit === 0 ? 'Unlimited' : `of ${metric.limit}`}
                        </span>
                      </div>
                      <Progress 
                        value={percentage} 
                        className={`h-2 ${
                          status === 'exceeded' ? 'bg-red-100' : 
                          status === 'high' ? 'bg-orange-100' : 'bg-gray-100'
                        }`}
                      />
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="plans">Plans</TabsTrigger>
          <TabsTrigger value="payment">Payment</TabsTrigger>
          <TabsTrigger value="invoices">Invoices</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <UsageMetrics />
        </TabsContent>

        <TabsContent value="plans">
          <SubscriptionPlans />
        </TabsContent>

        <TabsContent value="payment">
          <PaymentMethods />
        </TabsContent>

        <TabsContent value="invoices">
          <InvoiceHistory />
        </TabsContent>
      </Tabs>

      {/* Billing Alerts */}
      {currentSubscription && (
        <div className="space-y-4">
          {/* High Usage Warning */}
          {usageMetrics.some(metric => getUsageStatus(metric.key) === 'high') && (
            <Card className="border-orange-200 bg-orange-50">
              <CardContent className="p-4">
                <div className="flex items-center space-x-3">
                  <AlertTriangle className="w-5 h-5 text-orange-600" />
                  <div>
                    <h4 className="font-medium text-orange-800">High Usage Alert</h4>
                    <p className="text-sm text-orange-700">
                      You're approaching your plan limits. Consider upgrading to avoid service interruption.
                    </p>
                  </div>
                  <Button variant="outline" size="sm" className="ml-auto">
                    Upgrade Plan
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Billing Reminder */}
          {daysUntilBilling <= 7 && (
            <Card className="border-blue-200 bg-blue-50">
              <CardContent className="p-4">
                <div className="flex items-center space-x-3">
                  <Clock className="w-5 h-5 text-blue-600" />
                  <div>
                    <h4 className="font-medium text-blue-800">Upcoming Billing</h4>
                    <p className="text-sm text-blue-700">
                      Your next billing cycle starts in {daysUntilBilling} days. 
                      Amount: {formatPrice(nextBillingAmount)}
                    </p>
                  </div>
                  <Button variant="outline" size="sm" className="ml-auto">
                    View Details
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Subscription Status */}
          {currentSubscription.subscription.cancelAtPeriodEnd && (
            <Card className="border-red-200 bg-red-50">
              <CardContent className="p-4">
                <div className="flex items-center space-x-3">
                  <AlertTriangle className="w-5 h-5 text-red-600" />
                  <div>
                    <h4 className="font-medium text-red-800">Subscription Ending</h4>
                    <p className="text-sm text-red-700">
                      Your subscription will end on {new Date(currentSubscription.subscription.currentPeriodEnd).toLocaleDateString()}.
                      Reactivate to continue using premium features.
                    </p>
                  </div>
                  <Button variant="outline" size="sm" className="ml-auto">
                    Reactivate
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      )}

      {/* No Subscription State */}
      {!currentSubscription && (
        <Card className="text-center py-12">
          <CardContent>
            <div className="space-y-4">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto">
                <CreditCard className="w-8 h-8 text-gray-400" />
              </div>
              <div>
                <h3 className="text-lg font-medium text-gray-900">No Active Subscription</h3>
                <p className="text-gray-600 mt-1">
                  Choose a plan to unlock premium features and start managing your social media like a pro
                </p>
              </div>
              <Button 
                className="bg-blue-600 hover:bg-blue-700"
                onClick={() => setActiveTab('plans')}
              >
                <CreditCard className="w-4 h-4 mr-2" />
                Choose a Plan
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default BillingDashboard;
