import { useState } from 'react'
import { useIntegrations } from '../../hooks/useIntegrations'
import LoadingSpinner from '../../components/ui/LoadingSpinner'
import IntegrationCard from '../../components/integrations/IntegrationCard'
import ConnectPlatformModal from '../../components/integrations/ConnectPlatformModal'
import { 
  PlusIcon,
  LinkIcon,
} from '@heroicons/react/24/outline'

const IntegrationsPage: React.FC = () => {
  const [showConnectModal, setShowConnectModal] = useState(false)
  const { data: integrations, isLoading, error } = useIntegrations()

  const availablePlatforms = [
    { 
      id: 'INSTAGRAM', 
      name: 'Instagram', 
      icon: '📷', 
      color: 'bg-pink-500',
      description: 'Share photos, stories, and reels'
    },
    { 
      id: 'FACEBOOK', 
      name: 'Facebook', 
      icon: '👥', 
      color: 'bg-blue-600',
      description: 'Connect with your audience'
    },
    { 
      id: 'TWITTER', 
      name: 'Twitter', 
      icon: '🐦', 
      color: 'bg-sky-500',
      description: 'Share quick updates and thoughts'
    },
    { 
      id: 'LINKEDIN', 
      name: 'LinkedIn', 
      icon: '💼', 
      color: 'bg-blue-700',
      description: 'Professional networking and content'
    },
    { 
      id: 'TIKTOK', 
      name: 'TikTok', 
      icon: '🎵', 
      color: 'bg-black',
      description: 'Short-form video content'
    },
    { 
      id: 'YOUTUBE', 
      name: 'YouTube', 
      icon: '📺', 
      color: 'bg-red-600',
      description: 'Video content and tutorials'
    },
    { 
      id: 'PINTEREST', 
      name: 'Pinterest', 
      icon: '📌', 
      color: 'bg-red-700',
      description: 'Visual discovery and inspiration'
    },
  ]

  // Get connected platform IDs
  const connectedPlatforms = integrations?.map(integration => integration.platform) || []

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-600">Failed to load integrations. Please try again.</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Integrations</h1>
          <p className="text-gray-600">Connect your social media accounts to manage content</p>
        </div>
        
        <button
          onClick={() => setShowConnectModal(true)}
          className="btn-primary flex items-center space-x-2"
        >
          <PlusIcon className="h-5 w-5" />
          <span>Connect Platform</span>
        </button>
      </div>

      {/* Connected Integrations */}
      {integrations && integrations.length > 0 && (
        <div>
          <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center space-x-2">
            <LinkIcon className="h-5 w-5" />
            <span>Connected Accounts</span>
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {integrations.map((integration) => (
              <IntegrationCard key={integration.id} integration={integration} />
            ))}
          </div>
        </div>
      )}

      {/* Available Platforms */}
      <div>
        <h2 className="text-lg font-semibold text-gray-900 mb-4">
          {integrations && integrations.length > 0 ? 'Available Platforms' : 'Connect Your First Platform'}
        </h2>
        
        {integrations && integrations.length === 0 ? (
          <div className="card">
            <div className="card-content text-center py-12">
              <div className="text-6xl mb-4">🔗</div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No integrations yet</h3>
              <p className="text-gray-500 mb-6">
                Connect your social media accounts to start managing your content across platforms
              </p>
              <button
                onClick={() => setShowConnectModal(true)}
                className="btn-primary"
              >
                Connect Your First Platform
              </button>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {availablePlatforms
              .filter(platform => !connectedPlatforms.includes(platform.id))
              .map((platform) => (
                <div
                  key={platform.id}
                  className="card hover:shadow-md transition-shadow cursor-pointer"
                  onClick={() => setShowConnectModal(true)}
                >
                  <div className="card-content">
                    <div className="flex items-center space-x-3 mb-3">
                      <div className={`p-2 rounded-lg ${platform.color} text-white`}>
                        <span className="text-lg">{platform.icon}</span>
                      </div>
                      <div>
                        <h3 className="font-medium text-gray-900">{platform.name}</h3>
                      </div>
                    </div>
                    <p className="text-sm text-gray-600 mb-4">{platform.description}</p>
                    <button className="btn-outline w-full text-sm">
                      Connect {platform.name}
                    </button>
                  </div>
                </div>
              ))}
          </div>
        )}
      </div>

      {/* Connect Platform Modal */}
      {showConnectModal && (
        <ConnectPlatformModal
          availablePlatforms={availablePlatforms.filter(
            platform => !connectedPlatforms.includes(platform.id)
          )}
          onClose={() => setShowConnectModal(false)}
          onSuccess={() => {
            setShowConnectModal(false)
          }}
        />
      )}
    </div>
  )
}

export default IntegrationsPage
