import { Project } from '../../types/project'
import { ContentStatus, ContentType, Platform } from '../../types/content'

interface ContentFiltersProps {
  filters: {
    projectId: string
    status: string
    type: string
    platforms: string
  }
  projects: Project[]
  onFiltersChange: (filters: any) => void
}

const ContentFilters: React.FC<ContentFiltersProps> = ({
  filters,
  projects,
  onFiltersChange,
}) => {
  const handleFilterChange = (key: string, value: string) => {
    onFiltersChange({
      ...filters,
      [key]: value,
    })
  }

  const clearFilters = () => {
    onFiltersChange({
      projectId: '',
      status: '',
      type: '',
      platforms: '',
    })
  }

  const statusOptions: ContentStatus[] = [
    'DRAFT',
    'REVIEW', 
    'APPROVED',
    'SCHEDULED',
    'PUBLISHED',
    'FAILED'
  ]

  const typeOptions: ContentType[] = [
    'POST',
    'STORY',
    'REEL',
    'VIDEO',
    'ARTICLE',
    'CAROUSEL'
  ]

  const platformOptions: Platform[] = [
    'INSTAGRAM',
    'FACEBOOK',
    'TWITTER',
    'LINKEDIN',
    'TIKTOK',
    'YOUTUBE',
    'PINTEREST'
  ]

  return (
    <div className="card">
      <div className="card-content">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900">Filters</h3>
          <button
            onClick={clearFilters}
            className="text-sm text-gray-500 hover:text-gray-700"
          >
            Clear all
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Project
            </label>
            <select
              value={filters.projectId}
              onChange={(e) => handleFilterChange('projectId', e.target.value)}
              className="input"
            >
              <option value="">All projects</option>
              {projects.map((project) => (
                <option key={project.id} value={project.id}>
                  {project.name}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Status
            </label>
            <select
              value={filters.status}
              onChange={(e) => handleFilterChange('status', e.target.value)}
              className="input"
            >
              <option value="">All statuses</option>
              {statusOptions.map((status) => (
                <option key={status} value={status}>
                  {status.replace('_', ' ')}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Type
            </label>
            <select
              value={filters.type}
              onChange={(e) => handleFilterChange('type', e.target.value)}
              className="input"
            >
              <option value="">All types</option>
              {typeOptions.map((type) => (
                <option key={type} value={type}>
                  {type}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Platform
            </label>
            <select
              value={filters.platforms}
              onChange={(e) => handleFilterChange('platforms', e.target.value)}
              className="input"
            >
              <option value="">All platforms</option>
              {platformOptions.map((platform) => (
                <option key={platform} value={platform}>
                  {platform}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ContentFilters
