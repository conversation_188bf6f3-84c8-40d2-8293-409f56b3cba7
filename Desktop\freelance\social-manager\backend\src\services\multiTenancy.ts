import { PrismaClient } from '@prisma/client';
import { Request, Response, NextFunction } from 'express';
import { logger } from '../utils/logger.js';
import { CacheService } from '../config/redis.js';

export interface TenantConfig {
  id: string;
  name: string;
  domain?: string;
  subdomain?: string;
  branding: {
    logo?: string;
    primaryColor: string;
    secondaryColor: string;
    fontFamily?: string;
    customCSS?: string;
  };
  features: {
    whiteLabel: boolean;
    customDomain: boolean;
    apiAccess: boolean;
    advancedAnalytics: boolean;
    customIntegrations: boolean;
    sso: boolean;
  };
  limits: {
    users: number;
    projects: number;
    storage: number; // in MB
    apiCalls: number; // per month
    socialAccounts: number;
  };
  settings: {
    timezone: string;
    dateFormat: string;
    currency: string;
    language: string;
    emailNotifications: boolean;
    dataRetention: number; // in days
  };
}

export class MultiTenancyService {
  private prisma: PrismaClient;
  private tenantCache = new Map<string, TenantConfig>();

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }

  /**
   * Middleware to resolve tenant from request
   */
  tenantResolver() {
    return async (req: Request, res: Response, next: NextFunction) => {
      try {
        let tenantId: string | null = null;

        // Method 1: Subdomain resolution
        const host = req.headers.host;
        if (host) {
          const subdomain = this.extractSubdomain(host);
          if (subdomain && subdomain !== 'www' && subdomain !== 'api') {
            tenantId = await this.getTenantBySubdomain(subdomain);
          }
        }

        // Method 2: Custom domain resolution
        if (!tenantId && host) {
          tenantId = await this.getTenantByDomain(host);
        }

        // Method 3: Header-based resolution (for API calls)
        if (!tenantId) {
          const tenantHeader = req.headers['x-tenant-id'] as string;
          if (tenantHeader) {
            tenantId = tenantHeader;
          }
        }

        // Method 4: User's organization (fallback)
        if (!tenantId && req.user?.organizationId) {
          tenantId = req.user.organizationId;
        }

        if (!tenantId) {
          return res.status(400).json({ error: 'Tenant not found' });
        }

        // Load tenant configuration
        const tenant = await this.getTenantConfig(tenantId);
        if (!tenant) {
          return res.status(404).json({ error: 'Tenant configuration not found' });
        }

        // Add tenant to request
        req.tenant = tenant;
        
        // Set tenant context for database queries
        req.tenantId = tenantId;

        next();
      } catch (error) {
        logger.error('Error resolving tenant:', error);
        return res.status(500).json({ error: 'Internal server error' });
      }
    };
  }

  /**
   * Get tenant configuration
   */
  async getTenantConfig(tenantId: string): Promise<TenantConfig | null> {
    try {
      // Check cache first
      if (this.tenantCache.has(tenantId)) {
        return this.tenantCache.get(tenantId)!;
      }

      // Check Redis cache
      const cached = await CacheService.get(`tenant:${tenantId}`);
      if (cached) {
        const config = JSON.parse(cached);
        this.tenantCache.set(tenantId, config);
        return config;
      }

      // Load from database
      const organization = await this.prisma.organization.findUnique({
        where: { id: tenantId },
        include: {
          subscription: true,
          settings: true
        }
      });

      if (!organization) {
        return null;
      }

      const config: TenantConfig = {
        id: organization.id,
        name: organization.name,
        domain: organization.customDomain,
        subdomain: organization.subdomain,
        branding: {
          logo: organization.logo,
          primaryColor: organization.primaryColor || '#3B82F6',
          secondaryColor: organization.secondaryColor || '#1F2937',
          fontFamily: organization.fontFamily,
          customCSS: organization.customCSS
        },
        features: {
          whiteLabel: organization.subscription?.plan === 'ENTERPRISE',
          customDomain: organization.subscription?.plan === 'ENTERPRISE',
          apiAccess: ['PROFESSIONAL', 'AGENCY', 'ENTERPRISE'].includes(organization.subscription?.plan || ''),
          advancedAnalytics: ['AGENCY', 'ENTERPRISE'].includes(organization.subscription?.plan || ''),
          customIntegrations: organization.subscription?.plan === 'ENTERPRISE',
          sso: ['AGENCY', 'ENTERPRISE'].includes(organization.subscription?.plan || '')
        },
        limits: this.getPlanLimits(organization.subscription?.plan || 'FREE'),
        settings: {
          timezone: organization.settings?.timezone || 'UTC',
          dateFormat: organization.settings?.dateFormat || 'MM/DD/YYYY',
          currency: organization.settings?.currency || 'USD',
          language: organization.settings?.language || 'en',
          emailNotifications: organization.settings?.emailNotifications ?? true,
          dataRetention: organization.settings?.dataRetention || 365
        }
      };

      // Cache the configuration
      this.tenantCache.set(tenantId, config);
      await CacheService.set(`tenant:${tenantId}`, JSON.stringify(config), 3600); // 1 hour

      return config;
    } catch (error) {
      logger.error('Error getting tenant config:', error);
      return null;
    }
  }

  /**
   * Update tenant configuration
   */
  async updateTenantConfig(
    tenantId: string,
    updates: Partial<TenantConfig>
  ): Promise<TenantConfig | null> {
    try {
      const updateData: any = {};

      if (updates.name) updateData.name = updates.name;
      if (updates.domain) updateData.customDomain = updates.domain;
      if (updates.subdomain) updateData.subdomain = updates.subdomain;

      if (updates.branding) {
        if (updates.branding.logo) updateData.logo = updates.branding.logo;
        if (updates.branding.primaryColor) updateData.primaryColor = updates.branding.primaryColor;
        if (updates.branding.secondaryColor) updateData.secondaryColor = updates.branding.secondaryColor;
        if (updates.branding.fontFamily) updateData.fontFamily = updates.branding.fontFamily;
        if (updates.branding.customCSS) updateData.customCSS = updates.branding.customCSS;
      }

      await this.prisma.organization.update({
        where: { id: tenantId },
        data: updateData
      });

      // Update settings if provided
      if (updates.settings) {
        await this.prisma.organizationSettings.upsert({
          where: { organizationId: tenantId },
          create: {
            organizationId: tenantId,
            ...updates.settings
          },
          update: updates.settings
        });
      }

      // Clear cache
      this.tenantCache.delete(tenantId);
      await CacheService.del(`tenant:${tenantId}`);

      // Return updated config
      return await this.getTenantConfig(tenantId);
    } catch (error) {
      logger.error('Error updating tenant config:', error);
      return null;
    }
  }

  /**
   * Check if tenant has feature enabled
   */
  async hasFeature(tenantId: string, feature: keyof TenantConfig['features']): Promise<boolean> {
    const config = await this.getTenantConfig(tenantId);
    return config?.features[feature] || false;
  }

  /**
   * Check if tenant is within limits
   */
  async checkLimits(tenantId: string, resource: keyof TenantConfig['limits']): Promise<{
    withinLimit: boolean;
    current: number;
    limit: number;
  }> {
    const config = await this.getTenantConfig(tenantId);
    if (!config) {
      return { withinLimit: false, current: 0, limit: 0 };
    }

    const limit = config.limits[resource];
    let current = 0;

    switch (resource) {
      case 'users':
        current = await this.prisma.user.count({
          where: { organizationId: tenantId }
        });
        break;
      case 'projects':
        current = await this.prisma.project.count({
          where: { organizationId: tenantId }
        });
        break;
      case 'socialAccounts':
        current = await this.prisma.socialAccount.count({
          where: { organizationId: tenantId }
        });
        break;
      case 'storage':
        // This would require calculating actual storage usage
        current = 0; // Placeholder
        break;
      case 'apiCalls':
        // This would require tracking API usage
        current = 0; // Placeholder
        break;
    }

    return {
      withinLimit: current < limit,
      current,
      limit
    };
  }

  /**
   * Generate white-label CSS
   */
  generateWhiteLabelCSS(config: TenantConfig): string {
    const { branding } = config;
    
    return `
      :root {
        --primary-color: ${branding.primaryColor};
        --secondary-color: ${branding.secondaryColor};
        --font-family: ${branding.fontFamily || 'Inter, sans-serif'};
      }
      
      .brand-primary {
        color: var(--primary-color) !important;
      }
      
      .bg-brand-primary {
        background-color: var(--primary-color) !important;
      }
      
      .border-brand-primary {
        border-color: var(--primary-color) !important;
      }
      
      .brand-secondary {
        color: var(--secondary-color) !important;
      }
      
      .bg-brand-secondary {
        background-color: var(--secondary-color) !important;
      }
      
      body {
        font-family: var(--font-family);
      }
      
      ${branding.customCSS || ''}
    `;
  }

  /**
   * Get tenant by subdomain
   */
  private async getTenantBySubdomain(subdomain: string): Promise<string | null> {
    try {
      const organization = await this.prisma.organization.findFirst({
        where: { subdomain },
        select: { id: true }
      });
      return organization?.id || null;
    } catch (error) {
      logger.error('Error getting tenant by subdomain:', error);
      return null;
    }
  }

  /**
   * Get tenant by custom domain
   */
  private async getTenantByDomain(domain: string): Promise<string | null> {
    try {
      const organization = await this.prisma.organization.findFirst({
        where: { customDomain: domain },
        select: { id: true }
      });
      return organization?.id || null;
    } catch (error) {
      logger.error('Error getting tenant by domain:', error);
      return null;
    }
  }

  /**
   * Extract subdomain from host
   */
  private extractSubdomain(host: string): string | null {
    const parts = host.split('.');
    if (parts.length >= 3) {
      return parts[0];
    }
    return null;
  }

  /**
   * Get plan limits based on subscription plan
   */
  private getPlanLimits(plan: string): TenantConfig['limits'] {
    const limits = {
      FREE: {
        users: 1,
        projects: 3,
        storage: 100, // 100MB
        apiCalls: 1000,
        socialAccounts: 3
      },
      STARTER: {
        users: 3,
        projects: 10,
        storage: 1000, // 1GB
        apiCalls: 10000,
        socialAccounts: 10
      },
      PROFESSIONAL: {
        users: 10,
        projects: 50,
        storage: 5000, // 5GB
        apiCalls: 50000,
        socialAccounts: 25
      },
      AGENCY: {
        users: 50,
        projects: 200,
        storage: 20000, // 20GB
        apiCalls: 200000,
        socialAccounts: 100
      },
      ENTERPRISE: {
        users: -1, // Unlimited
        projects: -1, // Unlimited
        storage: -1, // Unlimited
        apiCalls: -1, // Unlimited
        socialAccounts: -1 // Unlimited
      }
    };

    return limits[plan as keyof typeof limits] || limits.FREE;
  }
}

/**
 * Middleware to enforce tenant isolation
 */
export function tenantIsolation() {
  return (req: Request, res: Response, next: NextFunction) => {
    // Add tenant filter to all database queries
    if (req.tenantId) {
      // This would be implemented at the Prisma level
      // For now, we'll add it to the request context
      req.dbFilter = { organizationId: req.tenantId };
    }
    next();
  };
}

/**
 * Middleware to check feature access
 */
export function requireFeature(feature: keyof TenantConfig['features']) {
  return async (req: Request, res: Response, next: NextFunction) => {
    if (!req.tenant) {
      return res.status(400).json({ error: 'Tenant not found' });
    }

    if (!req.tenant.features[feature]) {
      return res.status(403).json({ 
        error: 'Feature not available',
        feature,
        plan: 'upgrade_required'
      });
    }

    next();
  };
}

/**
 * Middleware to check resource limits
 */
export function checkResourceLimit(resource: keyof TenantConfig['limits']) {
  return async (req: Request, res: Response, next: NextFunction) => {
    if (!req.tenant || !req.tenantId) {
      return res.status(400).json({ error: 'Tenant not found' });
    }

    const multiTenancy = new MultiTenancyService(req.prisma || new PrismaClient());
    const limitCheck = await multiTenancy.checkLimits(req.tenantId, resource);

    if (!limitCheck.withinLimit) {
      return res.status(429).json({
        error: 'Resource limit exceeded',
        resource,
        current: limitCheck.current,
        limit: limitCheck.limit
      });
    }

    next();
  };
}
