import { useState } from 'react'
import { ContentItem, Platform, ContentStatus, ContentType } from '../../types/content'
import { 
  CalendarIcon,
  ClockIcon,
  EllipsisVerticalIcon,
  CheckIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
} from '@heroicons/react/24/outline'
import { Menu, Transition } from '@headlessui/react'
import { Fragment } from 'react'
import { format } from 'date-fns'

interface ContentCardProps {
  item: ContentItem
  viewMode: 'grid' | 'list'
  isSelected: boolean
  onSelect: () => void
  onDelete: () => void
  getStatusColor: (status: ContentStatus) => string
  getPlatformIcon: (platform: Platform) => string
  getTypeIcon: (type: ContentType) => string
}

const ContentCard: React.FC<ContentCardProps> = ({
  item,
  viewMode,
  isSelected,
  onSelect,
  onDelete,
  getStatusColor,
  getPlatformIcon,
  getTypeIcon,
}) => {
  const [showPreview, setShowPreview] = useState(false)

  const formatScheduledDate = (date: string) => {
    const scheduleDate = new Date(date)
    const now = new Date()
    
    if (scheduleDate < now) {
      return `Scheduled for ${format(scheduleDate, 'MMM d, yyyy h:mm a')}`
    } else {
      return `Scheduled for ${format(scheduleDate, 'MMM d, yyyy h:mm a')}`
    }
  }

  if (viewMode === 'list') {
    return (
      <div className={`card hover:shadow-md transition-shadow ${isSelected ? 'ring-2 ring-primary-500' : ''}`}>
        <div className="card-content">
          <div className="flex items-center space-x-4">
            <div className="flex items-center">
              <input
                type="checkbox"
                checked={isSelected}
                onChange={onSelect}
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
            </div>

            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-3">
                <div className="text-2xl">{getTypeIcon(item.type)}</div>
                <div className="flex-1">
                  <h3 className="text-sm font-medium text-gray-900 truncate">
                    {item.title}
                  </h3>
                  <p className="text-sm text-gray-500">Project ID: {item.projectId}</p>
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              {item.platforms.map((platform) => (
                <span key={platform} className="text-lg" title={platform}>
                  {getPlatformIcon(platform)}
                </span>
              ))}
            </div>

            <div className="flex items-center">
              <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(item.status)}`}>
                {item.status.replace('_', ' ')}
              </span>
            </div>

            <div className="text-sm text-gray-500">
              {item.scheduledAt ? (
                <div className="flex items-center">
                  <ClockIcon className="h-4 w-4 mr-1" />
                  {format(new Date(item.scheduledAt), 'MMM d, h:mm a')}
                </div>
              ) : (
                <div className="flex items-center">
                  <CalendarIcon className="h-4 w-4 mr-1" />
                  {format(new Date(item.createdAt), 'MMM d')}
                </div>
              )}
            </div>

            <Menu as="div" className="relative">
              <Menu.Button className="p-1 rounded-md hover:bg-gray-100">
                <EllipsisVerticalIcon className="h-5 w-5 text-gray-400" />
              </Menu.Button>
              <Transition
                as={Fragment}
                enter="transition ease-out duration-100"
                enterFrom="transform opacity-0 scale-95"
                enterTo="transform opacity-100 scale-100"
                leave="transition ease-in duration-75"
                leaveFrom="transform opacity-100 scale-100"
                leaveTo="transform opacity-0 scale-95"
              >
                <Menu.Items className="absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                  <Menu.Item>
                    {({ active }) => (
                      <button
                        onClick={() => setShowPreview(true)}
                        className={`flex items-center w-full text-left px-4 py-2 text-sm ${
                          active ? 'bg-gray-100 text-gray-900' : 'text-gray-700'
                        }`}
                      >
                        <EyeIcon className="h-4 w-4 mr-2" />
                        Preview
                      </button>
                    )}
                  </Menu.Item>
                  <Menu.Item>
                    {({ active }) => (
                      <button
                        className={`flex items-center w-full text-left px-4 py-2 text-sm ${
                          active ? 'bg-gray-100 text-gray-900' : 'text-gray-700'
                        }`}
                      >
                        <PencilIcon className="h-4 w-4 mr-2" />
                        Edit
                      </button>
                    )}
                  </Menu.Item>
                  <Menu.Item>
                    {({ active }) => (
                      <button
                        onClick={onDelete}
                        className={`flex items-center w-full text-left px-4 py-2 text-sm ${
                          active ? 'bg-gray-100 text-red-900' : 'text-red-700'
                        }`}
                      >
                        <TrashIcon className="h-4 w-4 mr-2" />
                        Delete
                      </button>
                    )}
                  </Menu.Item>
                </Menu.Items>
              </Transition>
            </Menu>
          </div>
        </div>
      </div>
    )
  }

  // Grid view
  return (
    <div className={`card hover:shadow-lg transition-shadow ${isSelected ? 'ring-2 ring-primary-500' : ''}`}>
      <div className="card-content">
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center space-x-3">
            <input
              type="checkbox"
              checked={isSelected}
              onChange={onSelect}
              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
            />
            <div className="text-2xl">{getTypeIcon(item.type)}</div>
            <div>
              <h3 className="font-semibold text-gray-900 truncate">{item.title}</h3>
              <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(item.status)}`}>
                {item.status.replace('_', ' ')}
              </span>
            </div>
          </div>
          
          <Menu as="div" className="relative">
            <Menu.Button className="p-1 rounded-md hover:bg-gray-100">
              <EllipsisVerticalIcon className="h-5 w-5 text-gray-400" />
            </Menu.Button>
            <Transition
              as={Fragment}
              enter="transition ease-out duration-100"
              enterFrom="transform opacity-0 scale-95"
              enterTo="transform opacity-100 scale-100"
              leave="transition ease-in duration-75"
              leaveFrom="transform opacity-100 scale-100"
              leaveTo="transform opacity-0 scale-95"
            >
              <Menu.Items className="absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                <Menu.Item>
                  {({ active }) => (
                    <button
                      onClick={() => setShowPreview(true)}
                      className={`flex items-center w-full text-left px-4 py-2 text-sm ${
                        active ? 'bg-gray-100 text-gray-900' : 'text-gray-700'
                      }`}
                    >
                      <EyeIcon className="h-4 w-4 mr-2" />
                      Preview
                    </button>
                  )}
                </Menu.Item>
                <Menu.Item>
                  {({ active }) => (
                    <button
                      className={`flex items-center w-full text-left px-4 py-2 text-sm ${
                        active ? 'bg-gray-100 text-gray-900' : 'text-gray-700'
                      }`}
                    >
                      <PencilIcon className="h-4 w-4 mr-2" />
                      Edit
                    </button>
                  )}
                </Menu.Item>
                <Menu.Item>
                  {({ active }) => (
                    <button
                      onClick={onDelete}
                      className={`flex items-center w-full text-left px-4 py-2 text-sm ${
                        active ? 'bg-gray-100 text-red-900' : 'text-red-700'
                      }`}
                    >
                      <TrashIcon className="h-4 w-4 mr-2" />
                      Delete
                    </button>
                  )}
                </Menu.Item>
              </Menu.Items>
            </Transition>
          </Menu>
        </div>

        {item.description && (
          <p className="text-gray-600 text-sm mb-4 line-clamp-2">{item.description}</p>
        )}

        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-500">Platforms:</span>
            <div className="flex items-center space-x-1">
              {item.platforms.map((platform) => (
                <span key={platform} className="text-lg" title={platform}>
                  {getPlatformIcon(platform)}
                </span>
              ))}
            </div>
          </div>

          {item.scheduledAt && (
            <div className="flex items-center text-sm text-gray-500">
              <ClockIcon className="h-4 w-4 mr-2" />
              <span>{formatScheduledDate(item.scheduledAt)}</span>
            </div>
          )}

          <div className="flex items-center text-sm text-gray-500">
            <span>Project ID: {item.projectId}</span>
          </div>
        </div>

        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="h-6 w-6 bg-gray-300 rounded-full flex items-center justify-center">
                <span className="text-xs font-medium text-gray-700">
                  {item.createdBy.firstName[0]}{item.createdBy.lastName[0]}
                </span>
              </div>
              <span className="text-sm text-gray-600">
                {item.createdBy.firstName} {item.createdBy.lastName}
              </span>
            </div>
            <span className="text-xs text-gray-500">
              {format(new Date(item.createdAt), 'MMM d')}
            </span>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ContentCard
