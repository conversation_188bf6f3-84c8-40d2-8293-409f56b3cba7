import {
  DocumentTextIcon,
  CheckCircleIcon,
  ClockIcon,
  PencilSquareIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
} from '@heroicons/react/24/outline'

interface AnalyticsOverviewData {
  totalContent: number
  publishedContent: number
  scheduledContent: number
  draftContent: number
  contentGrowth: number
  publishedGrowth: number
}

interface AnalyticsOverviewCardsProps {
  data: AnalyticsOverviewData
}

const AnalyticsOverviewCards: React.FC<AnalyticsOverviewCardsProps> = ({ data }) => {
  const cards = [
    {
      title: 'Total Content',
      value: data.totalContent,
      icon: DocumentTextIcon,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      growth: data.contentGrowth,
    },
    {
      title: 'Published',
      value: data.publishedContent,
      icon: CheckCircleIcon,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      growth: data.publishedGrowth,
    },
    {
      title: 'Scheduled',
      value: data.scheduledContent,
      icon: ClockIcon,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
    },
    {
      title: 'Drafts',
      value: data.draftContent,
      icon: PencilSquareIcon,
      color: 'text-gray-600',
      bgColor: 'bg-gray-50',
    },
  ]

  const formatGrowth = (growth: number) => {
    if (growth === 0) return null
    const isPositive = growth > 0
    const Icon = isPositive ? ArrowTrendingUpIcon : ArrowTrendingDownIcon
    const colorClass = isPositive ? 'text-green-600' : 'text-red-600'
    
    return (
      <div className={`flex items-center space-x-1 ${colorClass}`}>
        <Icon className="h-4 w-4" />
        <span className="text-sm font-medium">
          {Math.abs(growth).toFixed(1)}%
        </span>
      </div>
    )
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {cards.map((card) => (
        <div key={card.title} className="card">
          <div className="card-content">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{card.title}</p>
                <p className="text-3xl font-bold text-gray-900">{card.value}</p>
                {card.growth !== undefined && formatGrowth(card.growth)}
              </div>
              <div className={`p-3 rounded-lg ${card.bgColor}`}>
                <card.icon className={`h-6 w-6 ${card.color}`} />
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}

export default AnalyticsOverviewCards
